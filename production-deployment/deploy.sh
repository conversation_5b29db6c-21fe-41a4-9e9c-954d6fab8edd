#!/bin/bash

# Production Deployment Script for NutriFocus on AWS EC2
# This script deploys the application to production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
DOMAIN="nutrifocus.duckdns.org"
APP_DIR="/opt/nutrifocus"
BACKUP_DIR="/opt/nutrifocus/backups"
LOG_DIR="/var/log/nutrifocus"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root"
   exit 1
fi

print_status "Starting NutriFocus production deployment..."

# Create necessary directories
print_status "Creating application directories..."
mkdir -p $APP_DIR
mkdir -p $BACKUP_DIR
mkdir -p $LOG_DIR

# Update system packages
print_status "Updating system packages..."
apt update && apt upgrade -y

# Install required packages
print_status "Installing required packages..."
apt install -y docker.io docker-compose nginx certbot python3-certbot-nginx curl wget git

# Start and enable Docker
print_status "Starting Docker service..."
systemctl start docker
systemctl enable docker

# Create Docker network
print_status "Creating Docker network..."
docker network create nutrifocus-network 2>/dev/null || true

# Stop existing containers
print_status "Stopping existing containers..."
docker stop nutrifocus-frontend nutrifocus-backend nutrifocus-postgres 2>/dev/null || true
docker rm nutrifocus-frontend nutrifocus-backend nutrifocus-postgres 2>/dev/null || true

# Backup existing data
if [ -d "$APP_DIR/data" ]; then
    print_status "Creating backup of existing data..."
    tar -czf "$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S).tar.gz" -C $APP_DIR data
fi

# Create production environment files
print_status "Creating production environment files..."

# Frontend environment
cat > $APP_DIR/frontend.env << EOF
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://$DOMAIN
NEXT_PUBLIC_COOKIE_DOMAIN=$DOMAIN
EOF

# Backend environment
cat > $APP_DIR/backend.env << EOF
NODE_ENV=production
PORT=4000
POSTGRES_HOST=nutrifocus-postgres
POSTGRES_PORT=5432
POSTGRES_USER=nutrifocus_user
POSTGRES_PASSWORD=your-secure-postgres-password
POSTGRES_DB=nutrifocus_prod
FRONTEND_URL=https://$DOMAIN
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_EXPIRES_IN=2h
JWT_ACCESS_TOKEN_SECRET=your-access-token-secret
JWT_ACCESS_TOKEN_EXPIRATION_TIME=2h
JWT_REFRESH_TOKEN_SECRET=your-refresh-token-secret
JWT_REFRESH_TOKEN_EXPIRATION_TIME=24h
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_VERIFICATION_SECRET=your-email-verification-secret
ML_API_URL=http://nutrifocus-ml:8000
THROTTLE_TTL=60
THROTTLE_LIMIT=100
PASSWORD_RESET_SECRET=your-password-reset-secret
GOOGLE_AUTH_CLIENT_ID=your-google-client-id
GOOGLE_AUTH_CLIENT_SECRET=your-google-client-secret
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret
EOF

# Create Docker Compose file for production
cat > $APP_DIR/docker-compose.prod.yml << EOF
version: '3.8'

services:
  frontend:
    image: nutrifocus-frontend:latest
    container_name: nutrifocus-frontend
    env_file:
      - frontend.env
    ports:
      - "3000:3000"
    restart: unless-stopped
    networks:
      - nutrifocus-network
    volumes:
      - $LOG_DIR/frontend:/app/logs

  backend:
    image: nutrifocus-backend:latest
    container_name: nutrifocus-backend
    env_file:
      - backend.env
    ports:
      - "4000:4000"
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - nutrifocus-network
    volumes:
      - $LOG_DIR/backend:/app/logs

  postgres:
    image: postgres:15-alpine
    container_name: nutrifocus-postgres
    environment:
      - POSTGRES_USER=nutrifocus_user
      - POSTGRES_PASSWORD=your-secure-postgres-password
      - POSTGRES_DB=nutrifocus_prod
    volumes:
      - $APP_DIR/data/postgres:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - nutrifocus-network

  ml-service:
    image: nutrifocus-ml:latest
    container_name: nutrifocus-ml
    environment:
      - ENVIRONMENT=production
    ports:
      - "8000:8000"
    restart: unless-stopped
    networks:
      - nutrifocus-network

networks:
  nutrifocus-network:
    external: true
EOF

# Build Docker images
print_status "Building Docker images..."

# Build frontend
print_status "Building frontend image..."
cd /path/to/your/Frontend_client-side
docker build -t nutrifocus-frontend:latest .

# Build backend
print_status "Building backend image..."
cd /path/to/your/Backend_server-side
docker build -t nutrifocus-backend:latest .

# Build ML service
print_status "Building ML service image..."
cd /path/to/your/Machine-Learning_AI
docker build -t nutrifocus-ml:latest .

# Configure Nginx
print_status "Configuring Nginx..."
cp nginx.production.conf /etc/nginx/nginx.conf
systemctl restart nginx
systemctl enable nginx

# Start services
print_status "Starting services..."
cd $APP_DIR
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 30

# Check service health
print_status "Checking service health..."
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    print_success "Frontend is running"
else
    print_error "Frontend is not responding"
fi

if curl -f http://localhost:4000/health > /dev/null 2>&1; then
    print_success "Backend is running"
else
    print_error "Backend is not responding"
fi

# Setup SSL certificate with Let's Encrypt
print_status "Setting up SSL certificate..."
certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email <EMAIL>

# Create systemd service for auto-restart
cat > /etc/systemd/system/nutrifocus.service << EOF
[Unit]
Description=NutriFocus Application
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$APP_DIR
ExecStart=/usr/local/bin/docker-compose -f docker-compose.prod.yml up -d
ExecStop=/usr/local/bin/docker-compose -f docker-compose.prod.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable nutrifocus.service

# Create monitoring script
cat > $APP_DIR/monitor.sh << 'EOF'
#!/bin/bash

# Simple monitoring script
LOG_FILE="/var/log/nutrifocus/monitor.log"

echo "$(date): Checking services..." >> $LOG_FILE

# Check frontend
if ! curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "$(date): Frontend is down, restarting..." >> $LOG_FILE
    docker restart nutrifocus-frontend
fi

# Check backend
if ! curl -f http://localhost:4000/health > /dev/null 2>&1; then
    echo "$(date): Backend is down, restarting..." >> $LOG_FILE
    docker restart nutrifocus-backend
fi

# Check postgres
if ! docker exec nutrifocus-postgres pg_isready > /dev/null 2>&1; then
    echo "$(date): PostgreSQL is down, restarting..." >> $LOG_FILE
    docker restart nutrifocus-postgres
fi
EOF

chmod +x $APP_DIR/monitor.sh

# Add monitoring to crontab
(crontab -l 2>/dev/null; echo "*/5 * * * * $APP_DIR/monitor.sh") | crontab -

# Create backup script
cat > $APP_DIR/backup.sh << 'EOF'
#!/bin/bash

# Backup script
BACKUP_DIR="/opt/nutrifocus/backups"
DATE=$(date +%Y%m%d-%H%M%S)

# Create backup
tar -czf "$BACKUP_DIR/backup-$DATE.tar.gz" -C /opt/nutrifocus data

# Keep only last 7 backups
ls -t $BACKUP_DIR/backup-*.tar.gz | tail -n +8 | xargs -r rm
EOF

chmod +x $APP_DIR/backup.sh

# Add backup to crontab (daily at 2 AM)
(crontab -l 2>/dev/null; echo "0 2 * * * $APP_DIR/backup.sh") | crontab -

print_success "Production deployment completed successfully!"
echo ""
echo "🌐 Your application is now available at: https://$DOMAIN"
echo ""
echo "📊 Useful commands:"
echo "  - View logs: docker-compose -f $APP_DIR/docker-compose.prod.yml logs -f"
echo "  - Restart services: systemctl restart nutrifocus.service"
echo "  - Check status: systemctl status nutrifocus.service"
echo "  - Monitor logs: tail -f $LOG_DIR/*"
echo ""
echo "⚠️  Important notes:"
echo "  - Update environment variables in $APP_DIR/*.env files"
echo "  - Update Google OAuth redirect URIs to include https://$DOMAIN"
echo "  - Update Cloudinary settings if needed"
echo "  - SSL certificate will auto-renew via Let's Encrypt"
echo ""
echo "🔒 Security checklist:"
echo "  - Change default passwords in environment files"
echo "  - Configure firewall rules"
echo "  - Set up regular backups"
echo "  - Monitor application logs" 