{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "peerDependencies": {"@nestjs/common": "^9.0.0", "@nestjs/core": "^9.0.0", "@nestjs/throttler": "^5.0.0"}, "scripts": {"build": "NODE_OPTIONS='--max-old-space-size=2048' nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "NODE_ENV=production node --max-old-space-size=2048 dist/main.js", "start:dev": "nest start --watch", "start:prod": "node --max-old-space-size=2048 dist/main.js", "start:debug": "nest start --debug --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "biome:check": "pnpm biome check --write src", "test:unit": "jest --config ./test/jest-unit.config.js", "test:integration": "jest --config ./test/jest-integration.config.js", "test:users-unit": "jest users.service.spec.ts", "test:users-integration": "jest users.integration.spec.ts"}, "dependencies": {"@biomejs/biome": "^1.9.4", "@faker-js/faker": "^9.6.0", "@hapi/joi": "^17.1.1", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^3.2.0", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.0.5", "@nestjs/throttler": "^5.0.0", "@types/nodemailer": "^6.4.17", "axios": "^1.8.3", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cloudinary": "^2.6.1", "cookie-parser": "^1.4.6", "dotenv": "^16.4.7", "drizzle-kit": "^0.30.5", "drizzle-orm": "^0.40.0", "googleapis": "^148.0.0", "jsonwebtoken": "^9.0.2", "mailer": "link:@@nestjs/mailer", "multer": "^2.0.0", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.10.0", "pg": "^8.13.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.4", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/cookie-parser": "^1.4.7", "@types/express": "^5.0.0", "@types/hapi__joi": "^17.1.14", "@types/jest": "^29.5.14", "@types/joi": "^17.2.3", "@types/multer": "^1.4.12", "@types/node": "^22.13.10", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^7.0.0"}, "engines": {"node": ">=18.0.0"}}