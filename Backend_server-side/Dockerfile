# Stage 1: Build stage
FROM node:18-alpine AS builder

# Create app directory
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm

# Copy only package files to leverage Docker layer caching
COPY package.json ./

# Install dependencies
RUN pnpm install

# Copy only the source code
COPY . .

# Build the application with increased memory limit
ENV NODE_OPTIONS=--max-old-space-size=2048
RUN pnpm run build

# Stage 2: Runtime stage
FROM node:18-alpine

WORKDIR /app

# Install pnpm and production dependencies
RUN npm install -g pnpm

# Copy only the necessary artifacts from builder
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package.json ./
COPY --from=builder /app/pnpm-lock.yaml ./
COPY --from=builder /app/certs/ ./certs/

ENV NODE_ENV=production

# Install only production dependencies
RUN pnpm install --prod

# Expose the app port (can use default 4000 or from ENV)
ENV PORT=4000
EXPOSE $PORT


# Start the application
CMD ["node", "dist/src/main.js"]
