ALTER TABLE "Blogs" ADD COLUMN "featured_image" varchar(512);

-- Create a function to update the updatedAt column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- -- Apply the trigger to the Users table
CREATE TRIGGER set_updated_at_users
BEFORE UPDATE ON "Users"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- -- Apply the trigger to the Admins table
CREATE TRIGGER set_updated_at_admins
BEFORE UPDATE ON "Admins"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- -- Apply the trigger to the SuperAdmins table
CREATE TRIGGER set_updated_at_superadmins
BEFORE UPDATE ON "SuperAdmins"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Apply the trigger to the FoodLogs table
CREATE TRIGGER set_updated_at_foodlogs
BEFORE UPDATE ON "FoodLogs"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- -- Apply the trigger to the Blogs table
CREATE TRIGGER set_updated_at_blogs
BEFORE UPDATE ON "Blogs"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- -- Apply the trigger to the Feedback table
CREATE TRIGGER set_updated_at_feedback
BEFORE UPDATE ON "Feedback"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- -- Apply the trigger to the Notifications table
CREATE TRIGGER set_updated_at_notifications
BEFORE UPDATE ON "Notifications"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- -- Apply the trigger to the EthiopianFoods table
CREATE TRIGGER set_updated_at_ethiopianfoods
BEFORE UPDATE ON "EthiopianFoods"
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();