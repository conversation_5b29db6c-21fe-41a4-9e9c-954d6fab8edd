{"id": "e98e5c71-03d5-4926-a258-1a47d38d561c", "prevId": "96e979a9-e4f0-43bb-afa3-fea43bc5a498", "version": "7", "dialect": "postgresql", "tables": {"public.Admins": {"name": "Admins", "schema": "", "columns": {"admin_id": {"name": "admin_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true, "default": "'ADMIN'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'active'"}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Admins_email_unique": {"name": "Admins_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "Admins_phone_unique": {"name": "Admins_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Blogs": {"name": "Blogs", "schema": "", "columns": {"blog_id": {"name": "blog_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "author": {"name": "author", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"Blogs_author_Admins_admin_id_fk": {"name": "Blogs_author_Admins_admin_id_fk", "tableFrom": "Blogs", "tableTo": "Admins", "columnsFrom": ["author"], "columnsTo": ["admin_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.EthiopianFoods": {"name": "EthiopianFoods", "schema": "", "columns": {"food_id": {"name": "food_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "local_name": {"name": "local_name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "region": {"name": "region", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "base_ingredient": {"name": "base_ingredient", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "preparation_method": {"name": "preparation_method", "type": "text", "primaryKey": false, "notNull": false}, "calories": {"name": "calories", "type": "numeric", "primaryKey": false, "notNull": false}, "nutrients": {"name": "nutrients", "type": "jsonb", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Feedback": {"name": "<PERSON><PERSON><PERSON>", "schema": "", "columns": {"feedback_id": {"name": "feedback_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'Submitted'"}, "feedback_attachment": {"name": "feedback_attachment", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"Feedback_user_id_Users_user_id_fk": {"name": "Feedback_user_id_Users_user_id_fk", "tableFrom": "<PERSON><PERSON><PERSON>", "tableTo": "Users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.FoodLogs": {"name": "FoodLogs", "schema": "", "columns": {"log_id": {"name": "log_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "food_name": {"name": "food_name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "base_ingredient": {"name": "base_ingredient", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "preparation": {"name": "preparation", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "calories": {"name": "calories", "type": "numeric", "primaryKey": false, "notNull": false}, "nutrients": {"name": "nutrients", "type": "jsonb", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"FoodLogs_user_id_Users_user_id_fk": {"name": "FoodLogs_user_id_Users_user_id_fk", "tableFrom": "FoodLogs", "tableTo": "Users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Notifications": {"name": "Notifications", "schema": "", "columns": {"notification_id": {"name": "notification_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "recipient_id": {"name": "recipient_id", "type": "uuid", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.SuperAdmins": {"name": "SuperAdmins", "schema": "", "columns": {"super_admin_id": {"name": "super_admin_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true, "default": "'SUPERADMIN'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'active'"}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"SuperAdmins_email_unique": {"name": "SuperAdmins_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "SuperAdmins_phone_unique": {"name": "SuperAdmins_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Users": {"name": "Users", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "health_metrics": {"name": "health_metrics", "type": "jsonb", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'pending'"}, "is_email_confirmed": {"name": "is_email_confirmed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "confirmation_sent_at": {"name": "confirmation_sent_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Users_email_unique": {"name": "Users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "Users_phone_unique": {"name": "Users_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}