{"id": "8bfccccd-bb56-456b-ba7c-984354ab99eb", "prevId": "f53979af-7404-43e7-a778-9b813e5604de", "version": "7", "dialect": "postgresql", "tables": {"public.Admins": {"name": "Admins", "schema": "", "columns": {"admin_id": {"name": "admin_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true, "default": "'ADMIN'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'active'"}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Admins_email_unique": {"name": "Admins_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "Admins_phone_unique": {"name": "Admins_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Blogs": {"name": "Blogs", "schema": "", "columns": {"blog_id": {"name": "blog_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "featured_image": {"name": "featured_image", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "author": {"name": "author", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"Blogs_author_Admins_admin_id_fk": {"name": "Blogs_author_Admins_admin_id_fk", "tableFrom": "Blogs", "tableTo": "Admins", "columnsFrom": ["author"], "columnsTo": ["admin_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.EthiopianFoods": {"name": "EthiopianFoods", "schema": "", "columns": {"food_id": {"name": "food_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "local_name": {"name": "local_name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "region": {"name": "region", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "base_ingredient": {"name": "base_ingredient", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "preparation_method": {"name": "preparation_method", "type": "text", "primaryKey": false, "notNull": false}, "calories": {"name": "calories", "type": "numeric", "primaryKey": false, "notNull": false}, "nutrients": {"name": "nutrients", "type": "jsonb", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Feedback": {"name": "<PERSON><PERSON><PERSON>", "schema": "", "columns": {"feedback_id": {"name": "feedback_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'Submitted'"}, "feedback_attachment": {"name": "feedback_attachment", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"Feedback_user_id_Users_user_id_fk": {"name": "Feedback_user_id_Users_user_id_fk", "tableFrom": "<PERSON><PERSON><PERSON>", "tableTo": "Users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.FoodLogs": {"name": "FoodLogs", "schema": "", "columns": {"log_id": {"name": "log_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "food_name": {"name": "food_name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "base_ingredient": {"name": "base_ingredient", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "preparation": {"name": "preparation", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "calories": {"name": "calories", "type": "numeric", "primaryKey": false, "notNull": false}, "nutrients": {"name": "nutrients", "type": "jsonb", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"FoodLogs_user_id_Users_user_id_fk": {"name": "FoodLogs_user_id_Users_user_id_fk", "tableFrom": "FoodLogs", "tableTo": "Users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Notifications": {"name": "Notifications", "schema": "", "columns": {"notification_id": {"name": "notification_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "recipient_id": {"name": "recipient_id", "type": "uuid", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "from": {"name": "from", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "'System'"}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Recommendations": {"name": "Recommendations", "schema": "", "columns": {"recommendation_id": {"name": "recommendation_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "health_metric_id": {"name": "health_metric_id", "type": "uuid", "primaryKey": false, "notNull": true}, "predictions": {"name": "predictions", "type": "jsonb", "primaryKey": false, "notNull": true}, "nutrition_targets": {"name": "nutrition_targets", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"Recommendations_user_id_Users_user_id_fk": {"name": "Recommendations_user_id_Users_user_id_fk", "tableFrom": "Recommendations", "tableTo": "Users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}, "Recommendations_health_metric_id_UserHealthMetrics_health_metric_id_fk": {"name": "Recommendations_health_metric_id_UserHealthMetrics_health_metric_id_fk", "tableFrom": "Recommendations", "tableTo": "UserHealthMetrics", "columnsFrom": ["health_metric_id"], "columnsTo": ["health_metric_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.SuperAdmins": {"name": "SuperAdmins", "schema": "", "columns": {"super_admin_id": {"name": "super_admin_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true, "default": "'SUPERADMIN'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'active'"}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"SuperAdmins_email_unique": {"name": "SuperAdmins_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "SuperAdmins_phone_unique": {"name": "SuperAdmins_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.UserHealthMetrics": {"name": "UserHealthMetrics", "schema": "", "columns": {"health_metric_id": {"name": "health_metric_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "age": {"name": "age", "type": "numeric", "primaryKey": false, "notNull": true}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": true}, "weight": {"name": "weight", "type": "numeric", "primaryKey": false, "notNull": true}, "activity_level": {"name": "activity_level", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "dietary_preference": {"name": "dietary_preference", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "weight_goal": {"name": "weight_goal", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "health_issues": {"name": "health_issues", "type": "jsonb", "primaryKey": false, "notNull": false}, "fasting": {"name": "fasting", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'No'"}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"UserHealthMetrics_user_id_Users_user_id_fk": {"name": "UserHealthMetrics_user_id_Users_user_id_fk", "tableFrom": "UserHealthMetrics", "tableTo": "Users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Users": {"name": "Users", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'pending'"}, "is_email_confirmed": {"name": "is_email_confirmed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "confirmation_sent_at": {"name": "confirmation_sent_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "is_registered_with_google": {"name": "is_registered_with_google", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "profile_picture": {"name": "profile_picture", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_token": {"name": "reset_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_token_expiry": {"name": "reset_token_expiry", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Users_email_unique": {"name": "Users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "Users_phone_unique": {"name": "Users_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.WeightHistory": {"name": "WeightHistory", "schema": "", "columns": {"entry_id": {"name": "entry_id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "weight": {"name": "weight", "type": "numeric", "primaryKey": false, "notNull": true}, "recorded_at": {"name": "recorded_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"WeightHistory_user_id_Users_user_id_fk": {"name": "WeightHistory_user_id_Users_user_id_fk", "tableFrom": "WeightHistory", "tableTo": "Users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}