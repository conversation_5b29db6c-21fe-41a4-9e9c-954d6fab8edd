CREATE TABLE "Admins" (
	"admin_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(256) NOT NULL,
	"email" varchar(256) NOT NULL,
	"password" varchar NOT NULL,
	"phone" varchar(20),
	"role" varchar(256) DEFAULT 'ADMIN' NOT NULL,
	"status" varchar(50) DEFAULT 'active',
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "Admins_email_unique" UNIQUE("email"),
	CONSTRAINT "Admins_phone_unique" UNIQUE("phone")
);
--> statement-breakpoint
CREATE TABLE "SuperAdmins" (
	"super_admin_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(256) NOT NULL,
	"email" varchar(256) NOT NULL,
	"password" varchar NOT NULL,
	"phone" varchar(20),
	"role" varchar(256) DEFAULT 'SUPERADMIN' NOT NULL,
	"status" varchar(50) DEFAULT 'active',
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "SuperAdmins_email_unique" UNIQUE("email"),
	CONSTRAINT "SuperAdmins_phone_unique" UNIQUE("phone")
);
--> statement-breakpoint
CREATE TABLE "Users" (
	"user_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(256) NOT NULL,
	"email" varchar(256) NOT NULL,
	"password" varchar NOT NULL,
	"health_metrics" jsonb,
	"phone" varchar(256),
	"status" varchar DEFAULT 'pending',
	"created_at" timestamp DEFAULT now(),
	CONSTRAINT "Users_email_unique" UNIQUE("email"),
	CONSTRAINT "Users_phone_unique" UNIQUE("phone")
);
