CREATE TABLE "UserHealthMetrics" (
	"health_metric_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"age" numeric NOT NULL,
	"gender" varchar(10) NOT NULL,
	"height" numeric NOT NULL,
	"weight" numeric NOT NULL,
	"activity_level" varchar(50) NOT NULL,
	"dietary_preference" varchar(50) NOT NULL,
	"weight_goal" varchar(50) NOT NULL,
	"health_issues" text,
	"fasting" boolean NOT NULL,
	"location" varchar(255) NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "UserHealthMetrics" ADD CONSTRAINT "UserHealthMetrics_user_id_Users_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."Users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "Users" DROP COLUMN "health_metrics";