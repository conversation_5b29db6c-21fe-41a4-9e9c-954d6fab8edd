CREATE TABLE "Recommendations" (
	"recommendation_id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"health_metric_id" uuid NOT NULL,
	"predictions" jsonb NOT NULL,
	"nutrition_targets" jsonb NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "Recommendations" ADD CONSTRAINT "Recommendations_user_id_Users_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."Users"("user_id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "Recommendations" ADD CONSTRAINT "Recommendations_health_metric_id_UserHealthMetrics_health_metric_id_fk" FOREIGN KEY ("health_metric_id") REFERENCES "public"."UserHealthMetrics"("health_metric_id") ON DELETE no action ON UPDATE no action;