name: Backend CI/CD Pipeline

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_DB: test_db
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18.x'
          cache: 'pnpm'

      - name: Install dependencies
        run: |
          pnpm install
          pnpm install -D @typescript-eslint/eslint-plugin @typescript-eslint/parser typescript-eslint

      - name: Run linting
        run: pnpm lint

      # Temporarily skip tests until test setup is complete
      # - name: Run tests
      #   run: |
      #     pnpm test:unit
      #     pnpm test:integration
      #     pnpm test:e2e

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18.x'
          cache: 'pnpm'

      - name: Install dependencies
        run: |
          pnpm install
          pnpm install -D @typescript-eslint/eslint-plugin @typescript-eslint/parser typescript-eslint

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push Docker image to ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: backend
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_ENV

      - name: Deploy to EC2
        env:
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_SSH_KEY: ${{ secrets.EC2_SSH_KEY }}
        run: |
          echo "$EC2_SSH_KEY" > key.pem
          chmod 400 key.pem
          ssh -o StrictHostKeyChecking=no -i key.pem $EC2_USER@$EC2_HOST << 'EOF'
            # Clean up disk space
            sudo apt-get clean
            sudo apt-get autoremove -y
            sudo rm -rf /var/lib/apt/lists/*
            sudo docker system prune -af --volumes

            # Install AWS CLI using apt (more reliable than zip method)
            sudo apt-get update
            sudo apt-get install -y awscli

            # Configure AWS credentials non-interactively
            mkdir -p ~/.aws
            printf "[default]\naws_access_key_id=%s\naws_secret_access_key=%s\n" "${{ secrets.AWS_ACCESS_KEY_ID }}" "${{ secrets.AWS_SECRET_ACCESS_KEY }}" > ~/.aws/credentials
            printf "[default]\nregion=us-east-1\n" > ~/.aws/config

            # Pull latest code
            cd ~/Backend_server-side
            git fetch origin
            git reset --hard origin/main

            # Docker operations with sudo
            sudo docker system prune -a -f --volumes
            aws ecr get-login-password --region us-east-1 | sudo docker login --username AWS --password-stdin ${{ steps.login-ecr.outputs.registry }}
            sudo docker pull ${{ env.image }}
            
            # Stop and remove existing containers more safely
            sudo docker ps -a | grep backend_server-side_api_1 | awk '{print $1}' | xargs -r sudo docker stop
            sudo docker ps -a | grep backend_server-side_api_1 | awk '{print $1}' | xargs -r sudo docker rm
            
            # Start new container with proper environment setup
            sudo docker run -d \
              --name backend_server-side_api_1 \
              -p 4000:4000 \
              -v ~/Backend_server-side:/app \
              -w /app \
              --user $(id -u):$(id -g) \
              ${{ env.image }} \
              sh -c "npm install && npm run build && npm start"

            # Wait for container to start and check logs
            sleep 10
            sudo docker logs backend_server-side_api_1
          EOF
          rm key.pem