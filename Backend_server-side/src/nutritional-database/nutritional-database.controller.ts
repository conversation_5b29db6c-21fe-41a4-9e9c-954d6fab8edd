import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards } from '@nestjs/common';
import { NutritionalDatabaseService } from './nutritional-database.service';
import { CreateEthiopianFoodDto } from './dto/create-ethiopian-food.dto';
import { UpdateEthiopianFoodDto } from './dto/update-ethiopian-food.dto';
import { FilterEthiopianFoodDto } from './dto/filter-ethiopian-food.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Roles } from '../common/decorators/role.decorator';
import { Role } from '../common/enums/permissions.enums';
import { SkipThrottle } from '@nestjs/throttler/dist';

@SkipThrottle({ default: true })
@ApiTags('nutritional-database')
@Controller('nutritional-database')
export class NutritionalDatabaseController {
  constructor(private readonly nutritionalDatabaseService: NutritionalDatabaseService) {}

  @Post()
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  @ApiOperation({ summary: 'Create a new Ethiopian food entry' })
  @ApiResponse({ status: 201, description: 'The food has been successfully created.' })
  create(@Body() createEthiopianFoodDto: CreateEthiopianFoodDto) {
    return this.nutritionalDatabaseService.create(createEthiopianFoodDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all Ethiopian foods with filtering options' })
  @ApiResponse({ status: 200, description: 'Return all Ethiopian foods that match the filters.' })
  findAll(@Query() filterDto: FilterEthiopianFoodDto) {
    return this.nutritionalDatabaseService.findAll(filterDto);
  }

  @Get('categories')
  @ApiOperation({ summary: 'Get all food categories' })
  @ApiResponse({ status: 200, description: 'Return all food categories.' })
  getCategories() {
    return this.nutritionalDatabaseService.getCategories();
  }

  @Get('regions')
  @ApiOperation({ summary: 'Get all food regions' })
  @ApiResponse({ status: 200, description: 'Return all food regions.' })
  getRegions() {
    return this.nutritionalDatabaseService.getRegions();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific Ethiopian food by ID' })
  @ApiResponse({ status: 200, description: 'Return the food.' })
  @ApiResponse({ status: 404, description: 'Food not found.' })
  findOne(@Param('id') id: string) {
    return this.nutritionalDatabaseService.findOne(id);
  }

  @Patch(':id')
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  @ApiOperation({ summary: 'Update an Ethiopian food entry' })
  @ApiResponse({ status: 200, description: 'The food has been successfully updated.' })
  update(@Param('id') id: string, @Body() updateEthiopianFoodDto: UpdateEthiopianFoodDto) {
    return this.nutritionalDatabaseService.update(id, updateEthiopianFoodDto);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  @ApiOperation({ summary: 'Delete an Ethiopian food entry' })
  @ApiResponse({ status: 200, description: 'The food has been successfully deleted.' })
  remove(@Param('id') id: string) {
    return this.nutritionalDatabaseService.remove(id);
  }
}