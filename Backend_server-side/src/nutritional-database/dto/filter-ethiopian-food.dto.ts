import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsN<PERSON>ber, IsInt, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class FilterEthiopianFoodDto {
  @ApiPropertyOptional({ description: 'Search by food name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Filter by food category' })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ description: 'Filter by region' })
  @IsOptional()
  @IsString()
  region?: string;

  @ApiPropertyOptional({ description: 'Page number for pagination', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Number of items per page', default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Minimum calories' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minCalories?: number;

  @ApiPropertyOptional({ description: 'Maximum calories' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxCalories?: number;
}
