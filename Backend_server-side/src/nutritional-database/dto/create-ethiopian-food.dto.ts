import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber, IsObject } from 'class-validator';

export class CreateEthiopianFoodDto {
  @ApiProperty({ description: 'Name of the Ethiopian food' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Local name in Amharic or other Ethiopian language', required: false })
  @IsOptional()
  @IsString()
  localName?: string;

  @ApiProperty({ description: 'Description of the food' })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({ description: 'Food category (e.g., Injera-based, Stew, Breakfast)', required: false })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({ description: 'Region of Ethiopia where the food is common', required: false })
  @IsOptional()
  @IsString()
  region?: string;

  @ApiProperty({ description: 'Primary ingredient of the food', required: false })
  @IsOptional()
  @IsString()
  baseIngredient?: string;

  @ApiProperty({ description: 'Method of preparation', required: false })
  @IsOptional()
  @IsString()
  preparationMethod?: string;

  @ApiProperty({ description: 'Caloric value per serving' })
  @IsOptional()
  @IsNumber()
  calories?: number;

  @ApiProperty({
    description: 'Nutritional information',
    example: {
      protein: 10.5,
      fat: 5.2,
      carbohydrates: 65.3,
      fiber: 12.1,
      ash: 2.3,
      calcium: 120,
      zinc: 3.2,
      copper: 0.8,
      sodium: 15,
      thiamine: 0.4,
      riboflavin: 0.3,
      ascorbicAcid: 5
    }
  })
  @IsOptional()
  @IsObject()
  nutrients?: {
    protein: number;
    fat: number;
    carbohydrates: number;
    fiber: number;
    ash: number;
    calcium: number;
    zinc: number;
    copper: number;
    sodium: number;
    thiamine: number;
    riboflavin: number;
    ascorbicAcid: number;
  };

  @ApiProperty({ description: 'URL to an image of the food', required: false })
  @IsOptional()
  @IsString()
  image?: string;
}
