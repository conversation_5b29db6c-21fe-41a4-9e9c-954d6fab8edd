import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsN<PERSON>ber, Min, Max } from 'class-validator';

export class UpdateEthiopianFoodDto {
  @ApiProperty({ description: 'Name of the Ethiopian food', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Category of the Ethiopian food', required: false })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({ description: 'Region where the food is popular', required: false })
  @IsOptional()
  @IsString()
  region?: string;

  @ApiProperty({ description: 'Base ingredient of the food', required: false })
  @IsOptional()
  @IsString()
  baseIngredient?: string;

  @ApiProperty({ description: 'Calories in the food', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  calories?: number;

  @ApiProperty({ description: 'Protein content in grams', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  protein?: number;

  @ApiProperty({ description: 'Fat content in grams', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  fat?: number;

  @ApiProperty({ description: 'Carbohydrate content in grams', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  carbohydrates?: number;
}