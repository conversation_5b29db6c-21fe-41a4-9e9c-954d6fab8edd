import { Injectable } from '@nestjs/common';
import { DrizzleService } from '../database/drizzle.service';
import { CreateEthiopianFoodDto } from './dto/create-ethiopian-food.dto';
import { UpdateEthiopianFoodDto } from './dto/update-ethiopian-food.dto';
import { FilterEthiopianFoodDto } from './dto/filter-ethiopian-food.dto';
import { ethiopianFoods } from '../database/database-schema';
import { eq, like, and, gte, lte, sql, gt, lt } from 'drizzle-orm';

@Injectable()
export class NutritionalDatabaseService {
  constructor(private drizzle: DrizzleService) {}

  async create(createEthiopianFoodDto: CreateEthiopianFoodDto) {
    const result = await this.drizzle.db.insert(ethiopianFoods).values(createEthiopianFoodDto).returning();
    return result[0];
  }

  async findAll(filterDto: FilterEthiopianFoodDto) {
    const { 
      name, 
      category, 
      region, 
      minCalories, 
      maxCalories,
      page = 1, 
      limit = 10 
    } = filterDto;
    
    const offset = (page - 1) * limit;
    const conditions = [];
    
    if (name) {
      conditions.push(like(ethiopianFoods.name, `%${name}%`));
    }
    
    if (category) {
      conditions.push(eq(ethiopianFoods.category, category));
    }
    
    if (region) {
      conditions.push(eq(ethiopianFoods.region, region));
    }
    
    if (minCalories !== undefined) {
      conditions.push(gte(ethiopianFoods.calories, String(maxCalories)));
    }
    
    if (maxCalories !== undefined) {
      conditions.push(lte(ethiopianFoods.calories, String(maxCalories)));
    }
    
    // Execute query with conditions
    const query = this.drizzle.db.select({
      foodId: ethiopianFoods.foodId,
      name: ethiopianFoods.name,
      localName: ethiopianFoods.localName,
      description: ethiopianFoods.description,
      category: ethiopianFoods.category,
      region: ethiopianFoods.region,
      baseIngredient: ethiopianFoods.baseIngredient,
      preparationMethod: ethiopianFoods.preparationMethod,
      calories: ethiopianFoods.calories,
      nutrients: ethiopianFoods.nutrients,
      servingSize: ethiopianFoods.servingSize,
      culturalSignificance: ethiopianFoods.culturalSignificance,
      healthBenefits: ethiopianFoods.healthBenefits,
      nutritionalContext: ethiopianFoods.nutritionalContext,
      highlights: ethiopianFoods.highlights,
      image: ethiopianFoods.image,
    }).from(ethiopianFoods);
    
    // Add conditions if any
    if (conditions.length > 0) {
      query.where(and(...conditions));
    }
    
    // Add pagination
    query.limit(limit).offset(offset);
    
    // Execute query and count total
    const [foods, countResult] = await Promise.all([
      query,
      this.drizzle.db.select({ count: sql`count(*)` }).from(ethiopianFoods)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
    ]);
    
    const total = Number(countResult[0].count);
    
    return {
      data: foods,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  async findOne(id: string) {
    const result = await this.drizzle.db.select().from(ethiopianFoods).where(eq(ethiopianFoods.foodId, id));
    return result[0] || null;
  }

  async update(id: string, updateEthiopianFoodDto: UpdateEthiopianFoodDto) {
    const result = await this.drizzle.db
      .update(ethiopianFoods)
      .set({
        ...updateEthiopianFoodDto,
      })
      .where(eq(ethiopianFoods.foodId, id))
      .returning();

    return result[0] || null;
  }

  async remove(id: string) {
    const result = await this.drizzle.db
      .delete(ethiopianFoods)
      .where(eq(ethiopianFoods.foodId, id))
      .returning();
    
    return result[0] || null;
  }

  async getCategories() {
    const result = await this.drizzle.db
      .select({ category: ethiopianFoods.category })
      .from(ethiopianFoods)
      .groupBy(ethiopianFoods.category);
    
    return result.map(item => item.category).filter(Boolean);
  }

  async getRegions() {
    const result = await this.drizzle.db
      .select({ region: ethiopianFoods.region })
      .from(ethiopianFoods)
      .groupBy(ethiopianFoods.region);
    
    return result.map(item => item.region).filter(Boolean);
  }
}
