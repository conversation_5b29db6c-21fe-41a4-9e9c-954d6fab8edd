import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Post,
  Request,
  Response,
  UseInterceptors,
} from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'
import { GoogleAuthenticationService } from './googleAuth.service'
import TokenVerificationDto from './tokenValidatation'
import { Public } from '../common/decorators/auth.decorators'
import { cookiePresets, setCookie } from '../common/utils/cookie.utils'

@Controller('google')
@ApiTags('googleAuth')
@UseInterceptors(ClassSerializerInterceptor)
export class GoogleAuthenticationController {
  constructor(
    private readonly googleAuthenticationService: GoogleAuthenticationService,
  ) {}

  @Post('signin')
  @Public()
  async authenticate(
    @Body() tokenData: TokenVerificationDto,
    @Response({ passthrough: true }) res,
    @Request() req,
  ) {
    const { accessToken, refreshToken, user } =
      await this.googleAuthenticationService.authenticate(
        tokenData.token, 'signin',
      )

    setCookie(res, 'refresh_token', refreshToken, cookiePresets.refreshToken);
    setCookie(res, 'access_token', accessToken, cookiePresets.accessToken);
    setCookie(res, 'user_role', 'USER', cookiePresets.userRole);

    console.log('login with google')

    const redirectUrl = '/userDashboard'

    return { accessToken, refreshToken, redirectUrl, userRole: 'USER' }
  }

  @Post('signup')
  @Public()
  async signup(
    @Body() tokenData: TokenVerificationDto,
    @Response({ passthrough: true }) res,
    @Request() req,
  ) {
    const { accessToken, refreshToken, user } =
      await this.googleAuthenticationService.authenticate(
        tokenData.token, 'signup',
      )

    setCookie(res, 'refresh_token', refreshToken, cookiePresets.refreshToken);
    setCookie(res, 'access_token', accessToken, cookiePresets.accessToken);
    setCookie(res, 'user_role', 'USER', cookiePresets.userRole);

    console.log('Signup with google ')

    const redirectUrl = '/userDashboard'
    return { accessToken, refreshToken, redirectUrl, userRole: 'USER' }
  }
}
