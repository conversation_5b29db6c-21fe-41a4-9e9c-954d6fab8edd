import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsUUID,
  IsEnum,
  IsOptional,
} from 'class-validator';
import {
  NotificationCategory,
  NotificationType,
} from '../../common/enums/permissions.enums';

export class CreateNotificationDto {
  @ApiProperty({ description: 'ID of the recipient' })
  @IsNotEmpty()
  @IsUUID()
  recipientId: string;

  @ApiProperty({ description: 'Notification message' })
  @IsNotEmpty()
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Type of notification',
    enum: NotificationType,
  })
  @IsNotEmpty()
  @IsEnum(NotificationType)
  type: NotificationType;

  @ApiProperty({ description: 'Who the notification is from' })
  @IsOptional()
  @IsString()
  from?: string;

  @ApiProperty({ description: 'Category of the notification', required: false })
  @IsOptional()
  @IsEnum(NotificationCategory)
  category?: NotificationCategory;
}
