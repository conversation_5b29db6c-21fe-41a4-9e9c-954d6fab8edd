import { Injectable, NotFoundException } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DrizzleService } from '../database/drizzle.service';
import EmailService from '../email/email.service';
import { NotificationCategory, NotificationType } from '../common/enums/permissions.enums';
import { eq, and, lt, sql, desc } from 'drizzle-orm';
import { users, userHealthMetrics, weightHistory, notifications } from '../database/database-schema';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { GetNotificationsDto } from './dto/get-notifications.dto';

@Injectable()
export class NotificationService {
  constructor(
    private drizzle: DrizzleService, 
    private emailService: EmailService
  ) {}

  async create(createNotificationDto: CreateNotificationDto) {
    const result = await this.drizzle.db
      .insert(notifications)
      .values({
        ...createNotificationDto,
      })
      .returning();

    return result[0];
  }

  async findAll(
    recipientId?: string,
    queryDto?: GetNotificationsDto,
  ) {
    try {
      const { type, category, page = 1, limit = 10, isRead } = queryDto || {};
      const offset = (page - 1) * limit;

      // Log the parameters for debugging
      console.log('Processing notification query with params:', { 
        recipientId, type, category, page, limit, 
        isRead, 
        isReadType: typeof isRead 
      });

      const conditions = [eq(notifications.recipientId, recipientId || null)];

      if (type) {
        conditions.push(eq(notifications.type, type));
      }
      
      if (category && category !== 'all') {
        conditions.push(eq(notifications.category, category));
      }
      
      // Only add isRead condition if it's explicitly defined (true or false)
      if (isRead !== undefined) {
        console.log(`Adding isRead condition: ${isRead} (${typeof isRead})`);
        conditions.push(eq(notifications.isRead, isRead));
      }

      const totalCount = await this.drizzle.db
        .select({ count: sql`count(*)` })
        .from(notifications)
        .where(and(...conditions));

      const results = await this.drizzle.db
        .select()
        .from(notifications)
        .where(and(...conditions))
        .orderBy(desc(notifications.timestamp))
        .limit(limit)
        .offset(offset);
      
      return {
        data: results,
        meta: {
          total: Number(totalCount[0].count || 0),
          page,
          limit,
          totalPages: Math.ceil(Number(totalCount[0].count || 0) / limit),
        },
      };
    } catch (error) {
      console.error('Error in findAll notifications:', error);
      throw error;
    }
  }

  async markAsRead(notificationId: string, recipientId: string) {
    const [notification] = await this.drizzle.db
      .select()
      .from(notifications)
      .where(
        and(
          eq(notifications.notificationId, notificationId),
          eq(notifications.recipientId, recipientId),
        ),
      );

    if (!notification) {
      throw new NotFoundException(`Notification not found`);
    }

    const updatedNotification = await this.drizzle.db.execute(
      sql`UPDATE "Notifications" SET "is_read" = true WHERE "notification_id" = ${notificationId}`,
    );

    return updatedNotification;
  }

  async markAllAsRead(recipientId: string) {
    await this.drizzle.db.execute(
      sql`
      UPDATE "Notifications" SET "is_read" = true WHERE "recipient_id" = ${recipientId}`,
    );

    return { message: 'All notifications marked as read' };
  }


  async sendNotificationForAdmins(message: string) {
    const admins = await this.drizzle.db.query.admins.findMany();
    for (const admin of admins) {
      await this.create({
      recipientId: String(admin.adminId),
       message: message,
        type: NotificationType.SYSTEM_ALERT,
        from: "System",
      });
    }
  }

  async weightUpdateReminder() {
    const users = await this.drizzle.db.query.users.findMany();
    for (const user of users) {
      await this.create({
        recipientId: String(user.userId),
        message: 'Please update your weight',
        type: NotificationType.SYSTEM_ALERT,
        from: "System",
      });
    }
  }


 
  async getNotificationCounts(recipientId: string) {
    const allCount = await this.drizzle.db
      .select({ count: sql`count(*)` })
      .from(notifications)
      .where(eq(notifications.recipientId, recipientId));
  
    const unreadCount = await this.drizzle.db
      .select({ count: sql`count(*)` })
      .from(notifications)
      .where(
        and(
          eq(notifications.recipientId, recipientId),
          eq(notifications.isRead, false),
        ),
      );
  
    const readCount = await this.drizzle.db
      .select({ count: sql`count(*)` })
      .from(notifications)
      .where(
        and(
          eq(notifications.recipientId, recipientId),
          eq(notifications.isRead, true),
        ),
      );
  
    return {
      all: Number(allCount[0].count),
      unread: Number(unreadCount[0].count),
      read: Number(readCount[0].count),
    };
  }

  async getUnreadCount(recipientId: string) {
    const result = await this.drizzle.db
      .select({ count: sql`count(*)` })
      .from(notifications)
      .where(
        and(
          eq(notifications.recipientId, recipientId),
          eq(notifications.isRead, false),
        ),
      );

    return { unreadCount: Number(result[0].count) };
  }

  async deleteNotification(notificationId: string, recipientId: string) {
    const [notification] = await this.drizzle.db
      .select()
      .from(notifications)
      .where(
        and(
          eq(notifications.notificationId, notificationId),
          eq(notifications.recipientId, recipientId),
        ),
      );

    if (!notification) {
      throw new NotFoundException(`Notification not found`);
    }

    await this.drizzle.db
      .delete(notifications)
      .where(eq(notifications.notificationId, notificationId));

    return { message: 'Notification deleted successfully' };
  }

  async notifyUser(userId: string, message: string, type: NotificationType, from: string = "System") {
    return this.create({
      recipientId: userId,
      message,
      type,
      from,
    });
  }

  async notifyAllUsers(message: string, type: NotificationType, from: string = "System") {
    const users = await this.drizzle.db.query.users.findMany();
    for (const user of users) {
      await this.create({
        recipientId: String(user.userId),
        message,
        type,
        from,
      });
    }
    return { message: 'Notifications sent to all users' };
  }

  // Add a method that handles both notification types
  async sendCriticalNotification(userId: string, message: string, type: NotificationType) {
    // Get user email
    const user = await this.drizzle.db.query.users.findFirst({
      where: eq(users.userId, userId),
    });
    
    if (!user || !user.email) {
      throw new NotFoundException('User email not found');
    }
    
    // Create in-app notification
    await this.create({
      recipientId: userId,
      message,
      type,
      from: "System",
      category: NotificationCategory.CRITICAL
    });
    
    // Send email notification using the EmailService
    await this.emailService.sendCriticalNotificationEmail(
      user.email,
      'Important Health Update - NutriGeo',
      message
    );
    
    return { message: 'Critical notification sent via app and email' };
  }

  // Run daily at 10:00 AM
  @Cron(CronExpression.EVERY_DAY_AT_10AM)
  async checkWeightUpdateReminders() {
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
    
    // Find users who haven't updated weight in 3+ days
    const usersNeedingReminder = await this.drizzle.db
      .select({
        userId: users.userId,
        email: users.email,
        lastWeightDate: weightHistory.recordedAt
      })
      .from(users)
      .leftJoin(
        weightHistory,
        eq(users.userId, weightHistory.userId)
      )
      .where(
        and(
          eq(users.status, "active"),
          lt(weightHistory.recordedAt, threeDaysAgo)
        )
      );
    
    // Send notifications to these users
    for (const user of usersNeedingReminder) {
      // In-app notification
      await this.create({
        recipientId: user.userId,
        message: 'Please update your weight - it\'s been 3+ days since your last update',
        type: NotificationType.SYSTEM_ALERT,
        from: "System",
        category: NotificationCategory.REMINDER
      });
      
      // Email for users who haven't updated in 7+ days (more critical)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      
      if (user.lastWeightDate < sevenDaysAgo && user.email) {
        // Create in-app notification
        await this.create({
          recipientId: user.userId,
          message: 'Important: Your weight hasn\'t been updated in over a week',
          type: NotificationType.SYSTEM_ALERT,
          from: "System",
          category: NotificationCategory.CRITICAL
        });
        
        // Send email using the EmailService
        await this.emailService.sendWeightUpdateReminder(user.email, 7);
      }
    }
  }
  
  // Run every Monday at 9:00 AM
  @Cron('0 9 * * 1')
  async sendWeeklyHealthSummary() {
    // Implementation for weekly summary notifications
  }
}