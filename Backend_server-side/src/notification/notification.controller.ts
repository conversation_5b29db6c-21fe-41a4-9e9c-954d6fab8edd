import {
  Controller,
  Get,
  Post,
  Param,
  Delete,
  Request,
  Query,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { NotificationService } from './notification.service';
import { GetNotificationsDto } from './dto/get-notifications.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { SkipThrottle } from '@nestjs/throttler/dist';

@ApiTags('notifications')
@Controller('notifications')
@SkipThrottle({ default: true })
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get()
  @ApiOperation({ summary: 'Get all notifications for the authenticated user' })
  @ApiResponse({ status: 200, description: 'Returns all notifications.' })
  async findAll(@Request() req, @Query() queryDto: GetNotificationsDto) {
    try {
      // Make sure req.user exists and has an id
      if (!req.user || !req.user.id) {
        throw new HttpException(
          'User not authenticated',
          HttpStatus.UNAUTHORIZED,
        );
      }
      return await this.notificationService.findAll(req.user.id, queryDto);
    } catch (error) {
      console.error('Error in findAll notifications:', error);
      throw new HttpException(
        error.message || 'Failed to fetch notifications',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('counts')
  @ApiOperation({
    summary: 'Get counts of all, unread, and read notifications',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns counts of all, unread, and read notifications.',
  })
  getNotificationCounts(@Request() req) {
    return this.notificationService.getNotificationCounts(req.user.id);
  }

  @Get('unread-count')
  @ApiOperation({ summary: 'Get count of unread notifications' })
  @ApiResponse({
    status: 200,
    description: 'Returns unread notifications count.',
  })
  getUnreadCount(@Request() req) {
    return this.notificationService.getUnreadCount(req.user.id);
  }

  @Post(':id/mark-read')
  @ApiOperation({ summary: 'Mark a notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read.' })
  markAsRead(@Param('id') id: string, @Request() req) {
    return this.notificationService.markAsRead(id, req.user.id);
  }

  @Post('mark-all-read')
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({
    status: 200,
    description: 'All notifications marked as read.',
  })
  markAllAsRead(@Request() req) {
    return this.notificationService.markAllAsRead(req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a notification' })
  @ApiResponse({
    status: 200,
    description: 'Notification deleted successfully.',
  })
  delete(@Param('id') id: string, @Request() req) {
    return this.notificationService.deleteNotification(id, req.user.id);
  }
}
