import { Injectable, NotFoundException } from '@nestjs/common';
import { MLApiService } from './ml-api.service';
import { eq, and, gte, lte, desc, sql, count, lt } from 'drizzle-orm';
import {
  GetRecommendationHistoryDto,
  MLResponseDto,
} from './dto/get-recommendation.dto';
import { DrizzleService } from '../database/drizzle.service';
import {
  userHealthMetrics,
  users,
  recommendations,
} from '../database/database-schema';
import { GetHealthMetricsDto } from '../health-metrics/dto/get-health-mertics.dto';
import { NotificationService } from '../notification/notification.service';
import { NotificationType } from '../common/enums/permissions.enums';

// Define interfaces
interface Nutrition {
  calories: number;
  protein?: number;
  fat?: number;
  carbohydrates?: number;
  fiber?: number;
  sodium?: number;
}

interface Meal {
  local_name: string;
  serving_nutrition: Nutrition;
}

interface DailyPlan {
  Breakfast: Meal[];
  Lunch: Meal[];
  Dinner: Meal[];
  Snack: Meal[];
}

@Injectable()
export class RecommendationService {
  constructor(
    private readonly mlApiService: MLApiService,
    private readonly drizzle: DrizzleService,
    private readonly notificationService: NotificationService,
  ) {}

  // Helper method to extract meal data
  private extractMeal(mealArray: any[]): Meal[] {
    if (!mealArray?.length) return [];
    return [{
      local_name: mealArray[0].local_name || mealArray[0].food || 'Unknown Food',
      serving_nutrition: {
        calories: mealArray[0].serving_nutrition?.calories || 200,
      },
    }];
  }

  async getTodaysPlan(userId: string): Promise<any> {
    const today = new Date();

    try {
      const latestRecommendation = await this.getLatestRecommendation(userId);
      if (!latestRecommendation) {
        return { Breakfast: [], Lunch: [], Dinner: [], Snack: [] };
      }

      const latestDate = new Date(latestRecommendation.createdAt);
      if (latestDate.toISOString().split('T')[0] !== today.toISOString().split('T')[0]) {
        return { Breakfast: [], Lunch: [], Dinner: [], Snack: [] };
      }

      const { predictions } = latestRecommendation;
      if (!predictions || !predictions.Breakfast || !predictions.Lunch || !predictions.Dinner || !predictions.Snack) {
        console.warn(`Invalid predictions structure for user ${userId}`);
        return { Breakfast: [], Lunch: [], Dinner: [], Snack: [] };
      }

      return {
        Breakfast: this.extractMeal(predictions.Breakfast),
        Lunch: this.extractMeal(predictions.Lunch),
        Dinner: this.extractMeal(predictions.Dinner),
        Snack: this.extractMeal(predictions.Snack),
      };
    } catch (error) {
      console.error(`Error fetching today's plan for user ${userId}:`, error);
      return { Breakfast: [], Lunch: [], Dinner: [], Snack: [] };
    }
  }

  async getTodaysTargetCalorie(userId: string): Promise<any> {
    const today = new Date();

    try {
      const latestRecommendation = await this.getLatestRecommendation(userId);
      if (!latestRecommendation) {
        return { daily_calorie_target: 2000 }; // Default target if no recommendation found
      }

      const latestDate = new Date(latestRecommendation.createdAt);
      if (latestDate.toISOString().split('T')[0] !== today.toISOString().split('T')[0]) {
        return; // Default target if not today's recommendation
      }

      const nutritionTargets = latestRecommendation.nutritionTargets;
      if (!nutritionTargets || !nutritionTargets.daily_calorie_target) {
        console.warn(`Invalid nutrition targets structure for user ${userId}`);
        return; // Default target
      }

      return { daily_calorie_target: nutritionTargets.daily_calorie_target };
    } catch (error) {
      console.error(`Error fetching today's target calorie for user ${userId}:`, error);
      return;// Default target on error
    }
  }


  async generatePersonalizedRecommendations(
    userId: string,
    healthMetricsDto: GetHealthMetricsDto,
  ) {
    // Get user profile
    const user = await this.drizzle.db.query.users.findFirst({
      where: eq(users.userId, userId),
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const mlPredictions = await this.mlApiService.getPredictions({
      age: healthMetricsDto.age,
      gender: healthMetricsDto.gender,
      height: healthMetricsDto.height,
      weight: healthMetricsDto.weight,
      activityLevel: healthMetricsDto.activityLevel,
      dietaryPreference: healthMetricsDto.dietaryPreference,
      weightGoal: healthMetricsDto.weightGoal,
      healthIssues: healthMetricsDto.healthIssues,
      fasting: healthMetricsDto.fasting,
      location: healthMetricsDto.location,
    });

    // Log the recommendation
    await this.storeRecommendation(userId, mlPredictions);

    // Create a notification for the user
    await this.notificationService.create({
      recipientId: userId,
      message: 'New meal recommendations are available based on your health data!',
      type: NotificationType.SYSTEM_ALERT,
      from: "System",
    });

    return {
      predictions: mlPredictions,
    };
  }


  async storeRecommendation(
    userId: string,
    mlResponse: any,
  ) {
    try {
      const today = new Date();
      today.setUTCHours(0, 0, 0, 0);
  
      const tomorrow = new Date(today);
      tomorrow.setUTCDate(today.getUTCDate() + 1);
  
      const healthMetrics = await this.drizzle.db.query.userHealthMetrics.findFirst({
        where: eq(userHealthMetrics.userId, userId),
      });

      if (!healthMetrics) {
        throw new Error('Health metrics not found for the user.');
      }
      
      // Transform the ML response to match our database schema
      const transformMealItem = (item: any) => {
        // console.log('Transforming meal item:', JSON.stringify(item, null, 2));
        return {
          local_name: item.local_name || item.food || 'Unknown Food',
          ingredient: item.ingredient || '',
          location: item.location || '',
          region: item.region || '',
          category: item.category || '',
          nutrition: {
            calories: item.nutrition?.calories || 0,
            protein: item.nutrition?.protein || 0,
            fat: item.nutrition?.fat || 0,
            carbohydrates: item.nutrition?.carbohydrates || 0,
            fiber: item.nutrition?.fiber || 0,
            sodium: item.nutrition?.sodium || 0,
          },
          serving_size_grams: item.serving_size_grams || 0,
          serving_nutrition: {
            calories: item.serving_nutrition?.calories || 0,
            protein: item.serving_nutrition?.protein || 0,
            fat: item.serving_nutrition?.fat || 0,
            carbohydrates: item.serving_nutrition?.carbohydrates || 0,
            fiber: item.serving_nutrition?.fiber || 0,
            sodium: item.serving_nutrition?.sodium || 0,
          },
        };
      };

      // Extract recommendations from the correct path in the response
      const recommendations = mlResponse.prediction?.recommendations || mlResponse.recommendations;
      // console.log('Extracted recommendations:', JSON.stringify(recommendations, null, 2));

      const predictions = {
        Breakfast: recommendations?.breakfast?.map(transformMealItem) || [],
        Lunch: recommendations?.lunch?.map(transformMealItem) || [],
        Snack: recommendations?.snack?.map(transformMealItem) || [],
        Dinner: recommendations?.dinner?.map(transformMealItem) || [],
      };

      // Extract nutrition targets from the correct path
      const nutritionDetail = mlResponse.prediction?.nutritional_detail || mlResponse.nutritional_detail;

      const leatestRecommendation = await this.getLatestRecommendation(userId);
      // Check if a recommendation already exists for today
      if (leatestRecommendation && new Date(leatestRecommendation.createdAt).toISOString().split('T')[0] === today.toISOString().split('T')[0]) {
        console.log("Updating existing recommendation for today");
        console.log(new Date(leatestRecommendation.createdAt).toISOString().split('T')[0], today.toISOString().split('T')[0], new Date())
        await this.drizzle.db.execute(sql`
          UPDATE "Recommendations" 
          SET 
            "predictions" = ${JSON.stringify(predictions)},
            "health_metric_id" = ${healthMetrics.healthMetricId},
            "nutrition_targets" = ${JSON.stringify({
              daily_calorie_target: nutritionDetail?.daily_calorie_target || 0,
              protein: nutritionDetail?.protein || 0,
              sugar: nutritionDetail?.sugar || 0,
              sodium: nutritionDetail?.sodium || 0,
              calories: nutritionDetail?.calories || 0,
              carbohydrates: nutritionDetail?.carbohydrates || 0,
              fiber: nutritionDetail?.fiber || 0,
              fat: nutritionDetail?.fat || 0,
            })},
            "updated_at" = CURRENT_TIMESTAMP
          WHERE "recommendation_id" = ${leatestRecommendation.recommendationId}
        `);
      }
      else {
        console.log("Inserting new recommendation for today");
        // Insert a new recommendation
      await this.drizzle.db.execute(sql
        `INSERT INTO "Recommendations" (
          "user_id", "health_metric_id", "predictions", "nutrition_targets"
        ) VALUES (
          ${userId}, ${healthMetrics.healthMetricId}, ${JSON.stringify(predictions)},
          ${JSON.stringify({
            daily_calorie_target: nutritionDetail?.daily_calorie_target || 0,
            protein: nutritionDetail?.protein || 0,
            sugar: nutritionDetail?.sugar || 0,
            sodium: nutritionDetail?.sodium || 0,
            calories: nutritionDetail?.calories || 0,
            carbohydrates: nutritionDetail?.carbohydrates || 0,
            fiber: nutritionDetail?.fiber || 0,
            fat: nutritionDetail?.fat || 0,
          })}
        )`
      );
    }

    return { status: 'success', message: 'Recommendation stored successfully' };
  } catch (error) {
    console.error('Error storing recommendation:', error);
    if (error instanceof Error) {
      throw new Error(`Failed to store recommendation: ${error.message}`);
    }
    throw new Error('Failed to store recommendation: Unknown error occurred');
  }
}
  

  async getRecommendationHistory(
    userId: string,
    QueryDto: GetRecommendationHistoryDto,
  ) {
    const { page = 1, limit = 10 } = QueryDto;
    const offset = (page - 1) * limit;

    const logs = await this.drizzle.db.query.recommendations.findMany({
      where: eq(recommendations.userId, userId),
      orderBy: desc(recommendations.createdAt),
      limit,
      offset,
    });

    const total = await this.drizzle.db
      .select({ count: sql`count(*)` })
      .from(recommendations)
      .where(eq(recommendations.userId, userId));

    return {
      recommendations: logs,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(Number(total[0].count) / limit),
      },
    };
  }

  async getLatestRecommendation(userId: string) {
    const latestRecommendation =
      await this.drizzle.db.query.recommendations.findFirst({
        where: eq(recommendations.userId, userId),
        orderBy: desc(recommendations.createdAt),
      });

    return latestRecommendation;
  }

  async getRecommendationsByDateRange(
    userId: string,
    startDate: Date,
    endDate: Date,
  ) {
    const recommendationHistory =
      await this.drizzle.db.query.recommendations.findMany({
        where: and(
          eq(recommendations.userId, userId),
          gte(recommendations.createdAt, startDate),
          lte(recommendations.createdAt, endDate),
        ),
        orderBy: desc(recommendations.createdAt),
      });

    return recommendationHistory;
  }
}
