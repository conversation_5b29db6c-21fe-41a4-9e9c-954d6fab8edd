import { 
  Controller, 
  Post, 
  Get, 
  Body, 
  Req,
  Query,
  UnauthorizedException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { RecommendationService } from './recommendation.service';
import { GetRecommendationHistoryDto } from './dto/get-recommendation.dto';
import { GetHealthMetricsDto } from '../health-metrics/dto/get-health-mertics.dto';
import { SkipThrottle } from '@nestjs/throttler/dist';

@ApiTags('Recommendations')
@Controller('recommendations')
@ApiBearerAuth()
@SkipThrottle( {default: true} )
export class RecommendationController {
  constructor(private readonly recommendationService: RecommendationService) {}


  @Get('todays-plan')
  @ApiOperation({ summary: 'Get today\'s plan' })
  async getTodaysPlan(@Req() req) {
    return await this.recommendationService.getTodaysPlan(req.user.id);
  }

  @Get('todays-target-calorie')
  @ApiOperation({ summary: 'Get today\'s target' })
  async getTodaysTarget(@Req() req) {
    return await this.recommendationService.getTodaysTargetCalorie(req.user.id);
  }

  @Get('history')
  @ApiOperation({ summary: 'Get recommendation history' })
  async getRecommendationHistory(
    @Req() req,
    @Query() query: GetRecommendationHistoryDto,

  ) {
    return await this.recommendationService.getRecommendationHistory(
      req.user.id,
      query
    );
  }

  @Post("save")
  @ApiOperation({ summary: 'Save recommendation' })
  async saveRecommendation(@Req() req, @Body() data: any) {    
    // Extract user ID from request
    const userId = req.user?.id;
    
    if (!userId) {
      throw new UnauthorizedException('User not authenticated');
    }
    
    // Pass both the user ID and the entire data object
    return await this.recommendationService.storeRecommendation(
      userId,
      data
    );
  }
}
