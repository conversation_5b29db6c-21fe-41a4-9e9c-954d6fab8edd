import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsNumber, IsArray, ValidateNested } from 'class-validator';


export class GetRecommendationHistoryDto {
  
  @ApiProperty({ description: 'Page number', required: false, default: 1 })
  @IsOptional()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ description: 'Number of items per page', required: false, default: 10 })
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10;
}

export class RegressionResultDto {
  @ApiProperty({ description: 'Daily calorie target' })
  @IsNumber()
  dailyCalorieTarget: number;

  @ApiProperty({ description: 'Protein amount in grams' })
  @IsNumber()
  protein: number;

  @ApiProperty({ description: 'Sugar amount in grams' })
  @IsNumber()
  sugar: number;

  @ApiProperty({ description: 'Sodium amount in milligrams' })
  @IsNumber()
  sodium: number;

  @ApiProperty({ description: 'Calories amount in kcal' })
  @IsNumber()
  calories: number;

  @ApiProperty({ description: 'Carbohydrates amount in grams' })
  @IsNumber()
  carbohydrates: number;

  @ApiProperty({ description: 'Fiber amount in grams' })
  @IsNumber()
  fiber: number;

  @ApiProperty({ description: 'Fat amount in grams' })
  @IsNumber()
  fat: number;
}

export class FoodPredictionDto {
  @ApiProperty({ description: 'Food name' })
  food: string;

  @ApiProperty({ description: 'Probability of the food being recommended' })
  @IsNumber()
  probability: number;
}

export class ClassificationResultDto {
  @ApiProperty({ description: 'Predictions for breakfast', type: [FoodPredictionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FoodPredictionDto)
  breakfast: FoodPredictionDto[];

  @ApiProperty({ description: 'Predictions for lunch', type: [FoodPredictionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FoodPredictionDto)
  lunch: FoodPredictionDto[];

  @ApiProperty({ description: 'Predictions for snacks', type: [FoodPredictionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FoodPredictionDto)
  snack: FoodPredictionDto[];

  @ApiProperty({ description: 'Predictions for dinner', type: [FoodPredictionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FoodPredictionDto)
  dinner: FoodPredictionDto[];
}

export class MLResponseDto {
  @ApiProperty({ description: 'Regression results', type: RegressionResultDto })
  @ValidateNested()
  @Type(() => RegressionResultDto)
  regression: RegressionResultDto;

  @ApiProperty({ description: 'Classification results', type: ClassificationResultDto })
  @ValidateNested()
  @Type(() => ClassificationResultDto)
  classification: ClassificationResultDto;
}