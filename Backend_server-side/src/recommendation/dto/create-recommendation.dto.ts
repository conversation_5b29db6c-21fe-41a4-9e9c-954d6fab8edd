import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID, IsNotEmpty, IsArray, IsNumber, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

class PredictionItemDto {
  @ApiProperty({ description: 'Food name' })
  @IsString()
  @IsNotEmpty()
  food: string;

  @ApiProperty({ description: 'Probability of the food being recommended' })
  @IsNumber()
  probability: number;
}

class MealPredictionsDto {
  @ApiProperty({ description: 'Predictions for breakfast', type: [PredictionItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PredictionItemDto)
  Breakfast: PredictionItemDto[];

  @ApiProperty({ description: 'Predictions for lunch', type: [PredictionItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PredictionItemDto)
  Lunch: PredictionItemDto[];

  @ApiProperty({ description: 'Predictions for snacks', type: [PredictionItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PredictionItemDto)
  Snack: PredictionItemDto[];

  @ApiProperty({ description: 'Predictions for dinner', type: [PredictionItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PredictionItemDto)
  Dinner: PredictionItemDto[];
}

export class CreateRecommendationDto {
  @ApiProperty({ description: 'Recommendation ID' })
  @IsUUID()
  recommendationId: string;

  @ApiProperty({ description: 'User ID' })
  @IsUUID()
  userId: string;

  @ApiProperty({ description: 'Health Metric ID' })
  @IsUUID()
  healthMetricId: string;

  @ApiProperty({ description: 'Meal predictions', type: MealPredictionsDto })
  @ValidateNested()
  @Type(() => MealPredictionsDto)
  predictions: MealPredictionsDto;

  @ApiProperty({ description: 'Creation timestamp' })
  @IsString()
  createdAt: string;

  @ApiProperty({ description: 'Update timestamp' })
  @IsString()
  updatedAt: string;
}
