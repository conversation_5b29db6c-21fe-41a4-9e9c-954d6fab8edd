import { Injectable, HttpException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { GetHealthMetricsDto } from '../health-metrics/dto/get-health-mertics.dto';

@Injectable()
export class MLApiService {
  private readonly apiUrl: string;

  constructor(private configService: ConfigService) {
    this.apiUrl = this.configService.get('ML_API_URL');
  }

  async getPredictions(userData: GetHealthMetricsDto
) {
    try {
      const mlInput = {
        Ages: userData.age,
        Gender: userData.gender,
        Height: userData.height,
        Weight: userData.weight,
        Activity_Level: userData.activityLevel,
        Dietary_Preference: userData.dietaryPreference,
        Weight_Goal: userData.weightGoal,
        Health_Issues: userData.healthIssues,
        Fasting: userData.fasting,
        Location: userData.location
      };

      const response = await axios.post(`${this.apiUrl}/predict`, mlInput);
      return response.data;
    } catch (error) {
      console.error('ML API Error:', error.response?.data || error.message);
      throw new HttpException(
        error.response?.data?.detail || 'Failed to get predictions from ML service',
        error.response?.status || 500
      );
    }
  }
}
