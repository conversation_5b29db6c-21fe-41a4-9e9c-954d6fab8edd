import { Modu<PERSON> } from '@nestjs/common';
import { RecommendationService } from './recommendation.service';
import { RecommendationController } from './recommendation.controller';
import { HealthMetricsModule } from '../health-metrics/health-metrics.module';
import { MLApiService } from './ml-api.service';
import { ConfigModule } from '@nestjs/config';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    ConfigModule,
    HealthMetricsModule,
    NotificationModule,
  ],
  controllers: [RecommendationController],
  providers: [RecommendationService, MLApiService],
  exports: [RecommendationService]
})
export class RecommendationModule {}
