export enum Role {
  SUPERADMIN = "SUPERADMIN",
  ADMIN = 'ADMIN',
  USER = 'USER',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  DELETED = 'deleted',
  PENDING = 'pending',
}

export enum AdminStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  DELETED = 'deleted',
  PENDING = 'pending',
}

export enum NotificationType {
  BLOG_UPDATE = 'Blog Update',
  FEEDBACK_ALERT = 'Feedback Alert',
  USER_ACTION = 'User Action',
  SYSTEM_ALERT = 'System Alert',
}

export enum NotificationCategory {
  INFO = 'Info',
  ALERT = 'Alert',
  CRITICAL = 'Critical',
  REMINDER = 'Reminder',
}

export enum BlogCategory {
  Health = 'Health',
  Lifestyle = 'Lifestyle',
  Fitness = 'Fitness',
  Nutrition = 'Nutrition'
}


export enum FeedbackStatus {
  PENDING = 'Pending',
  REVIEWED = 'Reviewed',
  RESOLVED = 'Resolved'
}