import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as fs from 'fs';
import * as https from 'https';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import * as crypto from 'crypto';
import cookieParser from 'cookie-parser';
// import { setupHttps } from './config/https.config';

// // Make it globally available
global.crypto = crypto.webcrypto as unknown as Crypto;

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Add cookie parser middleware
  app.use(cookieParser());

  // Global Validation Pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    })
  );

  // CORS Configuration
  app.enableCors({
    origin: [
      'https://nutrifocus.duckdns.org', 
      'https://nutrifocusmealplan.duckdns.org',
      'https://localhost:3000',
      'http://localhost:3000',
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Cookie'],
    exposedHeaders: ['Authorization', 'Set-Cookie'],
    maxAge: 86400, // Cache preflight for 24 hours
    preflightContinue: false,
    optionsSuccessStatus: 204
  });

  // Swagger Documentation Setup
  const config = new DocumentBuilder()
    .setTitle('API Documentation')
    .setDescription('Your API description')
    .setVersion('1.0')
    .addBearerAuth() // If using JWT
    .addCookieAuth('access_token') // For cookie-based auth
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true, // Keep auth token between refreshes
      withCredentials: true, // Enable sending cookies
    },
  });

  await app.init();

  const port = process.env.PORT || 4000;
  app.listen(port, () => {
    console.log(`🚀 Server running on https://nutrifocus.duckdns.org`);
    console.log(`📄 Swagger docs: https://nutrifocus.duckdns.org/api`);
  });

  // await setupHttps(app);
}

bootstrap();
