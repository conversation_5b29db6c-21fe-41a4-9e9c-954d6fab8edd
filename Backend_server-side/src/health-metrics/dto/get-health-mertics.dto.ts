import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, <PERSON><PERSON>umber, <PERSON>, Max, IsArray } from 'class-validator';

export class GetHealthMetricsDto {
  @ApiProperty({ description: 'User age in years', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(120)
  age?: number;
  
  @ApiProperty({ description: 'User gender', required: false })
  @IsOptional()
  @IsString()
  gender?: string;

  @ApiProperty({ description: 'User weight in kilograms', required: false })
  @IsOptional()
  @IsNumber()
  @Min(20)
  @Max(300)
  weight?: number;

  @ApiProperty({ description: 'User height in centimeters', required: false })
  @IsOptional()
  @IsNumber()
  @Min(50)
  @Max(250)
  height?: number;

  @ApiProperty({ description: 'Health issues or conditions', required: false })
  @IsOptional()
  @IsArray()
  healthIssues?: [string];

  @ApiProperty({ description: 'Health and dietary goals', required: false })
  @IsOptional()
  @IsString()
  goals?: string;

  @ApiProperty({ description: 'User activity level', required: false })
  @IsOptional()
  @IsString()
  activityLevel?: string;

  @ApiProperty({ description: 'User dietary preference', required: false })
  @IsOptional()
  @IsString()
  dietaryPreference?: string;

  @ApiProperty({ description: 'User weight goal', required: false })
  @IsOptional()
  @IsString()
  weightGoal?: string;

  @ApiProperty({ description: 'User fasting status', required: false })
  @IsOptional()
  @IsString()
  fasting?: string;

  @ApiProperty({ description: 'User location', required: false })
  @IsOptional()
  @IsString()
  location?: string;
}
