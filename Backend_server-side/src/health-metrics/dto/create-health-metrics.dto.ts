import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, <PERSON>N<PERSON>ber, <PERSON>, <PERSON>, IsArray } from 'class-validator';

export class CreateHealthMetricsDto {
  @ApiProperty({ description: 'User age in years', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(120)
  age?: number;
  
  @ApiProperty({ description: 'User gender', required: false })
  @IsOptional()
  @IsString()
  gender?: string;

  @ApiProperty({ description: 'User weight in kilograms', required: false })
  @IsOptional()
  @IsNumber()
  @Min(20)
  @Max(300)
  weight?: number;

  @ApiProperty({ description: 'User height in centimeters', required: false })
  @IsOptional()
  @IsNumber()
  @Min(50)
  @Max(250)
  height?: number;

  @ApiProperty({ description: 'Health issues or conditions', required: false })
  @IsOptional()
  @IsArray()
  health_issues?: string[];

  @ApiProperty({ description: 'User activity level', required: false })
  @IsOptional()
  @IsString()
  activity_level?: string;

  @ApiProperty({ description: 'User dietary preference', required: false })
  @IsOptional()
  @IsString()
  dietary_preference?: string;

  @ApiProperty({ description: 'User weight goal', required: false })
  @IsOptional()
  @IsString()
  weight_goal?: string;

  @ApiProperty({ description: 'User fasting status', required: false })
  @IsOptional()
  @IsString()
  fasting?: string;

  @ApiProperty({ description: 'User location', required: false })
  @IsOptional()
  @IsString()
  location?: string;
}


export class CreateHealthMetricOnBoardingsDto {
  @ApiProperty({ description: 'User age in years', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(120)
  age?: number;
  
  @ApiProperty({ description: 'User gender', required: false })
  @IsOptional()
  @IsString()
  gender?: string;

  @ApiProperty({ description: 'User weight in kilograms', required: false })
  @IsOptional()
  @IsNumber()
  @Min(20)
  @Max(300)
  weight?: number;

  @ApiProperty({ description: 'User height in centimeters', required: false })
  @IsOptional()
  @IsNumber()
  @Min(50)
  @Max(250)
  height?: number;

  @ApiProperty({ description: 'Health issues or conditions', required: false })
  @IsOptional()
  @IsArray()
  health_issues?: string[];

  @ApiProperty({ description: 'User activity level', required: false })
  @IsOptional()
  @IsString()
  activity_level?: string;

  @ApiProperty({ description: 'User dietary preference', required: false })
  @IsOptional()
  @IsString()
  dietary_preference?: string;

  @ApiProperty({ description: 'User weight goal', required: false })
  @IsOptional()
  @IsString()
  weight_goal?: string;

  @ApiProperty({ description: 'User fasting status', required: false })
  @IsOptional()
  @IsString()
  fasting?: string;

  @ApiProperty({ description: 'User location', required: false })
  @IsOptional()
  @IsString()
  location?: string;
}
