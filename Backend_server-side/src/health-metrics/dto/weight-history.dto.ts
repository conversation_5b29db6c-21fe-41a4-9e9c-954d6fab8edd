
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsN<PERSON>ber, IsString, Min, Max, IsBoolean } from 'class-validator';

export class AddWeightEntryDto {
  @ApiProperty({ description: 'User ID', example: '0bf5979c-c5c4-49ba-8b1e-77de84d1c2a8' })
  @IsString()
  userId: string;

  @ApiProperty({ description: 'User weight in kilograms', example: 72.5 })
  @IsNumber()
  @Min(20)
  @Max(300)
  weight: number;

  @ApiProperty({ description: 'Is this entry a goal entry?', required: false, example: false })
  @IsOptional()
  @IsBoolean()
  isGoalEntry?: boolean;

  @ApiProperty({ description: 'User weight goal', required: false, example: 'Weight Gain' })
  @IsOptional()
  @IsString()
  weightGoal?: string;

  @ApiProperty({ description: 'Target weight in kilograms', required: false, example: 68 })
  @IsOptional()
  @IsNumber()
  @Min(20)
  @Max(300)
  targetWeight?: number;

  @ApiProperty({ description: 'Starting weight in kilograms', required: false, example: 75 })
  @IsOptional()
  @IsNumber()
  @Min(20)
  @Max(300)
  startingWeight?: number;
}

export class UpdateGoalDto {
  @ApiProperty({ description: 'User ID', example: '0bf5979c-c5c4-49ba-8b1e-77de84d1c2a8' })
  @IsString()
  userId: string;

  @ApiProperty({ description: 'User weight goal', example: 'Weight Maintain' })
  @IsString()
  weightGoal: string;

  @ApiProperty({ description: 'Target weight in kilograms', required: false, example: 68 })
  @IsOptional()
  @IsNumber()
  @Min(20)
  @Max(300)
  targetWeight?: number;
}

export class UpdateWeightDto {
  @ApiProperty({ description: 'User ID', example: '0bf5979c-c5c4-49ba-8b1e-77de84d1c2a8' })
  @IsString()
  userId: string;

  @ApiProperty({ description: 'User weight in kilograms', example: 72.5 })
  @IsNumber()
  @Min(20)
  @Max(300)
  weight: number;
}
