import { Module } from '@nestjs/common';
import { HealthMetricsService } from './health-metrics.service';
import { HealthMetricsController } from './health-metrics.controller';
import { WeightHistoryService } from './weight-history.service';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    NotificationModule, // This will provide NotificationService with its dependencies
  ],
  controllers: [HealthMetricsController],
  providers: [HealthMetricsService, WeightHistoryService],
  exports: [HealthMetricsService, WeightHistoryService],
})
export class HealthMetricsModule {}
