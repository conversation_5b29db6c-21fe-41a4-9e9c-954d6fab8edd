import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { DrizzleService } from '../database/drizzle.service';
import {
  UpdateHealthMetricsDto,
  UpdateHealthMetricsOnBoardingDto,
} from './dto/update-health-metrics.dto';
import { userHealthMetrics } from '../database/database-schema';
import { eq } from 'drizzle-orm';
import {
  CreateHealthMetricOnBoardingsDto,
  CreateHealthMetricsDto,
} from './dto/create-health-metrics.dto';
import { WeightHistoryService } from './weight-history.service';
import { NotificationService } from '../notification/notification.service';
import { NotificationType } from '../common/enums/permissions.enums';
import { fa } from '@faker-js/faker/dist';

@Injectable()
export class HealthMetricsService {
  constructor(
    private drizzle: DrizzleService,
    private weightHistoryService: WeightHistoryService,
    private notificationService: NotificationService,
  ) {}

  async getHealthMetrics(userId: string) {
    const result = await this.drizzle.db
      .select()
      .from(userHealthMetrics)
      .where(eq(userHealthMetrics.userId, userId));

    if (!result.length || !result[0]) {
      return {
        age: null,
        weight: null,
        height: null,
        healthIssues: null,
        weightGoal: null,
        startingWeight: null,
        targetWeight: null,
        bmi: null,
      };
    }

    const metrics = result[0];

    // Calculate BMI if height and weight are available
    let bmi = null;
    if (metrics.height && metrics.weight) {
      const heightInMeters = Number(metrics.height) / 100;
      bmi = parseFloat(
        (
          Number(metrics.weight) /
          (heightInMeters * heightInMeters)
        ).toFixed(1),
      );
    }

    return {
      ...metrics,
      bmi,
    };
  }

  private normalizeHealthIssues(
    healthIssues: string | string[] | undefined,
  ): string[] {
    if (!healthIssues || healthIssues === 'undefined') return [];
    if (Array.isArray(healthIssues)) return healthIssues;
    return [healthIssues];
  }

  async createHealthMetrics(
    userId: string,
    createDto: CreateHealthMetricsDto,
  ) {
    try {
      const existingMetrics = await this.drizzle.db
      .select()
      .from(userHealthMetrics)
      .where(eq(userHealthMetrics.userId, userId));
      if (existingMetrics.length > 0) {
        throw new BadRequestException(
          'Health metrics already exist for this user.',
        );
      }
      
      const suggestedTargetWeight = await this.weightHistoryService.suggestTargetWeight(
        userId,
        createDto.height,
        createDto.weight,
        createDto.weight_goal,
      );

      console.log(suggestedTargetWeight,"sadfa");
      const healthIssues = this.normalizeHealthIssues(createDto.health_issues);

      const insertData = {
        userId,
        age: createDto.age?.toString(),
        gender: createDto.gender,
        weight: createDto.weight?.toString(),
        height: createDto.height?.toString(),
        healthIssues,
        activityLevel: createDto.activity_level,
        dietaryPreference: createDto.dietary_preference,
        weightGoal: createDto.weight_goal,
        startingWeight: createDto.weight?.toString(),
        targetWeight: suggestedTargetWeight?.toString(),
        fasting: createDto.fasting || 'No',
        location: createDto.location,
        goalUpdatedAt: createDto.weight_goal ? new Date() : undefined,
      };

      const result = await this.drizzle.db
        .insert(userHealthMetrics)
        .values(insertData)
        .returning();

      // Record initial weight in history
      if (createDto.weight) {
        await this.weightHistoryService.addWeightEntry(
          userId,
          createDto.weight,
          true,
          createDto.weight_goal,
          suggestedTargetWeight,
          createDto.weight,
        );
      }

      return result[0];
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to create health metrics: ' + error.message,
      );
    }
  }

  async createHealthMetricsOnBoarding(
    userId: string,
    createDto: CreateHealthMetricOnBoardingsDto,
  ) {
    try {
      const existingMetrics = await this.drizzle.db
        .select()
        .from(userHealthMetrics)
        .where(eq(userHealthMetrics.userId, userId));

      if (existingMetrics.length > 0) {
        throw new BadRequestException(
          'Health metrics already exist for this user.',
        );
      }

      const suggestedTargetWeight = await this.weightHistoryService.suggestTargetWeight(
        userId,
        createDto.height,
        createDto.weight,
        createDto.weight_goal,
      );

      console.log(suggestedTargetWeight,"Weight Suggestion");
      const healthIssues = this.normalizeHealthIssues(createDto.health_issues);

      const insertData = {
        userId,
        age: createDto.age?.toString(),
        gender: createDto.gender,
        weight: createDto.weight?.toString(),
        height: createDto.height?.toString(),
        healthIssues,
        activityLevel: createDto.activity_level,
        dietaryPreference: createDto.dietary_preference,
        weightGoal: createDto.weight_goal,
        startingWeight: createDto.weight?.toString(),
        targetWeight: suggestedTargetWeight?.toString(),
        fasting: createDto.fasting || 'No',
        location: createDto.location,
        goalUpdatedAt: createDto.weight_goal ? new Date() : undefined,
      };


      const result = await this.drizzle.db
        .insert(userHealthMetrics)
        .values(insertData)
        .returning();

      // Record initial weight in history
      if (createDto.weight) {
        await this.weightHistoryService.addWeightEntry(
          userId,
          createDto.weight,
          true,
          createDto.weight_goal,
          suggestedTargetWeight,
          createDto.weight,
        );
      }

      return result[0];
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to create health metrics: ' + error.message,
      );
    }
  }

  async updateHealthMetricsOnBoarding(
    userId: string,
    updateDto: UpdateHealthMetricsOnBoardingDto,
  ) {
    try {
      const existingMetrics = await this.drizzle.db
        .select()
        .from(userHealthMetrics)
        .where(eq(userHealthMetrics.userId, userId));

      if (!existingMetrics.length) {
        const createDto: CreateHealthMetricOnBoardingsDto = {
          ...updateDto,
          health_issues: this.normalizeHealthIssues(updateDto.health_issues),
        };
        return await this.createHealthMetricsOnBoarding(userId, createDto);
      }

      const currentMetrics = existingMetrics[0];

      // If weight is being updated, add to weight history
      if (
        updateDto.weight !== undefined &&
        Number(updateDto.weight) !== Number(currentMetrics.weight)
      ) {
        await this.weightHistoryService.addWeightEntry(
          userId,
          Number(updateDto.weight),
          false,
          updateDto.weight_goal ?? currentMetrics.weightGoal,
          Number(currentMetrics.targetWeight),
          Number(currentMetrics.startingWeight),
        );
      }

      // Update goal tracking if weight goal changes
      if (updateDto.weight_goal !== undefined &&
        updateDto.weight_goal !== currentMetrics.weightGoal) {
          console.log("Updating weight goal");
          return this.weightHistoryService.updateGoal(userId, updateDto.weight_goal);
        }

      const healthIssues =
        updateDto.health_issues !== undefined
          ? this.normalizeHealthIssues(updateDto.health_issues)
          : undefined;

      const updateData = {
        age: updateDto.age?.toString(),
        gender: updateDto.gender,
        weight: updateDto.weight?.toString(),
        height: updateDto.height?.toString(),
        healthIssues,
        activityLevel: updateDto.activity_level,
        dietaryPreference: updateDto.dietary_preference,
        fasting: updateDto.fasting,
        location: updateDto.location,
      };

      // Remove undefined values
      const filteredUpdateData = Object.fromEntries(
        Object.entries(updateData).filter(([_, value]) => value !== undefined),
      );

      const result = await this.drizzle.db
        .update(userHealthMetrics)
        .set(filteredUpdateData)
        .where(eq(userHealthMetrics.userId, userId))
        .returning();

      return result[0];
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to update health metrics: ' + error.message,
      );
    }
  }

  async updateHealthMetrics(
    userId: string,
    updateDto: UpdateHealthMetricsDto,
  ) {
    try {
      const existingMetrics = await this.drizzle.db
        .select()
        .from(userHealthMetrics)
        .where(eq(userHealthMetrics.userId, userId));

      if (!existingMetrics.length) {
        const createDto: CreateHealthMetricsDto = {
          ...updateDto,
          health_issues: this.normalizeHealthIssues(updateDto.health_issues),
        };
        return this.createHealthMetrics(userId, createDto);
      }

      const currentMetrics = existingMetrics[0];

      // Update goal tracking if weight goal changes
        if (updateDto.weight_goal !== undefined &&
        updateDto.weight_goal !== currentMetrics.weightGoal) {
          console.log("Updating weight goal");
          return this.weightHistoryService.updateGoal(userId, updateDto.weight_goal);
        }

      // If weight is being updated, add to weight history
      if (updateDto.weight !== undefined) {
        await this.weightHistoryService.addWeightEntry(
          userId,
          Number(updateDto.weight),
          false,
          updateDto.weight_goal,
          Number(currentMetrics.targetWeight),
          Number(currentMetrics.startingWeight),
        );
      }

      const healthIssues =
        updateDto.health_issues !== undefined
          ? this.normalizeHealthIssues(updateDto.health_issues)
          : undefined;

      const updateData = {
        age: updateDto.age?.toString(),
        gender: updateDto.gender,
        weight: updateDto.weight?.toString(),
        height: updateDto.height?.toString(),
        healthIssues,
        activityLevel: updateDto.activity_level,
        dietaryPreference: updateDto.dietary_preference,
        fasting: updateDto.fasting,
        location: updateDto.location,
      };

      // Remove undefined values
      const filteredUpdateData = Object.fromEntries(
        Object.entries(updateData).filter(([_, value]) => value !== undefined),
      );

      const result = await this.drizzle.db
        .update(userHealthMetrics)
        .set(filteredUpdateData)
        .where(eq(userHealthMetrics.userId, userId))
        .returning();

      await this.notificationService.create({
        recipientId: userId,
        message: 'Your health metrics have been updated successfully!',
        type: NotificationType.SYSTEM_ALERT,
        from: "System",
      });

      return result[0];
    } catch (error) {
      throw new Error('Failed to update health metrics: ' + error.message);
    }
  }
}
