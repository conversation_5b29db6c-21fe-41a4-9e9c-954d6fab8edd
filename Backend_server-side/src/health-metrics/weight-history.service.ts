import { Injectable } from '@nestjs/common';
import { DrizzleService } from '../database/drizzle.service';
import { userHealthMetrics, weightHistory } from '../database/database-schema';
import { eq, desc, asc, sql } from 'drizzle-orm';
import { datacatalog } from 'googleapis/build/src/apis/datacatalog';

@Injectable()
export class WeightHistoryService {
  constructor(private drizzle: DrizzleService) {}

  async addWeightEntry(
    userId: string,
    weight: number,
    isGoalEntry: boolean = false,
    weightGoal?: string,
    targetWeight?: number,
    startingWeight?: number,
  ) {
    try {
      const today = new Date().toISOString().split('T')[0];
      const lastEntry = await this.lastWeightEntry(userId);
  
      // If there's already an entry for today, update it
      if (lastEntry && new Date(lastEntry.recordedAt).toISOString().split('T')[0] === today) {
        console.log('Updating existing weight entry for today');

        const result = await this.drizzle.db.execute(sql`
          UPDATE "WeightHistory"
          SET "weight" = ${weight.toString()},
              "is_goal_entry" = ${isGoalEntry},
              "weight_goal" = ${isGoalEntry ? weightGoal : null},
              "target_weight" = ${isGoalEntry && targetWeight ? targetWeight.toString() : null},
              "starting_weight" = ${isGoalEntry && startingWeight ? startingWeight.toString() : null},
              "updated_at" = ${new Date()}
          WHERE "user_id" = ${userId} AND DATE("recorded_at") = DATE(${lastEntry.recordedAt})
        `);
        return result[0];
      }

      const result = await this.drizzle.db.execute(sql`
        INSERT INTO "WeightHistory" (
          "user_id", 
          "weight", 
          "is_goal_entry", 
          "weight_goal", 
          "target_weight", 
          "starting_weight", 
          "recorded_at"
        ) VALUES (
          ${userId}, 
          ${weight.toString()}, 
          ${isGoalEntry}, 
          ${isGoalEntry ? weightGoal : null}, 
          ${isGoalEntry && targetWeight ? targetWeight.toString() : null}, 
          ${isGoalEntry && startingWeight ? startingWeight.toString() : null}, 
          ${new Date()}
        )
      `);
      return result[0];
    } catch (error) {
      throw new Error(`Failed to add weight entry: ${error.message}`);
    }
  }

  async lastWeightEntry(userId: string) {
    try {
      const result = await this.drizzle.db
        .select()
        .from(weightHistory)
        .where(eq(weightHistory.userId, userId))
        .orderBy(desc(weightHistory.recordedAt))
        .limit(1);
      return result[0] || null;
    } catch (error) {
      throw new Error(`Failed to get latest weight entry: ${error.message}`);
    }
  }

  async suggestTargetWeight(userId: string, height: number, weight: number, weightGoal: string): Promise<number> {
    try {
      
      const heightM = Number(height) / 100;
      const currentBmi = Number(weight) / (heightM * heightM);
      let targetBmi: number;
      console.log("current BMI:", currentBmi, "weightGoal:", weightGoal);
      if (weightGoal.includes('Gain')) {
        targetBmi = currentBmi < 18.5 ? 20 : currentBmi + 1;
      } else if (weightGoal.includes('Lose')) {
        targetBmi = currentBmi > 24.9 ? 23 : currentBmi - 1;
      } else {
        targetBmi = currentBmi >= 18.5 && currentBmi <= 24.9 ? currentBmi : 22;
      }
      const targetWeight = targetBmi * (heightM * heightM);
      return Math.round(targetWeight * 10) / 10; // 1 decimal
    } catch (error) {
      throw new Error(`Failed to suggest target weight: ${error.message}`);
    }
  }

  async updateGoal(userId: string, weightGoal: string) {
    console.log('Updating weight goal:', weightGoal);
    try {
      const metrics = await this.drizzle.db
        .select({
          weight: userHealthMetrics.weight,
          height: userHealthMetrics.height,
        })
        .from(userHealthMetrics)
        .where(eq(userHealthMetrics.userId, userId));
      if (!metrics[0]) throw new Error('User health metrics not found');
  
      const { weight, height } = metrics[0];
      const suggestedTarget = await this.suggestTargetWeight(userId, Number(height), Number(weight), weightGoal);
      // const finalTarget = targetWeight ?? suggestedTarget;
      const finalTarget = suggestedTarget
      // Validate BMI (18–30)
      const heightM = Number(height) / 100;
      // const targetBmi = finalTarget / (heightM * heightM);
      const targetBmi = suggestedTarget / (heightM * heightM);
      if (targetBmi < 18 || targetBmi > 30) throw new Error('Target weight outside safe BMI range');
  
      await this.addWeightEntry(userId, Number(weight), true, weightGoal, suggestedTarget, Number(weight));
        
      await this.drizzle.db.execute(sql`
        UPDATE "UserHealthMetrics"
        SET 
          "weight_goal" = ${weightGoal},
          "starting_weight" = ${weight},
          "target_weight" = ${suggestedTarget.toString()},
          "goal_updated_at" = ${new Date()}
        WHERE "user_id" = ${userId}
      `);
  
      return { weightGoal, targetWeight: finalTarget };
    } catch (error) {
      throw new Error(`Failed to update goal: ${error.message}`);
    }
  }

  async getProgress(userId: string) {
    try {
      const metrics = await this.drizzle.db
        .select({
          weight: userHealthMetrics.weight,
          startingWeight: userHealthMetrics.startingWeight,
          targetWeight: userHealthMetrics.targetWeight,
          weightGoal: userHealthMetrics.weightGoal,
        })
        .from(userHealthMetrics)
        .where(eq(userHealthMetrics.userId, userId));
      if (!metrics[0]) throw new Error('User health metrics not found');
  
      const { weight, startingWeight, targetWeight, weightGoal } = metrics[0];
      if (weightGoal.includes("Maintain")) {
        return {
          status: Math.abs(Number(weight) - Number(targetWeight)) <= 1 ? 'on_track' : 'off_track',
        };
      }
      const progress =
        ((Number(weight) - Number(startingWeight)) /
         (Number(targetWeight) - Number(startingWeight))) * 100;
      return { progress: Math.min(Math.max(progress, 0), 100) };
    } catch (error) {
      throw new Error(`Failed to get progress: ${error.message}`);
    }
  }
  async getWeightHistory(userId: string) {
    try {
      const result = await this.drizzle.db
        .select()
        .from(weightHistory)
        .where(eq(weightHistory.userId, userId))
        .orderBy(asc(weightHistory.recordedAt));

      return result;
    } catch (error) {
      throw new Error(`Failed to get weight history: ${error.message}`);
    }
  }

  async getWeightHistoryForChart(userId: string) {
    try {
      const result = await this.drizzle.db
        .select({
          recordedAt: weightHistory.recordedAt,
          weight: weightHistory.weight,
          isGoalEntry: weightHistory.isGoalEntry,
          weightGoal: weightHistory.weightGoal,
          targetWeight: weightHistory.targetWeight,
          startingWeight: weightHistory.startingWeight,
        })
        .from(weightHistory)
        .where(eq(weightHistory.userId, userId))
        .orderBy(asc(weightHistory.recordedAt));

      if (result.length === 0) {
        // Get user's health metrics for fallback data
        const userMetrics = await this.drizzle.db
          .select({
            weight: userHealthMetrics.weight,
            targetWeight: userHealthMetrics.targetWeight,
            startingWeight: userHealthMetrics.startingWeight,
          })
          .from(userHealthMetrics)
          .where(eq(userHealthMetrics.userId, userId))
          .limit(1);

        if (userMetrics.length > 0) {
          const today = new Date();
          const yesterday = new Date(today);
          yesterday.setDate(today.getDate() - 1);
          const twoDaysAgo = new Date(today);
          twoDaysAgo.setDate(today.getDate() - 2);

          return [
            {
              date: twoDaysAgo.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
              }),
              weight: Number(userMetrics[0].weight),
              isGoalEntry: false,
              weightGoal: null,
              targetWeight: userMetrics[0].targetWeight ? Number(userMetrics[0].targetWeight) : null,
              startingWeight: userMetrics[0].startingWeight ? Number(userMetrics[0].startingWeight) : null,
              isFallback: true
            },
            {
              date: yesterday.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
              }),
              weight: Number(userMetrics[0].weight),
              isGoalEntry: false,
              weightGoal: null,
              targetWeight: userMetrics[0].targetWeight ? Number(userMetrics[0].targetWeight) : null,
              startingWeight: userMetrics[0].startingWeight ? Number(userMetrics[0].startingWeight) : null,
              isFallback: true
            }
          ];
        }
      }

      // If there's only one entry, duplicate it with a different date
      if (result.length === 1) {
        const entry = result[0];
        const entryDate = new Date(entry.recordedAt);
        const dayBefore = new Date(entryDate);
        dayBefore.setDate(entryDate.getDate() - 1);

        return [
          {
            date: dayBefore.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
            }),
            weight: Number(entry.weight),
            isGoalEntry: entry.isGoalEntry,
            weightGoal: entry.weightGoal,
            targetWeight: entry.targetWeight ? Number(entry.targetWeight) : null,
            startingWeight: entry.startingWeight ? Number(entry.startingWeight) : null,
            isFallback: true
          },
          {
            date: entryDate.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
            }),
            weight: Number(entry.weight),
            isGoalEntry: entry.isGoalEntry,
            weightGoal: entry.weightGoal,
            targetWeight: entry.targetWeight ? Number(entry.targetWeight) : null,
            startingWeight: entry.startingWeight ? Number(entry.startingWeight) : null,
            isFallback: false
          }
        ];
      }

      return result.map((entry) => ({
        date: new Date(entry.recordedAt).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
        }),
        weight: Number(entry.weight),
        isGoalEntry: entry.isGoalEntry,
        weightGoal: entry.weightGoal,
        targetWeight: entry.targetWeight ? Number(entry.targetWeight) : null,
        startingWeight: entry.startingWeight ? Number(entry.startingWeight) : null,
        isFallback: false
      }));
    } catch (error) {
      throw new Error(`Failed to get weight history for chart: ${error.message}`);
    }
  }

  async updateWeight(userId: string, weight: number) {
    try {
      // Update userHealthMetrics
      await this.drizzle.db
        .update(userHealthMetrics)
        .set({
          weight: weight.toString(),
        })
        .where(eq(userHealthMetrics.userId, userId));
  
      // Add weight entry
      const result = await this.addWeightEntry(userId, weight);
      return result;
    } catch (error) {
      throw new Error(`Failed to update weight: ${error.message}`);
    }
  }

}
