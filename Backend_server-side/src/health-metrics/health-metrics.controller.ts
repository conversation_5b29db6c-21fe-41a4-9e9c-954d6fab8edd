import { Controller, Get, Patch, Body, Request, Post, UseGuards, Param, Put } from '@nestjs/common';
import { HealthMetricsService } from './health-metrics.service';
import { UpdateHealthMetricsDto, UpdateHealthMetricsOnBoardingDto } from './dto/update-health-metrics.dto';
import { CreateHealthMetricsDto } from './dto/create-health-metrics.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { Roles } from '../common/decorators/role.decorator';
import { Role } from '../common/enums/permissions.enums';
import { WeightHistoryService } from './weight-history.service';
import { AddWeightEntryDto, UpdateGoalDto, UpdateWeightDto } from './dto/weight-history.dto';
import { SkipThrottle } from '@nestjs/throttler/dist';

@ApiTags('health-metrics')
@Controller('health-metrics')
@SkipThrottle({default: true})
@Roles(Role.USER)
export class HealthMetricsController {
  constructor(private readonly healthMetricsService: HealthMetricsService,
    private readonly weightHistoryService: WeightHistoryService,
  ) {}

  @Post('add-Weight')
  async addWeightEntry(@Body() addWeightEntryDto: AddWeightEntryDto) {
    return await this.weightHistoryService.addWeightEntry(
      addWeightEntryDto.userId,
      addWeightEntryDto.weight,
      addWeightEntryDto.isGoalEntry,
      addWeightEntryDto.weightGoal,
      addWeightEntryDto.targetWeight,
      addWeightEntryDto.startingWeight,
    );
  }

  @Put('update-weight-goal')
  async updateGoal(@Body() updateGoalDto: UpdateGoalDto) {
    return await this.weightHistoryService.updateGoal(
      updateGoalDto.userId,
      updateGoalDto.weightGoal,
    );
  }

  @Put('update-weight')
  async updateWeight(@Body() updateWeightDto: UpdateWeightDto) {
    return await this.weightHistoryService.updateWeight(
      updateWeightDto.userId,
      updateWeightDto.weight,
    );
  }

  @Get('weight-progress/:userId')
  async getProgress(@Param('userId') userId: string) {
    return await this.weightHistoryService.getProgress(userId);
  }

  @Get('history/:userId')
  async getWeightHistory(@Param('userId') userId: string) {
    return await this.weightHistoryService.getWeightHistory(userId);
  }

  @Get('chart/:userId')
  async getWeightHistoryForChart(@Param('userId') userId: string) {
    return await this.weightHistoryService.getWeightHistoryForChart(userId);
  }

  @Post()
  @ApiOperation({ summary: 'Create health metrics for the authenticated user' })
  @ApiResponse({
    status: 201,
    description: 'Health metrics created successfully.',
  })
  createHealthMetrics(
    @Request() req,
    @Body() createHealthMetricsDto: CreateHealthMetricsDto,
  ) {
    return this.healthMetricsService.createHealthMetrics(
      req.user.id,
      createHealthMetricsDto,
    );
  }

  @Get('weight-history-chart')
  async getWeightHistoryForChartForUser(@Request() req) {
    return await this.weightHistoryService.getWeightHistoryForChart(req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get health metrics for the authenticated user' })
  @ApiResponse({ status: 200, description: 'Return health metrics.' })
  getHealthMetrics(@Request() req) {
    return this.healthMetricsService.getHealthMetrics(req.user.id);
  }

  @Patch()
  @ApiOperation({ summary: 'Update health metrics for the authenticated user' })
  @ApiResponse({
    status: 200,
    description: 'Health metrics updated successfully.',
  })
  updateHealthMetrics(
    @Request() req,
    @Body() updateHealthMetricsDto: UpdateHealthMetricsDto,
  ) {
    return this.healthMetricsService.updateHealthMetrics(
      req.user.id,
      updateHealthMetricsDto,
    );
  }

  @Patch("/onboarding")
  @ApiOperation({ summary: 'Update health metrics for the authenticated user' })
  @ApiResponse({
    status: 200,
    description: 'Health metrics updated successfully.',
  })
  updateHealthMetricsOnBoarding(
    @Request() req,
    @Body() updateHealthMetricsDto: UpdateHealthMetricsOnBoardingDto,
  ) {
    console.log('Received onboarding data:', updateHealthMetricsDto);
    return this.healthMetricsService.updateHealthMetricsOnBoarding(
      req.user.id,
      updateHealthMetricsDto,
    );
  }

}
