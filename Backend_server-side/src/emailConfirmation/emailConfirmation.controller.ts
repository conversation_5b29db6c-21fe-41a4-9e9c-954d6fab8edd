import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  Post,
  Query,
  Res,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { EmailConfirmationService } from './emailConfirmation.service';
import { Public } from '../common/decorators/auth.decorators';
import { ConfigService } from '@nestjs/config';

@Controller('email-confirmation')
@ApiTags('email-confirmation')
@UseInterceptors(ClassSerializerInterceptor)
export class EmailConfirmationController {
  constructor(
    private readonly emailConfirmationService: EmailConfirmationService,
    private readonly configService: ConfigService,
  ) {}

  @Post('resend-confirmation-link')
  @Public()
  async resendConfirmationLink(@Body('email') email: string) {
    await this.emailConfirmationService.resendConfirmationLink(email);
  }

  @Get('confirm')
  @Public()
  async confirm(@Query('token') token: string, @Res() res) {
    try {
      const email = await this.emailConfirmationService.decodeConfirmationToken(token);
      console.log('Decoded email:', email);
      
      await this.emailConfirmationService.confirmEmail(email);
      console.log('Email confirmed successfully');
      
      // Redirect to the login page after successful confirmation
      return res.redirect(`${this.configService.get('FRONTEND_URL')}/login?emailConfirmed=true`);
    } catch (error) {
      console.error('Email confirmation error:', error);
      const errorMessage = error.message || 'Email confirmation failed';
      // Redirect to login with error parameter
      return res.redirect(`${this.configService.get('FRONTEND_URL')}/login?emailConfirmed=false&error=${encodeURIComponent(errorMessage)}`);
    }
  }
}
