import { Module } from '@nestjs/common';
import { SuperAdminsService } from './superAdmin.service';
import { SuperAdminsController } from './superAdmin.controller';
import { PasswordService } from '../auth/services/password.service';
import { JwtService } from '@nestjs/jwt';

@Module({
    providers: [SuperAdminsService, PasswordService, JwtService],
    controllers: [SuperAdminsController],
    exports: [SuperAdminsService]
})
export class SuperAdminsModule {}
