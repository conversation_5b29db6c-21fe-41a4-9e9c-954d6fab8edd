import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID, IsEmail, IsOptional } from 'class-validator';

export class CreateSuperAdminDto {
    @ApiProperty({ example: '<PERSON>' })
    @IsString()
    name: string;
  
    @ApiProperty({ example: '<EMAIL>' })
    @IsEmail()
    email: string;
  
    @ApiProperty({ example: 'securepassword123@Q' })
    @IsString()
    password: string;
  
    @ApiProperty({ example: '+1234567890', required: false })
    @IsOptional()
    @IsString()
    phone?: string;
  
  }

export class SuperAdminResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  superAdminId: string;

  @ApiProperty({ example: '<PERSON>' })
  @IsString()
  name: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ example: '+1234567890', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ example: 'active', enum: ['active', 'suspended', 'inactive'] })
  @IsString()
  status: string;

  @ApiProperty({ example: '2024-03-16T12:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ example: '2024-03-16T12:00:00.000Z' })
  updatedAt: Date;
}
