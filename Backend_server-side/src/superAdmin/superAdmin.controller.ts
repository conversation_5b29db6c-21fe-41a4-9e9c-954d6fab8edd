import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Request,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';
import { SuperAdminsService } from './superAdmin.service';
import { UpdateSuperAdminDto } from './dto/update-superAdmin';
import { ApiTags, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { CreateSuperAdminDto } from './dto/create-superAdmin.dto';
import { Public } from '../common/decorators/auth.decorators';
import { Role } from '../common/enums/permissions.enums';
import { Roles } from '../common/decorators/role.decorator';
import { SkipThrottle } from '@nestjs/throttler/dist';

@Controller('super-admins')
@ApiTags('super-admins')
export class SuperAdminsController {
  constructor(private readonly superAdminService: SuperAdminsService) {}

  @Post('create')
  @Public()
  async createSuperAdmin(@Body() superAdminData: CreateSuperAdminDto) {
    return this.superAdminService.createSuperAdmin(superAdminData);
  }

  @Put(':id')
  @Roles(Role.SUPERADMIN)
  async updateSuperAdmin(
    @Param('id') superAdminId: string,
    @Body() updateData: UpdateSuperAdminDto,
  ) {
    return this.superAdminService.updateSuperAdmin(superAdminId, updateData);
  }

  @Delete(':id')
  @Roles(Role.SUPERADMIN)
  async deleteSuperAdmin(@Param('id') superAdminId: string) {
    return this.superAdminService.deleteSuperAdmin(superAdminId);
  }

  @Get('profile')
  @SkipThrottle({default: true})
  @Roles(Role.SUPERADMIN)
  async getSuperAdminProfile(@Request() req) {
    return this.superAdminService.getProfile(req.user.id);
  }

  @Post('profile-image')
  @Roles(Role.SUPERADMIN)
  @UseInterceptors(
    FileInterceptor('profileImage', {
      storage: memoryStorage(),
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
          return cb(
            new BadRequestException('Only image files are allowed!'),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        profileImage: {
          type: 'string',
          format: 'binary',
          description: 'Profile image file (JPG, PNG, JPEG, GIF)',
        },
      },
    },
  })
  async uploadProfileImage(
    @Request() req,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.superAdminService.uploadProfileImage(req.user.id, file);
  }
}
