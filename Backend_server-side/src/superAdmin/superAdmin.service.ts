import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { eq, sql } from 'drizzle-orm';
import { superAdmins } from '../database/database-schema';
import { DrizzleService } from '../database/drizzle.service';
import { CreateSuperAdminDto } from './dto/create-superAdmin.dto';
import { UpdateSuperAdminDto } from './dto/update-superAdmin';
import { PasswordService } from '../auth/services/password.service';
import { JwtService } from '@nestjs/jwt';
import { CloudinaryService } from '../utils/cloudinary.service';
import { Logger } from '@nestjs/common';

@Injectable()
export class SuperAdminsService {
  private readonly logger = new Logger(SuperAdminsService.name);

  constructor(
    private readonly drizzle: DrizzleService,
    private readonly passowrdService: PasswordService,
    private readonly jwtService: JwtService,
    private readonly cloudinaryService: CloudinaryService,
  ) {}

  async createSuperAdmin(data: CreateSuperAdminDto) {
    const existingSuperAdmin = await this.drizzle.db.query.superAdmins.findFirst();

    if (existingSuperAdmin) throw new BadRequestException('A SuperAdmin already exists.');

    const hashedPassword = await this.passowrdService.hashPassword(data.password);

    await this.drizzle.db
      .insert(superAdmins)
      .values({ ...data, password: hashedPassword });

      return {
        message: "SuperAdmin created Sucessfully."
      }
  }

  async getSuperAdminById(superAdminId: string) {
    const superAdmin = await this.drizzle.db.query.superAdmins.findFirst({
      where: eq(superAdmins.superAdminId, superAdminId),
    });

    if (!superAdmin) throw new NotFoundException('Super Admin not found');
    return superAdmin;
  }

  async getSuperAdminByEmail(email: string) {
    const superAdmin = await this.drizzle.db.query.superAdmins.findFirst({
      where: eq(superAdmins.email, email),
    });
    
    if (!superAdmin) throw new NotFoundException('SuperAdmin not found');
    return superAdmin;
  }

  async updateSuperAdmin(superAdminId: string, updateData: UpdateSuperAdminDto) {
    console.log('Update Data:', updateData);
    return this.drizzle.db
      .update(superAdmins)
      .set(updateData)
      .where(eq(superAdmins.superAdminId, superAdminId));
  }

  async deleteSuperAdmin(superAdminId: string) {
    return this.drizzle.db
      .delete(superAdmins)
      .where(eq(superAdmins.superAdminId, superAdminId));
  }

  async getProfile(superAdminId: string) {
    const superAdmin = await this.drizzle.db.query.superAdmins.findFirst({
      where: eq(superAdmins.superAdminId, superAdminId),
    });

    if (!superAdmin) throw new NotFoundException('SuperAdmin not found');

    const { password, ...data } = superAdmin;
    return {
      ...data,
      profilePicture: superAdmin.profilePicture || null
    };
  }

  async uploadProfileImage(superAdminId: string, file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    const superAdmin = await this.drizzle.db.query.superAdmins.findFirst({
      where: (superAdmins, { eq }) => eq(superAdmins.superAdminId, superAdminId),
    });

    if (!superAdmin) {
      throw new NotFoundException('Super Admin not found');
    }

    try {
      // Upload new image to Cloudinary
      const uploadResult = await this.cloudinaryService.uploadImage(
        file.buffer,
        `superadmin_${superAdminId}_${Date.now()}`,
      );

      console.log('Upload result:', uploadResult);

      // Update the profile picture URL in the database
      await this.drizzle.db.execute(
        sql`UPDATE "SuperAdmins" SET profile_picture = ${uploadResult.secure_url} WHERE super_admin_id = ${superAdminId}`
      );

      return {
        message: 'Profile image uploaded successfully',
        profilePicture: uploadResult.secure_url,
      };
    } catch (error) {
      this.logger.error('Failed to upload profile image:', error);
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Failed to upload profile image'
      );
    }
  }
}
