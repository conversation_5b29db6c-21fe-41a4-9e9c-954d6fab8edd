import { Module, forwardRef } from '@nestjs/common';
import { Reflector } from '@nestjs/core'; // Import Reflector
import { JwtModule, JwtService } from '@nestjs/jwt';
import { UsersModule } from '../users/users.module';
import { AuthService } from './services/auth.service';
import { AuthController } from './auth.controller';
import { APP_GUARD } from '@nestjs/core';
import { AuthGuard } from '../common/guard/auth.guard';
import { RolesGuard } from '../common/guard/roles.guard';
import { PasswordService } from './services/password.service';
import { AdminsService } from '../admins/admins.service';
import { SuperAdminsService } from '../superAdmin/superAdmin.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EmailModule } from '../email/email.module';
import { EmailConfirmationService } from '../emailConfirmation/emailConfirmation.service';
import 'dotenv/config';
import {
  ThrottlerGuard,
  ThrottlerModule,
} from '@nestjs/throttler';
import { NotificationService } from '../notification/notification.service';
import { DrizzleModule } from '../database/drizzle.module';

@Module({
  imports: [
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => [
        {
          ttl: config.get('THROTTLE_TTL'),
          limit: config.get('THROTTLE_LIMIT'),
        },
      ],
    }),
    EmailModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        service: configService.get('EMAIL_SERVICE'),
        user: configService.get('EMAIL_USER'),
        password: configService.get('EMAIL_PASSWORD'),
      }),
    }),
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET,
    }),
    forwardRef(() => UsersModule),
    DrizzleModule,
  ],

  controllers: [AuthController],
  providers: [
    EmailConfirmationService,
    AuthService,
    PasswordService,
    AdminsService,
    NotificationService,
    SuperAdminsService,
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    Reflector, // Add Reflector here
  ],
  exports: [AuthService],
})
export class AuthModule {}
