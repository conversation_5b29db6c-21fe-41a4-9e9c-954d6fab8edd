import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { eq, sql } from 'drizzle-orm';
import { DrizzleService } from '../../database/drizzle.service';
import { UserLoginDto } from '../../users/dto/login-user.dto';
import { UserSignupDto } from '../../users/dto/signup-user.dto';
import { UsersService } from '../../users/users.service';
import { AccessTokenPayload, RefreshTokenPayload } from '../auth.interfaces';
import { Role } from '../../common/enums/permissions.enums';
import { SuperAdminsService } from '../../superAdmin/superAdmin.service';
import { AdminsService } from '../../admins/admins.service';
import { PasswordService } from './password.service';
import { SuperAdminLoginDto } from '../../superAdmin/dto/login-superAdmin.dto';
import { AdminLoginDto } from '../../admins/dto/login-admin.dot';
import { EmailConfirmationService } from '../../emailConfirmation/emailConfirmation.service';
import { admins, databaseSchema, users } from '../../database/database-schema';
import EmailService from '../../email/email.service';
import { ForgotPasswordDto } from '../dto/forgot-password.dto';
import { createResetPasswordEmail } from '../../email/templates/template';
import { User } from '../../googleAuth/dto/google.dto';
import { NotificationService } from '../../notification/notification.service';
import { NotificationType } from '../../common/enums/permissions.enums';
@Injectable()
export class AuthService {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly passwordService: PasswordService,
    private readonly adminsService: AdminsService,
    private readonly superAdminService: SuperAdminsService,
    private readonly emailConfirmationService: EmailConfirmationService,
    private readonly emailService: EmailService,
    private readonly notificationService: NotificationService,
  ) {}

  async generateJwtToken(payload: any): Promise<string> {
    return this.jwtService.signAsync(payload);
  }

  async changePassword(userRole: string, email:string, currentPassword: string, newPassword: string) {
    if (userRole === Role.SUPERADMIN) {
      const superAdmin = await this.superAdminService.getSuperAdminByEmail(email);
      if (!superAdmin) throw new NotFoundException('SuperAdmin not found');

      // Verify current password
      const isPasswordValid = await this.passwordService.comparePasswords(
        currentPassword,
        superAdmin.password,
      );

      if (!isPasswordValid) {
        throw new UnauthorizedException('Current password is incorrect');
      }

      const hashedPassword = await this.passwordService.hashPassword(newPassword);

      await this.drizzle.db.execute(sql`
        UPDATE "SuperAdmins"
        SET "password" = ${hashedPassword}
        WHERE "super_admin_id" = ${superAdmin.superAdminId};
      `);

      return {
        message: 'Password has been changed successfully',
      };
    }
    if (userRole === Role.ADMIN) {
      const admin = await this.adminsService.getAdminByEmail(email);
      if (!admin) throw new NotFoundException('Admin not found');

      // Verify current password
      const isPasswordValid = await this.passwordService.comparePasswords(
        currentPassword,
        admin.password,
      );

      if (!isPasswordValid) {
        throw new UnauthorizedException('Current password is incorrect');
      }

      const hashedPassword = await this.passwordService.hashPassword(newPassword);

      await this.drizzle.db.execute(sql`
        UPDATE "Admins"
        SET "password" = ${hashedPassword}
        WHERE "admin_id" = ${admin.adminId};
      `);

      return {
        message: 'Password has been changed successfully',
      };
    }
    if (userRole === Role.USER) {
      const user = await this.usersService.findOneByEmail(email);
      if (!user) throw new NotFoundException('User not found');

      // Verify current password
      const isPasswordValid = await this.passwordService.comparePasswords(
        currentPassword,
        user.password,
      );

      if (!isPasswordValid) {
        throw new UnauthorizedException('Current password is incorrect');
      }

      const hashedPassword = await this.passwordService.hashPassword(newPassword);

      await this.drizzle.db.execute(sql`
        UPDATE "Users"
        SET "password" = ${hashedPassword}
        WHERE "user_id" = ${user.userId};
      `);

      return {
        message: 'Password has been changed successfully',
      };
    }
  }

  async getCookieWithJwtAccessToken(
    user: User,
  ): Promise<{ token: string }> {
    const accessTokenPayload: AccessTokenPayload = {
      id: user.userId,
      email: user.email,
      roles: [Role.USER],
    };

    const accessToken = await this.jwtService.signAsync(accessTokenPayload);

    return { token: accessToken };
  }
  async getCookieWithJwtRefreshToken(
    user: User,
  ): Promise<{ token: string }> {
    const refreshTokenPayload: RefreshTokenPayload = {
      email: user.email,
      roles: [Role.USER],
    };
    const refreshToken = await this.jwtService.signAsync(refreshTokenPayload);

    return {token: refreshToken };
  }

  async userLogin(data: UserLoginDto): Promise<any> {
    const user = await this.usersService.getUserByEmail(data.email);

    if (!user) {
      throw new BadRequestException('User not found');
    }
    
    if (user.isDeleted) {
      throw new BadRequestException('User deleted');
    }

    if (!user.isEmailConfirmed) {
      throw new BadRequestException('Email not confirmed');
    }

    if (user.isRegisteredWithGoogle){
      throw new BadRequestException('User registered with Google. Please login with Google.');
    }

    const isPasswordValid = await this.passwordService.validateUser(
      data.password,
      user.password,
    );

    if (!isPasswordValid) {
      throw new BadRequestException('Invalid password');
    }

    const accessTokenPayload: AccessTokenPayload = {
      id: user.userId,
      email: user.email,
      roles: [Role.USER],
    };

    const refreshTokenPayload: RefreshTokenPayload = {
      email: user.email,
      roles: [Role.USER],
    };

    const accessToken = await this.jwtService.signAsync(accessTokenPayload);

    const refreshToken = await this.jwtService.signAsync(refreshTokenPayload);

    await this.usersService.setRefreshToken(user.userId, refreshToken, [
      Role.USER,
    ]);
    const last_login = new Date();
    await this.drizzle.db.execute(sql`
      UPDATE "Users"
      SET "last_login" = ${last_login}
      WHERE "user_id" = ${user.userId};
    `);

    return {
      accessToken,
      refreshToken,
    };
  }

  async AdminLogin(data: AdminLoginDto): Promise<any> {
    const admin = await this.adminsService.getAdminByEmail(data.email);
    
    if (!admin) {
      throw new BadRequestException('Admin not found');
    }

    const isPasswordValid = await this.passwordService.validateUser(
      data.password,
      admin.password,
    );
    
    if (!isPasswordValid) {
      throw new BadRequestException('Invalid password');
    }
    
    console.log('Admin login attempt:', data);
    const accessTokenPayload: AccessTokenPayload = {
      id: admin.adminId,
      email: admin.email,
      roles: [Role.ADMIN],
    };

    const refreshTokenPayload: RefreshTokenPayload = {
      email: admin.email,
      roles: [Role.ADMIN],
    };

    const accessToken = await this.jwtService.signAsync(accessTokenPayload);

    const refreshToken = await this.jwtService.signAsync(refreshTokenPayload);

    await this.usersService.setRefreshToken(admin.adminId, refreshToken, [
      Role.ADMIN,
    ]);

    return {
      accessToken,
      refreshToken,
    };
  }

  async superAdminLogin(data: SuperAdminLoginDto): Promise<any> {
    const superAdmin = await this.superAdminService.getSuperAdminByEmail(
      data.email,
    );

    if (!superAdmin) {
      throw new BadRequestException('SuperAdmin not found');
    }

    const isPasswordValid = await this.passwordService.validateUser(
      data.password,
      superAdmin.password,
    );

    if (!isPasswordValid) {
      throw new BadRequestException('Invalid password');
    }

    const accessTokenPayload: AccessTokenPayload = {
      id: superAdmin.superAdminId,
      email: superAdmin.email,
      roles: [Role.SUPERADMIN],
    };

    const refreshTokenPayload: RefreshTokenPayload = {
      email: superAdmin.email,
      roles: [Role.SUPERADMIN],
    };

    const accessToken = await this.jwtService.signAsync(accessTokenPayload);

    const refreshToken = await this.jwtService.signAsync(refreshTokenPayload);

    await this.usersService.setRefreshToken(
      superAdmin.superAdminId,
      refreshToken,
      [Role.SUPERADMIN],
    );

    return {
      accessToken,
      refreshToken,
    };
  }

  async registerUser(data: UserSignupDto): Promise<any> {
    const userByEmail = await this.usersService.findOneByEmail(data.email);

    if (userByEmail) {
      throw new BadRequestException('Email already exists');
    }

    const hashedPassword = await this.passwordService.hashPassword(
      data.password,
    );

    const response = await this.emailConfirmationService.sendVerificationLink(
      data.email,
    );

    if (!response.res.success) {
      throw new BadRequestException('Failed to send email confirmation link');
    }

    await this.drizzle.db.execute(sql`
      insert into "Users" ("name", "email", "password")
      values (${data.name}, ${data.email}, ${hashedPassword});
    `);

    const adminData = await this.adminsService.getAllAdmins( {})
      for (const admin of adminData.admins) {
        await this.notificationService.create({
          recipientId: admin.adminId,
          message: 'A new user has registered. Please verify their information!',
          type: NotificationType.USER_ACTION,
        });
      }

    return {
      token: response.token,
    };
  }

  async updateAccessToken(refreshToken: string) {
    const data = this.jwtService.decode(refreshToken) as RefreshTokenPayload;

    if (!data || !data.email)
      throw new UnauthorizedException('Refresh token not found');

    let userOrAdmin;
    let role: Role[];
    if (data.roles.includes(Role.ADMIN)) {
      userOrAdmin = await this.drizzle.db.query.admins.findFirst({
        where: eq(databaseSchema.admins.email, data.email),
      });
      role = [Role.ADMIN];
    } else if (data.roles.includes(Role.USER)) {
      userOrAdmin = await this.drizzle.db.query.users.findFirst({
        where: eq(databaseSchema.users.email, data.email),
      });
      role = [Role.USER];
    } else if (data.roles.includes(Role.SUPERADMIN)) {
      userOrAdmin = await this.drizzle.db.query.superAdmins.findFirst({
        where: eq(databaseSchema.superAdmins.email, data.email),
      });
      role = [Role.SUPERADMIN];
    } else {
      throw new UnauthorizedException('Role not recognized');
    }

    if (!userOrAdmin) throw new NotFoundException('User or admin not found');
    if (!userOrAdmin.refreshToken) {
      throw new BadRequestException('Refresh token not provided');
    }
    const isValidRefreshToken = await this.passwordService.validateUser(
      refreshToken,
      userOrAdmin.refreshToken,
    );
    if (!isValidRefreshToken)
      throw new UnauthorizedException('Invalid refresh token');

    const payload: AccessTokenPayload = {
      id: userOrAdmin.userId,
      email: userOrAdmin.email,
      roles: role,
    };

    const accessToken = await this.jwtService.signAsync(payload);

    return accessToken;
  }

  async validateRefreshToken(pid: string, refreshToken: string, roles: Role[]) {
    let userOrAdmin;

    if (roles.includes(Role.ADMIN)) {
      userOrAdmin = await this.drizzle.db.query.admins.findFirst({
        where: eq(databaseSchema.admins.adminId, pid),
      });
    } else if (roles.includes(Role.USER)) {
      userOrAdmin = await this.drizzle.db.query.users.findFirst({
        where: eq(databaseSchema.users.userId, pid),
      });
    } else if (roles.includes(Role.SUPERADMIN)) {
      userOrAdmin = await this.drizzle.db.query.superAdmins.findFirst({
        where: eq(databaseSchema.superAdmins.superAdminId, pid),
      });
    } else {
      throw new UnauthorizedException('Role not recognized');
    }

    if (!userOrAdmin) throw new NotFoundException('No user found');
    if (
      !(await this.passwordService.validateUser(
        refreshToken,
        userOrAdmin.refreshToken,
      ))
    )
      throw new UnauthorizedException('refresh token not valid');
    return true; // Previuosly, this method returned the user or admin
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    const { email } = forgotPasswordDto;

    const user = await this.usersService.findOneByEmail(email);
    if (!user) {
      throw new NotFoundException('User with this email does not exist');
    }

    if (user.resetTokenExpiry && new Date() < new Date(user.resetTokenExpiry)) {
      throw new BadRequestException(
        'A password reset link has already been sent. Please try again later.',
      );
    }

    const resetToken = await this.jwtService.signAsync(
      {
        sub: user.id,
        email: user.email,
      },
      {
        expiresIn: '1h',
        secret: process.env.PASSWORD_RESET_SECRET,
      },
    );

    const now = new Date();
    const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);

    await this.drizzle.db.execute(sql`
        UPDATE "Users"
        SET "reset_token" = ${resetToken}, "reset_token_expiry" = ${oneHourFromNow}
        WHERE "email" = ${user.email};
      `);

    const resetLink = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

    await this.emailService.sendMail1({
      to: email,
      subject: 'Password Reset Request',
      html: createResetPasswordEmail(resetLink).html,
    });

    return {
      message:
        'If a user with this email exists, a password reset link has been sent.',
    };
  }

  async resetPassword(token: string, newPassword: string) {
    try {
      // Verify the token
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.PASSWORD_RESET_SECRET,
      });

      const user = await this.drizzle.db.query.users.findFirst({
        where: eq(users.email, payload.email),
      });

      if (!user || user.resetToken !== token) {
        throw new BadRequestException('Invalid or expired reset token');
      }

      if (
        !user.resetTokenExpiry ||
        new Date() > new Date(user.resetTokenExpiry)
      ) {
        throw new BadRequestException('Reset token has expired');
      }

      // Hash the new password
      const hashedPassword =
        await this.passwordService.hashPassword(newPassword);

      await this.drizzle.db.execute(sql`
        UPDATE "Users"
        SET "password" = ${hashedPassword}, "reset_token" = null, "reset_token_expiry" = null
        WHERE "email" = ${payload.email};
      `);

      return {
        message: 'Password has been reset successfully',
      };
    } catch (error) {
      if (error?.name === 'JsonWebTokenError') {
        throw new BadRequestException('Invalid reset token');
      }
      if (error?.name === 'TokenExpiredError') {
        throw new BadRequestException('Reset token has expired');
      }
      throw error;
    }
  }
}
