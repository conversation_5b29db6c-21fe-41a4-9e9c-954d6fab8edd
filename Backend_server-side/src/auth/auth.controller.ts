import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Post,
  Req,
  Request,
  Response,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { RegisterUserResponseDto } from '../users/dto/register-user.dto';
import { AuthService } from './services/auth.service';
import { UserLoginDto } from '../users/dto/login-user.dto';
import { Public } from '../common/decorators/auth.decorators';
import { SuperAdminLoginDto } from '../superAdmin/dto/login-superAdmin.dto';
import { EmailConfirmationService } from '../emailConfirmation/emailConfirmation.service';
import { SkipThrottle } from '@nestjs/throttler';
import { ForgotPasswordDto, ResetPasswordDto } from './dto/forgot-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { UserSignupDto } from '../users/dto/signup-user.dto';
import { cookiePresets, setCookie } from '../common/utils/cookie.utils';
import { AdminLoginDto } from '../admins/dto/login-admin.dot';

@Controller('auth')
@ApiTags('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly emailConfirmationService: EmailConfirmationService,
  ) {}

  @Post('register')
  @Public()
  async register(@Body() registerBody: UserSignupDto): Promise<RegisterUserResponseDto> {
    const response = await this.authService.registerUser(registerBody);
    return {
      message: 'User registered successfully. Verification Link sent to your email.',
      token: response.token,
    };
  }

  @Post('userLogin')
  @Public()
  async login(
    @Body() loginDto: UserLoginDto,
    @Response({ passthrough: true }) res,
    @Request() req,
  ): Promise<any> {
    const { refreshToken, accessToken } = await this.authService.userLogin(loginDto);
    const userRole = 'USER';

    setCookie(res, 'refresh_token', refreshToken, cookiePresets.refreshToken);
    setCookie(res, 'access_token', accessToken, cookiePresets.accessToken);
    setCookie(res, 'user_role', userRole, cookiePresets.userRole);
    
    return { 
      accessToken, 
      refreshToken,
      userRole,
      message: 'Login successful'
    };
  }

  @Post('change-password')
  async changePassword(@Body() changePasswordDto: ChangePasswordDto, @Req() req) {
    return this.authService.changePassword(
      req.user.roles[0],
      changePasswordDto.email,
      changePasswordDto.currentPassword,
      changePasswordDto.newPassword,
    );
  }

  @Post('AdminLogin')
  @Public()
  async adminLogin(
    @Body() loginDto: AdminLoginDto,
    @Response({ passthrough: true }) res,
    @Req() req,
  ) {
    let refreshToken;
    let accessToken;
    let userRole = 'ADMIN';
    if (loginDto.email === '<EMAIL>' && loginDto.password === 'securepassword123') {
      const result = await this.authService.superAdminLogin(loginDto);
      refreshToken = result.refreshToken;
      accessToken = result.accessToken;
      userRole = 'SUPERADMIN';
    }
    else {
      const result = await this.authService.AdminLogin(loginDto);
      refreshToken = result.refreshToken;
      accessToken = result.accessToken;
    }

    setCookie(res, 'refresh_token', refreshToken, cookiePresets.refreshToken);
    setCookie(res, 'access_token', accessToken, cookiePresets.accessToken);
    setCookie(res, 'user_role', userRole, cookiePresets.userRole);

    return { 
      accessToken, 
      refreshToken,
      userRole,
      message: 'Login successful'
    };
  }

  @Post('superAdminLogin')
  @Public()
  async superAdminLogin(
    @Body() loginDto: SuperAdminLoginDto,
    @Response({ passthrough: true }) res,
    @Request() req,
  ) {
    const { refreshToken, accessToken } = await this.authService.superAdminLogin(loginDto);
    const userRole = 'SUPERADMIN';

    setCookie(res, 'refresh_token', refreshToken, cookiePresets.refreshToken);
    setCookie(res, 'access_token', accessToken, cookiePresets.accessToken);
    setCookie(res, 'user_role', userRole, cookiePresets.userRole);

    return { 
      accessToken, 
      refreshToken,
      userRole,
      message: 'Login successful'
    };
  }

  @SkipThrottle({ default: true })
  @Get('check-cookies')
  @Public()
  async checkCookies(@Req() req) {
    const cookies = req.cookies || {};
    
    if (!cookies.access_token || !cookies.refresh_token) {
      throw new HttpException('No cookies found', HttpStatus.NOT_FOUND);
    }

    return {
      access_token: cookies.access_token,
      refresh_token: cookies.refresh_token,
      user_role: cookies.user_role
    };
  }

  @Get('check-user')
  @SkipThrottle({ default: true })
  async checkUser(@Req() req) {
    return req.user;
  }

  @Get('refresh')
  @Public()
  @SkipThrottle({ default: true })
  async refreshAccessToken(
    @Req() req,
    @Response({ passthrough: true }) res,
  ) {
    const cookies = req.cookies ?? req.signedCookies;
    const refreshToken = cookies.refresh_token;
  

    if (!refreshToken) {
      throw new HttpException('Refresh token not found', HttpStatus.UNAUTHORIZED);
    }

    try {
      if (!cookies.access_token) {
        const accessToken = await this.authService.updateAccessToken(refreshToken);
        
        setCookie(res, 'access_token', accessToken, cookiePresets.accessToken);
        setCookie(res, 'user_role', 'USER', cookiePresets.userRole);

        return { message: 'Access token refreshed', accessToken };
      }
      return { message: 'Access token is still valid' };
    } catch (error) {
      throw new HttpException('Failed to refresh access token', HttpStatus.UNAUTHORIZED);
    }
  }

  @Post('forgot-password')
  @Public()
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  @Post('reset-password')
  @Public()
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(
      resetPasswordDto.token,
      resetPasswordDto.newPassword,
    );
  }

  @Post('logout')
  async logout(@Response() res) {
    try {
      const cookieOptions = {
        sameSite: 'None' as const,
        secure: true,
        path: '/',
        domain: process.env.COOKIE_DOMAIN,
        httpOnly: true
      };

      // Clear cookies synchronously
      res.clearCookie('refresh_token', cookieOptions);
      res.clearCookie('access_token', cookieOptions);
      res.clearCookie('user_role', cookieOptions);
      
      // Send response immediately
      return res.status(200).json({ message: 'Logged out successfully' });
    } catch (error) {
      console.error('Logout error:', error);
      return res.status(500).json({ message: 'Error during logout' });
    }
  }
}