import { DynamicModule, Module } from '@nestjs/common';
import EmailService from './email.service';
import { EMAIL_CONFIG_OPTIONS } from './email.module-definition';
import EmailOptions from './emailOptions.interface';

@Module({})
export class EmailModule {
  static register(options: EmailOptions): DynamicModule {
    return {
      module: EmailModule,
      providers: [
        {
          provide: EMAIL_CONFIG_OPTIONS,
          useValue: options,
        },
        EmailService,
      ],
      exports: [EmailService], // Make sure EmailService is exported
    };
  }

  static registerAsync(options: any): DynamicModule {
    return {
      module: EmailModule,
      imports: options.imports || [],
      providers: [
        {
          provide: EMAIL_CONFIG_OPTIONS,
          useFactory: options.useFactory,
          inject: options.inject || [],
        },
        EmailService,
      ],
      exports: [EmailService], // Make sure EmailService is exported
    };
  }
}
