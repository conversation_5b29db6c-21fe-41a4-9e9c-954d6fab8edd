export const createVerificationEmail = (url: string) => ({
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body>
        <p>Welcome to NutriFocus!</p>
        <p>Please verify your email by clicking the link below:</p>
        <a href="${url}">Verify Email</a>
        <p>This link expires in 1 hours.</p>
      </body>
    </html>
  `
});

export const createResetPasswordEmail = (url: string) => ({
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body>
        <h3>Password Reset Request</h3>
        <p>Please click the link below to reset your password. This link will expire in 1 hour.</p>
        <a href="${url}">Reset Password</a>
        <p>If you didn't request this, please ignore this email.</p>
      </body>
    </html>
  `
});
