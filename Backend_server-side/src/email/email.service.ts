import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import axios from 'axios';
import { createTransport } from 'nodemailer';
import Mail from 'nodemailer/lib/mailer';
import { EMAIL_CONFIG_OPTIONS } from './email.module-definition';
import EmailOptions from './emailOptions.interface';

@Injectable()
export default class EmailService {
  private nodemailerTransport: Mail;

  constructor(@Inject(EMAIL_CONFIG_OPTIONS) private options: EmailOptions) {
    this.nodemailerTransport = createTransport({
      service: options.service,
      auth: {
        user: options.user,
        pass: options.password,
      },
      pool: true,
      maxConnections: 1,
      maxMessages: 3,
      socketTimeout: 10000,
      connectionTimeout: 10000,
      greetingTimeout: 10000,
      debug: true,
      logger: true
    });
  }

  // Function to validate email using Hunter.io API
  private async validateEmailWithHunter(email: string): Promise<boolean> {
    const apiKey = process.env.HUNTER_API_KEY;
    try {
      const response = await axios.get(
        'https://api.hunter.io/v2/email-verifier',
        {
          params: {
            email: email,
            api_key: apiKey,
          },
        },
      );

      // Hunter.io response data
      const { data } = response;

      // Check the verification result
      if (data && data.data && data.data.result === 'deliverable') {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      return false;
    }
  }

  async sendMail1(
    options: Mail.Options,
  ): Promise<{ success: boolean; message: string }> {
    const email = options.to as string;

    const isValidEmail = await this.validateEmailWithHunter(email); // Validate recipient email before sending using Hunter.io

    if (!isValidEmail) {
      throw new BadRequestException(
        'Email address is invalid or not deliverable.',
      );
    }

    try {
      // Attempt to send the email
      const info = await this.nodemailerTransport.sendMail({
        ...options,
        from: this.options.user, // Add sender email
      });
      console.log('Email sent successfully:', info);
      // Inspect the SMTP response for clues about delivery issues
      if (info.rejected.length > 0) {
        throw new BadRequestException(
          `Failed to send email to the following addresses: ${info.rejected.join(', ')}`
        );
      }
      return { success: true, message: 'Email sent successfully.' };
    } catch (error) {
      console.error('Email sending error:', error);
      throw new BadRequestException(
        `Failed to send email: ${error.message}`
      );
    }
  }

  // New method for sending critical notifications via email
  async sendCriticalNotificationEmail(
    email: string,
    subject: string,
    message: string,
  ): Promise<{ success: boolean; message: string }> {
    return this.sendMail1({
      to: email,
      subject: subject,
      text: message,
      html: `<p>${message}</p><p>Please log in to your NutriFocus account for more details.</p>`
    });
  }

  // New method for sending health alerts
  async sendHealthAlert(
    email: string,
    message: string,
  ): Promise<{ success: boolean; message: string }> {
    return this.sendMail1({
      to: email,
      subject: 'Important Health Update - NutriFocus',
      text: message,
      html: `<p>${message}</p><p>Please log in to your NutriFocus account for more details.</p>`
    });
  }

  // New method for sending weight update reminders
  async sendWeightUpdateReminder(
    email: string,
    daysMissed: number,
  ): Promise<{ success: boolean; message: string }> {
    const message = `Important: Your weight hasn't been updated in over ${daysMissed} days. Regular tracking is essential for accurate health recommendations.`;
    
    return this.sendMail1({
      to: email,
      subject: 'Weight Tracking Reminder - NutriFocus',
      text: message,
      html: `<p>${message}</p><p>Please log in to your NutriFocus account to update your weight.</p>`
    });
  }
}
