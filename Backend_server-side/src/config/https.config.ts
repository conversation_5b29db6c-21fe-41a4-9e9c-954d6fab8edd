import * as fs from 'fs';
import * as path from 'path';
import { INestApplication } from '@nestjs/common';
import * as https from 'https';

export interface HttpsConfig {
  keyPath: string;
  certPath: string;
  port: number;
}

export const defaultHttpsConfig: HttpsConfig = {
  keyPath: path.join(process.cwd(), 'src/certs/private.key'),
  certPath: path.join(process.cwd(), 'src/certs/certificate.crt'),
  port: 4000,
};

export function setupHttps(app: INestApplication, config: HttpsConfig = defaultHttpsConfig) {
  try {
    const httpsOptions = {
      key: fs.readFileSync(config.keyPath),
      cert: fs.readFileSync(config.certPath),
    };

    const server = https.createServer(httpsOptions, app.getHttpAdapter().getInstance());
    
    return new Promise<void>((resolve) => {
      server.listen(config.port, () => {
        console.log(`🚀 HTTPS Server running on port ${config.port}`);
        resolve();
      });
    });
  } catch (error) {
    console.error('Failed to setup HTTPS server:', error);
    throw error;
  }
} 