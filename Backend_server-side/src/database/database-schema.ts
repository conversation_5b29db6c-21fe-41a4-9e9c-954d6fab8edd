import {
  boolean,
  jsonb,
  numeric,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { pgTable } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

export const users = pgTable('Users', {
  userId: uuid('user_id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 256 }).notNull(),
  email: varchar('email', { length: 256 }).notNull().unique(),
  password: varchar('password'),
  phone: varchar('phone', { length: 256 }).unique(),
  status: varchar('status').default('pending').notNull(),
  isEmailConfirmed: boolean('is_email_confirmed').default(false).notNull(),
  confirmationSentAt: timestamp('confirmation_sent_at').defaultNow(),
  isRegisteredWithGoogle: boolean('is_registered_with_google').default(false),
  profilePicture: varchar('profile_picture', { length: 512 }),
  lastLogin: timestamp('last_login'),
  refreshToken: varchar('refresh_token'),
  resetToken: varchar('reset_token'),
  resetTokenExpiry: timestamp('reset_token_expiry'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const userHealthMetrics = pgTable('UserHealthMetrics', {
  healthMetricId: uuid('health_metric_id').defaultRandom().primaryKey(),
  userId: uuid('user_id')
    .references(() => users.userId)
    .notNull(),
  age: numeric('age').notNull(),
  gender: varchar('gender', { length: 10 }).notNull(),
  height: numeric('height').notNull(), // in cm
  weight: numeric('weight').notNull(), // in kg
  activityLevel: varchar('activity_level', { length: 50 }).notNull(),
  dietaryPreference: varchar('dietary_preference', { length: 50 }).notNull(),
  weightGoal: varchar('weight_goal', { length: 50 }).notNull(),
  startingWeight: numeric('starting_weight'),
  targetWeight: numeric('target_weight'),
  goalUpdatedAt: timestamp('goal_updated_at'), // Track goal changes
  healthIssues: jsonb('health_issues').$type<[string]>(), // Comma-separated or JSON
  fasting: varchar('fasting').default('No').notNull(),
  location: varchar('location', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const weightHistory = pgTable('WeightHistory', {
  entryId: uuid('entry_id').defaultRandom().primaryKey(),
  userId: uuid('user_id').notNull(),
  weight: numeric('weight').notNull(),
  isGoalEntry: boolean('is_goal_entry').default(false).notNull(),
  weightGoal: varchar('weight_goal', { length: 50 }),
  targetWeight: numeric('target_weight'),
  startingWeight: numeric('starting_weight'),
  recordedAt: timestamp('recorded_at').defaultNow().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});
// relations
export const usersRelations = relations(users, ({ one, many }) => ({
  healthMetrics: one(userHealthMetrics, {
    fields: [users.userId],
    references: [userHealthMetrics.userId],
  }),
  weightEntries: many(weightHistory),
}));

export const userHealthMetricsRelations = relations(
  userHealthMetrics,
  ({ one }) => ({
    user: one(users, {
      fields: [userHealthMetrics.userId],
      references: [users.userId],
    }),
  }),
);

export const admins = pgTable('Admins', {
  adminId: uuid('admin_id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 256 }).notNull(),
  email: varchar('email', { length: 256 }).notNull().unique(),
  password: varchar('password').notNull(),
  phone: varchar('phone', { length: 20 }).unique(),
  role: varchar('role', { length: 256 }).notNull().default('ADMIN'),
  status: varchar('status', { length: 50 }).default('active'),
  location: varchar('location', { length: 255 }),
  refreshToken: varchar('refresh_token'),
  profilePicture: varchar('profile_picture', { length: 512 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const superAdmins = pgTable('SuperAdmins', {
  superAdminId: uuid('super_admin_id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 256 }).notNull(),
  email: varchar('email', { length: 256 }).notNull().unique(),
  password: varchar('password').notNull(),
  phone: varchar('phone', { length: 20 }).unique(),
  role: varchar('role', { length: 256 }).notNull().default('SUPERADMIN'),
  status: varchar('status', { length: 50 }).default('active'),
  location: varchar('location', { length: 255 }),
  profilePicture: varchar('profile_picture', { length: 512 }),
  refreshToken: varchar('refresh_token'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const foodLogs = pgTable('FoodLogs', {
  logId: uuid('log_id').defaultRandom().primaryKey(),
  userId: uuid('user_id')
    .references(() => users.userId)
    .notNull(),
  foodName: varchar('food_name', { length: 256 }).notNull(),
  baseIngredient: varchar('base_ingredient', { length: 256 }),
  preparation: varchar('preparation', { length: 256 }),
  calories: numeric('calories'),
  nutrients: jsonb('nutrients').$type<{
    protein: number;
    fat: number;
    carbohydrates: number;
    fiber: number;
    ash: number;
    calcium: number;
    zinc: number;
    copper: number;
    sodium: number;
    thiamine: number;
    riboflavin: number;
    ascorbicAcid: number;
  }>(),
  timestamp: timestamp('timestamp').defaultNow().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const blogs = pgTable(
  'Blogs',
  {
    blogId: uuid('blog_id').defaultRandom().primaryKey(),
    title: varchar('title', { length: 256 }).notNull(),
    content: text('content').notNull(),
    featuredImage: varchar('featured_image', { length: 512 }),
    category: varchar('category', { length: 100 }).notNull(),
    authorId: uuid('author').notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  }
);

// Relations (updated for Blogs)
export const blogsRelations = relations(blogs, ({ one }) => ({
  adminAuthor: one(admins, {
    fields: [blogs.authorId],
    references: [admins.adminId],
  }),
  superAdminAuthor: one(superAdmins, {
    fields: [blogs.authorId],
    references: [superAdmins.superAdminId],
  }),
}));

export const feedback = pgTable('Feedback', {
  feedbackId: uuid('feedback_id').defaultRandom().primaryKey(),
  userId: uuid('user_id')
    .references(() => users.userId)
    .notNull(),
  message: text('message').notNull(),
  feedbackType: varchar('feedback_type', { length: 50 })
    .default('general')
    .notNull(),
  rating: numeric('rating'),
  status: varchar('status', { length: 50 }).default('Submitted').notNull(),
  adminResponse: text('admin_response'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const notifications = pgTable('Notifications', {
  notificationId: uuid('notification_id').defaultRandom().primaryKey(),
  recipientId: uuid('recipient_id').notNull(), // Can be userId, adminId, or superAdminId
  message: text('message').notNull(),
  type: varchar('type', { length: 100 }).notNull(), // Blog Update, Feedback Alert, User Action, etc.
  isRead: boolean('is_read').default(false).notNull(),
  timestamp: timestamp('timestamp').defaultNow().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  from: varchar('from', { length: 255 }).notNull().default('System'),
  category: varchar('category', { length: 100 }),
});

export const ethiopianFoods = pgTable('EthiopianFoods', {
  foodId: uuid('food_id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 256 }),
  localName: varchar('local_name', { length: 256 }),
  description: text('description'),
  category: varchar('category', { length: 100 }),
  region: varchar('region', { length: 100 }),
  baseIngredient: varchar('base_ingredient', { length: 256 }),
  preparationMethod: text('preparation_method'),
  calories: numeric('calories'),
  nutrients: jsonb('nutrients').$type<{
    protein: number;
    fat: number;
    carbohydrates: number;
    fiber: number;
    calcium: number; 
    sodium: number;
  }>(),
  servingSize: varchar('serving_size', { length: 100 }), // e.g., "100g", "1 piece"
  culturalSignificance: text('cultural_significance'),
  healthBenefits: text('health_benefits'),
  nutritionalContext: text('nutritional_context'),
  highlights: text('highlights'),
  image: varchar('image', { length: 512 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const recommendations = pgTable('Recommendations', {
  recommendationId: uuid('recommendation_id').defaultRandom().primaryKey(),
  userId: uuid('user_id')
    .references(() => users.userId)
    .notNull(),
  healthMetricId: uuid('health_metric_id')
    .references(() => userHealthMetrics.healthMetricId)
    .notNull(),
  predictions: jsonb('predictions')
    .$type<{
      Breakfast: {
        local_name: string;
        ingredient: string;
        location: string;
        region: string;
        category: string;
        nutrition: {
          calories: number;
          protein: number;
          fat: number;
          carbohydrates: number;
          fiber: number;
          sodium: number;
        };
        serving_size_grams: number;
        serving_nutrition: {
          calories: number;
          protein: number;
          fat: number;
          carbohydrates: number;
          fiber: number;
          sodium: number;
        };
      }[];
      Lunch: {
        local_name: string;
        ingredient: string;
        location: string;
        region: string;
        category: string;
        nutrition: {
          calories: number;
          protein: number;
          fat: number;
          carbohydrates: number;
          fiber: number;
          sodium: number;
        };
        serving_size_grams: number;
        serving_nutrition: {
          calories: number;
          protein: number;
          fat: number;
          carbohydrates: number;
          fiber: number;
          sodium: number;
        };
      }[];
      Snack: {
        local_name: string;
        ingredient: string;
        location: string;
        region: string;
        category: string;
        nutrition: {
          calories: number;
          protein: number;
          fat: number;
          carbohydrates: number;
          fiber: number;
          sodium: number;
        };
        serving_size_grams: number;
        serving_nutrition: {
          calories: number;
          protein: number;
          fat: number;
          carbohydrates: number;
          fiber: number;
          sodium: number;
        };
      }[];
      Dinner: {
        local_name: string;
        ingredient: string;
        location: string;
        region: string;
        category: string;
        nutrition: {
          calories: number;
          protein: number;
          fat: number;
          carbohydrates: number;
          fiber: number;
          sodium: number;
        };
        serving_size_grams: number;
        serving_nutrition: {
          calories: number;
          protein: number;
          fat: number;
          carbohydrates: number;
          fiber: number;
          sodium: number;
        };
      }[];
    }>()
    .notNull(),
  nutritionTargets: jsonb('nutrition_targets')
    .$type<{
      daily_calorie_target: number;
      protein: number;
      sugar: number;
      sodium: number;
      calories: number;
      carbohydrates: number;
      fiber: number;
      fat: number;
    }>()
    .notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const databaseSchema = {
  users,
  admins,
  superAdmins,
  foodLogs,
  blogs,
  feedback,
  notifications,
  ethiopianFoods,
  userHealthMetrics,
  recommendations,
  weightHistory,
  blogsRelations,
};
