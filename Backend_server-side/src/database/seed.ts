import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { databaseSchema } from './database-schema';
import 'dotenv/config';

import { faker } from '@faker-js/faker';

import { ConfigService } from '@nestjs/config';

const configService = new ConfigService();

const main = async () => {
  const pool = new Pool({
    host: configService.get('POSTGRES_HOST'),
    port: configService.get('POSTGRES_PORT'),
    user: configService.get('POSTGRES_USER'),
    password: configService.get('POSTGRES_PASSWORD'),
    database: configService.get('POSTGRES_DB'),
    ssl: configService.get('POSTGRES_SSL') === 'true' ? {
      rejectUnauthorized: false
    } : undefined
  });

  const db = drizzle(pool);

  // Seed SuperAdmins first
  console.log('Seeding SuperAdmins...');
  const superAdminData = [];
  for (let i = 0; i < 3; i++) {
    superAdminData.push({
      superAdminId: faker.string.uuid(),
      name: faker.person.fullName(),
      email: faker.internet.email(),
      password: faker.internet.password(),
      phone: `+251-9${faker.number.int({ min: 100000000, max: 999999999 }).toString().slice(1)}`,
      role: 'SUPERADMIN',
      status: 'active',
      location: faker.helpers.arrayElement(['Addis Ababa', 'Bale', 'Gondar', 'Hawassa']),
      profilePicture: faker.image.avatar(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }
  await db.insert(databaseSchema.superAdmins).values(superAdminData).returning();

  // Seed Admins
  console.log('Seeding Admins...');
  const adminData = [];
  for (let i = 0; i < 5; i++) {
    adminData.push({
      adminId: faker.string.uuid(),
      name: faker.person.fullName(),
      email: faker.internet.email(),
      password: faker.internet.password(),
      phone: `+251-9${faker.number.int({ min: 100000000, max: 999999999 }).toString().slice(1)}`,
      role: 'ADMIN',
      status: 'active',
      location: faker.helpers.arrayElement(['Addis Ababa', 'Bale', 'Gondar', 'Hawassa']),
      profilePicture: faker.image.avatar(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }
  await db.insert(databaseSchema.admins).values(adminData).returning();

  // Seed Users
  console.log('Seeding Users...');
  const userData = [];
  for (let i = 0; i < 20; i++) {
    userData.push({
      userId: faker.string.uuid(),
      name: faker.person.fullName(),
      email: faker.internet.email(),
      password: faker.internet.password(),
      phone: `+251-9${faker.number.int({ min: 100000000, max: 999999999 }).toString().slice(1)}`,
      status: faker.helpers.arrayElement(['active', 'pending', 'suspended']),
      isEmailConfirmed: faker.datatype.boolean(),
      isRegisteredWithGoogle: faker.datatype.boolean(),
      profilePicture: faker.image.avatar(),
      lastLogin: faker.date.recent(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }
  await db.insert(databaseSchema.users).values(userData).returning();

  // Seed UserHealthMetrics
  console.log('Seeding UserHealthMetrics...');
  const userRecords = await db.select().from(databaseSchema.users);
  const healthMetricsData = userRecords.map((user) => ({
    healthMetricId: faker.string.uuid(),
    userId: user.userId,
    age: faker.number.int({ min: 18, max: 80 }).toString(),
    gender: faker.helpers.arrayElement(['Male', 'Female']),
    height: faker.number.float({ min: 150, max: 200, fractionDigits: 1 }).toString(),
    weight: faker.number.float({ min: 50, max: 120, fractionDigits: 1 }).toString(),
    activityLevel: faker.helpers.arrayElement(['Sedentary', 'Moderate', 'Active']),
    dietaryPreference: faker.helpers.arrayElement(['Vegetarian', 'Vegan', 'Non-Vegetarian']),
    weightGoal: faker.helpers.arrayElement(['Maintain', 'Lose', 'Gain']),
    startingWeight: faker.number.float({ min: 50, max: 120, fractionDigits: 1 }).toString(),
    targetWeight: faker.number.float({ min: 45, max: 100, fractionDigits: 1 }).toString(),
    goalUpdatedAt: faker.date.recent(),
    healthIssues: [faker.helpers.arrayElement(['None', 'Diabetes', 'Hypertension', 'Asthma'])],
    fasting: faker.helpers.arrayElement(['Yes', 'No']),
    location: faker.helpers.arrayElement(['Addis Ababa', 'Bale', 'Gondar', 'Hawassa']),
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
  await db.insert(databaseSchema.userHealthMetrics).values(healthMetricsData).returning();

  // Seed WeightHistory
  console.log('Seeding WeightHistory...');
  const weightHistoryData = [];
  for (const user of userRecords) {
    const entries = faker.number.int({ min: 3, max: 10 });
    for (let i = 0; i < entries; i++) {
      weightHistoryData.push({
        entryId: faker.string.uuid(),
        userId: user.userId,
        weight: faker.number.float({ min: 50, max: 120, fractionDigits: 1 }).toString(),
        isGoalEntry: faker.datatype.boolean(),
        weightGoal: faker.helpers.arrayElement(['Maintain', 'Lose', 'Gain']),
        targetWeight: faker.number.float({ min: 45, max: 100, fractionDigits: 1 }).toString(),
        startingWeight: faker.number.float({ min: 50, max: 120, fractionDigits: 1 }).toString(),
        recordedAt: faker.date.recent(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }
  }
  await db.insert(databaseSchema.weightHistory).values(weightHistoryData).returning();
  
  // Seed EthiopianFoods
  console.log('Seeding EthiopianFoods...');
  const foodData = [];
  for (let i = 0; i < 130; i++) {
    foodData.push({
      foodId: faker.string.uuid(),
      name: faker.lorem.words(2),
      localName: faker.lorem.words(2),
      description: faker.lorem.paragraph(),
      category: faker.helpers.arrayElement(['Injera-based', 'Stew', 'Breakfast']),
      region: faker.helpers.arrayElement(['amhara', "tigray", 'afar', 'common/urbun', 'south west']),
        baseIngredient: faker.lorem.word(),
      preparationMethod: faker.lorem.paragraph(),
      calories: faker.number.float({ min: 100, max: 800, fractionDigits: 1 }).toString(),
      nutrients: {
        protein: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
        fat: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
        carbohydrates: faker.number.float({ min: 1, max: 100, fractionDigits: 1 }),
        fiber: faker.number.float({ min: 1, max: 20, fractionDigits: 1 }),
        calcium: faker.number.float({ min: 10, max: 500, fractionDigits: 1 }),
        sodium: faker.number.float({ min: 1, max: 500, fractionDigits: 1 }),
      },
      servingSize: faker.helpers.arrayElement(['100g', '1 piece', '1 serving']),
      culturalSignificance: faker.lorem.paragraph(),
      healthBenefits: faker.lorem.paragraph(),
      nutritionalContext: faker.lorem.paragraph(),
      highlights: faker.lorem.paragraph(),
      image: faker.image.url(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }
  await db.insert(databaseSchema.ethiopianFoods).values(foodData).returning();

  // Seed FoodLogs
  console.log('Seeding FoodLogs...');
  const foodLogsData = [];
  for (let i = 0; i < 150; i++) {
    const randomUser = faker.helpers.arrayElement(userRecords);
    foodLogsData.push({
      logId: faker.string.uuid(),
      userId: randomUser.userId,
      foodName: faker.lorem.words(2),
      baseIngredient: faker.lorem.word(),
      preparation: faker.lorem.words(3),
      calories: faker.number.float({ min: 100, max: 800, fractionDigits: 1 }).toString(),
      nutrients: {
        protein: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
        fat: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
        carbohydrates: faker.number.float({ min: 1, max: 100, fractionDigits: 1 }),
        fiber: faker.number.float({ min: 1, max: 20, fractionDigits: 1 }),
        ash: faker.number.float({ min: 0.1, max: 5, fractionDigits: 1 }),
        calcium: faker.number.float({ min: 10, max: 500, fractionDigits: 1 }),
        zinc: faker.number.float({ min: 0.1, max: 10, fractionDigits: 1 }),
        copper: faker.number.float({ min: 0.1, max: 5, fractionDigits: 1 }),
        sodium: faker.number.float({ min: 1, max: 500, fractionDigits: 1 }),
        thiamine: faker.number.float({ min: 0.1, max: 5, fractionDigits: 1 }),
        riboflavin: faker.number.float({ min: 0.1, max: 5, fractionDigits: 1 }),
        ascorbicAcid: faker.number.float({ min: 1, max: 100, fractionDigits: 1 }),
      },
      timestamp: faker.date.recent(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }
  await db.insert(databaseSchema.foodLogs).values(foodLogsData).returning();

  // Seed Blogs
  console.log('Seeding Blogs...');
  const adminRecords = await db.select().from(databaseSchema.admins);
  const blogData = [];
  for (let i = 0; i < 100; i++) {
    const randomAdmin = faker.helpers.arrayElement(adminRecords);
    blogData.push({
      blogId: faker.string.uuid(),
      title: faker.lorem.sentence(),
      content: faker.lorem.paragraphs(3),
      featuredImage: faker.image.url(),
      category: faker.helpers.arrayElement(['Health', 'Nutrition', 'Fitness', 'Lifestyle']),
      authorId: randomAdmin.adminId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }
  await db.insert(databaseSchema.blogs).values(blogData).returning();

  // Seed Feedback
  console.log('Seeding Feedback...');
  const feedbackData = [];
  for (let i = 0; i < 200; i++) {
    const randomUser = faker.helpers.arrayElement(userRecords);
    feedbackData.push({
      feedbackId: faker.string.uuid(),
      userId: randomUser.userId,
      message: faker.lorem.paragraph(),
      feedbackType: faker.helpers.arrayElement(['general', 'bug', 'feature', 'complaint']),
      rating: faker.number.float({ min: 1, max: 5, fractionDigits: 1 }).toString(),
      status: faker.helpers.arrayElement(['Submitted', 'Reviewed', 'Resolved']),
      adminResponse: faker.datatype.boolean() ? faker.lorem.paragraph() : null,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }
  await db.insert(databaseSchema.feedback).values(feedbackData).returning();

  // Seed Notifications
  console.log('Seeding Notifications...');
  const notificationData = [];
  const superAdmins = await db.select().from(databaseSchema.superAdmins);

  const notificationTypes = ['info', 'success', 'warning', 'error'];
  const notificationCategories = ['System', 'User', 'Blog', 'Feedback'];
  const notificationSources = ['System', 'Admin', 'SuperAdmin', 'User Action'];

  const notificationMessages = {
    'info': [
      'New blog post: "Traditional Ethiopian Foods and Their Health Benefits"',
      'Your weekly nutrition report is ready to view',
      'New feature: You can now track your water intake',
      'Tips for incorporating more teff into your diet',
      'Did you know? Ethiopian coffee has numerous health benefits'
    ],
    'success': [
      'Your meal plan for the week has been generated successfully',
      'Congratulations on logging your meals for 7 consecutive days!',
      'Your health metrics have been updated successfully',
      'Weight goal milestone achieved! Keep up the good work',
      'Youve completed your nutritional assessment'
    ],
    'warning': [
      'Your protein intake is below recommended levels',
      'You havent logged your meals in 3 days',
      'Your subscription will expire in 7 days',
      'Remember to update your weight measurements this week',
      'Your daily calorie intake is above your target'
    ],
    'error': [
      'Failed to sync your latest health data',
      'There was an error processing your meal plan',
      'Your payment method has expired',
      'Unable to connect to your fitness tracker',
      'System maintenance: Some features may be unavailable'
    ]
  };

  for (let i = 0; i < 400; i++) {
    const recipientType = faker.helpers.arrayElement(['user', 'admin', 'superAdmin']);
    let recipientId;

    if (recipientType === 'user' && userRecords.length > 0) {
      const randomUser = faker.helpers.arrayElement(userRecords);
      recipientId = randomUser.userId;
    } else if (recipientType === 'admin' && adminRecords.length > 0) {
      const randomAdmin = faker.helpers.arrayElement(adminRecords);
      recipientId = randomAdmin.adminId;
    } else if (superAdmins.length > 0) {
      const randomSuperAdmin = faker.helpers.arrayElement(superAdmins);
      recipientId = randomSuperAdmin.superAdminId;
    } else {
      recipientId = faker.string.uuid();
    }

    const type = notificationTypes[i % notificationTypes.length];
    const category = faker.helpers.arrayElement(notificationCategories);
    const from = faker.helpers.arrayElement(notificationSources);
    const message = faker.helpers.arrayElement(notificationMessages[type]);
    
    const daysAgo = faker.number.int({ min: 0, max: 30 });
    const timestamp = new Date();
    timestamp.setDate(timestamp.getDate() - daysAgo);
    
    notificationData.push({
      notificationId: faker.string.uuid(),
      recipientId: recipientId,
      message: message,
      type: type,
      category: category,
      from: from,
      isRead: daysAgo > 7 ? true : faker.datatype.boolean(),
      timestamp: timestamp,
      createdAt: timestamp,
      updatedAt: timestamp,
    });
  }
  await db.insert(databaseSchema.notifications).values(notificationData).returning();

  // Seed Recommendations
  console.log('Seeding Recommendations...');
  const recommendationsData = [];
  const healthMetricsRecords = await db.select().from(databaseSchema.userHealthMetrics);

  for (const healthMetric of healthMetricsRecords) {
    recommendationsData.push({
      recommendationId: faker.string.uuid(),
      userId: healthMetric.userId,
      healthMetricId: healthMetric.healthMetricId,
      predictions: {
        Breakfast: Array(3).fill(null).map(() => ({
          local_name: faker.lorem.words(2),
          ingredient: faker.lorem.word(),
          location: faker.helpers.arrayElement(['Addis Ababa', 'Bale', 'Gondar', 'Hawassa']),
          region: faker.helpers.arrayElement(['Amhara', 'Oromia', 'Tigray', 'Addis Ababa']),
          category: faker.helpers.arrayElement(['Injera-based', 'Stew', 'Breakfast']),
          nutrition: {
            calories: faker.number.float({ min: 100, max: 800, fractionDigits: 1 }),
            protein: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            fat: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            carbohydrates: faker.number.float({ min: 1, max: 100, fractionDigits: 1 }),
            fiber: faker.number.float({ min: 1, max: 20, fractionDigits: 1 }),
            sodium: faker.number.float({ min: 1, max: 500, fractionDigits: 1 }),
          },
          serving_size_grams: faker.number.int({ min: 100, max: 500 }),
          serving_nutrition: {
            calories: faker.number.float({ min: 100, max: 800, fractionDigits: 1 }),
            protein: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            fat: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            carbohydrates: faker.number.float({ min: 1, max: 100, fractionDigits: 1 }),
            fiber: faker.number.float({ min: 1, max: 20, fractionDigits: 1 }),
            sodium: faker.number.float({ min: 1, max: 500, fractionDigits: 1 }),
          },
        })),
        Lunch: Array(3).fill(null).map(() => ({
          local_name: faker.lorem.words(2),
          ingredient: faker.lorem.word(),
          location: faker.helpers.arrayElement(['Addis Ababa', 'Bale', 'Gondar', 'Hawassa']),
          region: faker.helpers.arrayElement(['Amhara', 'Oromia', 'Tigray', 'Addis Ababa']),
          category: faker.helpers.arrayElement(['Injera-based', 'Stew', 'Breakfast']),
          nutrition: {
            calories: faker.number.float({ min: 100, max: 800, fractionDigits: 1 }),
            protein: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            fat: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            carbohydrates: faker.number.float({ min: 1, max: 100, fractionDigits: 1 }),
            fiber: faker.number.float({ min: 1, max: 20, fractionDigits: 1 }),
            sodium: faker.number.float({ min: 1, max: 500, fractionDigits: 1 }),
          },
          serving_size_grams: faker.number.int({ min: 100, max: 500 }),
          serving_nutrition: {
            calories: faker.number.float({ min: 100, max: 800, fractionDigits: 1 }),
            protein: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            fat: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            carbohydrates: faker.number.float({ min: 1, max: 100, fractionDigits: 1 }),
            fiber: faker.number.float({ min: 1, max: 20, fractionDigits: 1 }),
            sodium: faker.number.float({ min: 1, max: 500, fractionDigits: 1 }),
          },
        })),
        Snack: Array(2).fill(null).map(() => ({
          local_name: faker.lorem.words(2),
          ingredient: faker.lorem.word(),
          location: faker.helpers.arrayElement(['Addis Ababa', 'Bale', 'Gondar', 'Hawassa']),
          region: faker.helpers.arrayElement(['Amhara', 'Oromia', 'Tigray', 'Addis Ababa']),
          category: faker.helpers.arrayElement(['Injera-based', 'Stew', 'Breakfast']),
          nutrition: {
            calories: faker.number.float({ min: 100, max: 800, fractionDigits: 1 }),
            protein: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            fat: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            carbohydrates: faker.number.float({ min: 1, max: 100, fractionDigits: 1 }),
            fiber: faker.number.float({ min: 1, max: 20, fractionDigits: 1 }),
            sodium: faker.number.float({ min: 1, max: 500, fractionDigits: 1 }),
          },
          serving_size_grams: faker.number.int({ min: 100, max: 500 }),
          serving_nutrition: {
            calories: faker.number.float({ min: 100, max: 800, fractionDigits: 1 }),
            protein: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            fat: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            carbohydrates: faker.number.float({ min: 1, max: 100, fractionDigits: 1 }),
            fiber: faker.number.float({ min: 1, max: 20, fractionDigits: 1 }),
            sodium: faker.number.float({ min: 1, max: 500, fractionDigits: 1 }),
          },
        })),
        Dinner: Array(3).fill(null).map(() => ({
          local_name: faker.lorem.words(2),
          ingredient: faker.lorem.word(),
          location: faker.helpers.arrayElement(['Addis Ababa', 'Bale', 'Gondar', 'Hawassa']),
          region: faker.helpers.arrayElement(['Amhara', 'Oromia', 'Tigray', 'Addis Ababa']),
          category: faker.helpers.arrayElement(['Injera-based', 'Stew', 'Breakfast']),
          nutrition: {
            calories: faker.number.float({ min: 100, max: 800, fractionDigits: 1 }),
            protein: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            fat: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            carbohydrates: faker.number.float({ min: 1, max: 100, fractionDigits: 1 }),
            fiber: faker.number.float({ min: 1, max: 20, fractionDigits: 1 }),
            sodium: faker.number.float({ min: 1, max: 500, fractionDigits: 1 }),
          },
          serving_size_grams: faker.number.int({ min: 100, max: 500 }),
          serving_nutrition: {
            calories: faker.number.float({ min: 100, max: 800, fractionDigits: 1 }),
            protein: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            fat: faker.number.float({ min: 1, max: 50, fractionDigits: 1 }),
            carbohydrates: faker.number.float({ min: 1, max: 100, fractionDigits: 1 }),
            fiber: faker.number.float({ min: 1, max: 20, fractionDigits: 1 }),
            sodium: faker.number.float({ min: 1, max: 500, fractionDigits: 1 }),
          },
        })),
      },
      nutritionTargets: {
        daily_calorie_target: faker.number.float({ min: 1500, max: 3000, fractionDigits: 1 }),
        protein: faker.number.float({ min: 50, max: 200, fractionDigits: 1 }),
        sugar: faker.number.float({ min: 20, max: 100, fractionDigits: 1 }),
        sodium: faker.number.float({ min: 1000, max: 3000, fractionDigits: 1 }),
        calories: faker.number.float({ min: 1500, max: 3000, fractionDigits: 1 }),
        carbohydrates: faker.number.float({ min: 150, max: 400, fractionDigits: 1 }),
        fiber: faker.number.float({ min: 20, max: 50, fractionDigits: 1 }),
        fat: faker.number.float({ min: 40, max: 100, fractionDigits: 1 }),
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }
  await db.insert(databaseSchema.recommendations).values(recommendationsData).returning();

  console.log('Seeding completed!');
  process.exit(0);
};

main().catch((error) => {
  console.error('Seeding failed:', error);
  process.exit(1);
});
