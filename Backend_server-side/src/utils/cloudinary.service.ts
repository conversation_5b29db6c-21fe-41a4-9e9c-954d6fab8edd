import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { cloudinary } from './cloudinary.config';
import {
  UploadApiResponse,
  UploadApiErrorResponse,
  UploadApiOptions,
} from 'cloudinary';

@Injectable()
export class CloudinaryService {
  private readonly logger = new Logger(CloudinaryService.name);

  async uploadImage(
    fileBuffer: Buffer,
    publicId?: string,
  ): Promise<UploadApiResponse> {
    if (!fileBuffer) {
      throw new BadRequestException('No file buffer provided');
    }

    return new Promise((resolve, reject) => {
      const uploadOptions: UploadApiOptions = {
        resource_type: 'image',
        timeout: 30000, // 30 seconds timeout
      };

      if (publicId) {
        uploadOptions.public_id = publicId;
      }

      const uploadStream = cloudinary.uploader.upload_stream(
        uploadOptions,
        (error, result) => {
          if (error) {
            this.logger.error('Cloudinary upload error:', error);
            return reject(new BadRequestException(`Failed to upload image: ${error.message}`));
          }
          if (!result) {
            this.logger.error('Cloudinary upload returned no result');
            return reject(new BadRequestException('Failed to upload image: No result returned'));
          }
          resolve(result);
        }
      );

      uploadStream.on('error', (error) => {
        this.logger.error('Upload stream error:', error);
        reject(new BadRequestException(`Failed to upload image: ${error.message}`));
      });

      uploadStream.end(fileBuffer);
    });
  }

  async deleteImage(publicId: string): Promise<void> {
    if (!publicId) {
      throw new BadRequestException('No public ID provided');
    }

    return new Promise((resolve, reject) => {
      cloudinary.uploader.destroy(publicId, (error, result) => {
        if (error) {
          this.logger.error('Cloudinary delete error:', error);
          return reject(new BadRequestException(`Failed to delete image: ${error.message}`));
        }
        if (result?.result === 'not found') {
          this.logger.log(`Image ${publicId} not found in Cloudinary, skipping deletion`);
          return resolve();
        }
        if (result?.result !== 'ok') {
          this.logger.error('Cloudinary delete failed:', result);
          return reject(new BadRequestException('Failed to delete image'));
        }
        resolve();
      });
    });
  }
}