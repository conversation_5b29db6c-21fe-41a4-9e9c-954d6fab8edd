import { Modu<PERSON> } from '@nestjs/common';
import { AdminsService } from './admins.service';
import { AdminsController } from './admins.controller';
import { PasswordService } from '../auth/services/password.service';
import { JwtService } from '@nestjs/jwt';
import { DashboardService } from './dashboard.service';
import { DashboardController } from './dashboard.controller';

@Module({
  controllers: [AdminsController, DashboardController],
  providers: [AdminsService, PasswordService, JwtService, DashboardService],
  exports: [AdminsService, DashboardService],
})
export class AdminsModule {}
