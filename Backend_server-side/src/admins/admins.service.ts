import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { and, count, desc, eq, like, sql } from 'drizzle-orm';
import { databaseSchema, admins } from '../database/database-schema';
import { DrizzleService } from '../database/drizzle.service';
import { CreateAdminDto } from './dto/create-admin.dto';
import { PasswordService } from '../auth/services/password.service';
import { AccessTokenPayload } from '../auth/auth.interfaces';
import { AdminStatus, Role } from '../common/enums/permissions.enums';
import { JwtService } from '@nestjs/jwt';import { GetAdminsDto } from './dto/get-admin.dto';
import { CloudinaryService } from '../utils/cloudinary.service';
import { Logger } from '@nestjs/common';

@Injectable()
export class AdminsService {
  private readonly logger = new Logger(AdminsService.name);
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly passowrdService: PasswordService,
    private readonly jwtService: JwtService,
    private readonly cloudinaryService: CloudinaryService,
  ) {}
  async createAdmin(data: CreateAdminDto) {
    const adminCount = await this.drizzle.db
      .select({
        count: count(),
      })
      .from(admins);

    if (adminCount[0].count >= 5) {
      throw new BadRequestException('Maximum of 5 admins aready exist.');
    }

    const existingAdmin = await this.drizzle.db.query.admins.findFirst({
      where: eq(databaseSchema.admins.email, data.email),
    });

    if (existingAdmin) throw new ConflictException('Email already exists');

    // await this.checkPhone(data.phone);

    const hashedPassword = await this.passowrdService.hashPassword(
      data.password,
    );
    await this.drizzle.db
      .insert(admins)
      .values({ ...data, password: hashedPassword });

    return {
      message: 'Admin created Sucessfully.',
    };
  }

  async getAdminProfile(adminId: string) {
    const admin = await this.drizzle.db.query.admins.findFirst({
      where: eq(databaseSchema.admins.adminId, adminId),
    });

    if (!admin) throw new NotFoundException('Admin not found');

    const { password, ...data } = admin;
    return data;
  }
  
   async getAllAdmins(queryDto: GetAdminsDto) {
      const { page = 1, limit = 10, search, status, includeDeleted } = queryDto;
      const offset = (page - 1) * limit;
  
      const conditions = [];
      
      if (search) {
        conditions.push(
          like(admins.name, `%${search}%`)
        );
      }
      
      if (status) {
        conditions.push(eq(admins.status, status));
      }
  
      const [data, countResult] = await Promise.all([
        this.drizzle.db
          .select()
          .from(admins)
          .where(and(...conditions))
          .limit(limit)
          .offset(offset)
          .orderBy(desc(admins.createdAt)),
        
        this.drizzle.db
          .select({ count: sql`count(*)` })
          .from(admins)
          .where(and(...conditions))
      ]);
  
      const sanitizedData = data.map((admin) => {
        const { password, refreshToken, ...alladmins } = admin;
        return alladmins;
      });
  
      const total = Number(countResult[0].count);
      
      return { 
        admins: sanitizedData,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        }
      };
    }

  async getAdminById(adminId: string) {
    const admin = await this.drizzle.db.query.admins.findFirst({
      where: eq(databaseSchema.admins.adminId, adminId),
    });

    if (!admin) throw new NotFoundException('Admin not found');

    const { password, ...data } = admin;
    return data;
  }

  async checkPhone(phone: string) {
    const admin = await this.drizzle.db.query.admins.findFirst({
      where: eq(databaseSchema.admins.phone, phone),
    });

    if (admin) throw new ConflictException('Phone number already exists');
  }

  async getAdminByEmail(email: string) {
    const admin = await this.drizzle.db.query.admins.findFirst({
      where: eq(databaseSchema.admins.email, email),
    });

    if (!admin) throw new NotFoundException('Admin not found');
    return admin;
  }

  async updateAdmin(adminId: string, updateData: any) {
    if (updateData.email) {
      const existingAdmin = await this.drizzle.db.query.admins.findFirst({
        where: eq(databaseSchema.admins.email, updateData.email),
      });
      if (existingAdmin && existingAdmin.adminId !== adminId) {
        throw new ConflictException('Email already exists');
      }
    }

    if (updateData.phone) {
      const existingAdmin = await this.drizzle.db.query.admins.findFirst({
        where: eq(databaseSchema.admins.phone, updateData.phone),
      });
      if (existingAdmin && existingAdmin.adminId !== adminId) {
        throw new ConflictException('Phone number already exists');
      }
    }

    await this.drizzle.db
      .update(admins)
      .set(updateData)
      .where(eq(databaseSchema.admins.adminId, adminId));
    return { message: 'Admin updated successfully' };
  }

  async deleteAdmin(adminId: string) {
    await this.drizzle.db.execute(sql`
      UPDATE "Admins"
      SET "status" = ${AdminStatus.DELETED}
      WHERE "admin_id" = ${adminId};
    `);
    return { message: 'Admin deleted successfully' };
  }

  async uploadProfileImage(adminId: string, file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    const admin = await this.drizzle.db.query.admins.findFirst({
      where: (admins, { eq }) => eq(admins.adminId, adminId),
    });

    if (!admin) {
      throw new NotFoundException('Admin not found');
    }

    try {
      // Upload new image to Cloudinary
      const uploadResult = await this.cloudinaryService.uploadImage(
        file.buffer,
        `admin_${adminId}_${Date.now()}`,
      );

      // Update the profile picture URL in the database
      await this.drizzle.db.execute(
        sql`UPDATE "Admins" SET profile_picture = ${uploadResult.secure_url} WHERE admin_id = ${adminId}`
      );

      return {
        message: 'Profile image uploaded successfully',
        profilePicture: uploadResult.secure_url,
      };
    } catch (error) {
      this.logger.error('Failed to upload profile image:', error);
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Failed to upload profile image'
      );
    }
  }
}
