import { Injectable } from '@nestjs/common';
import { DrizzleService } from '../database/drizzle.service';
import { eq, and, gte, sql, count } from 'drizzle-orm';
import { users, admins } from '../database/database-schema';
import { UserStatus } from '../common/enums/permissions.enums';

@Injectable()
export class DashboardService {
  constructor(private readonly drizzle: DrizzleService) {}

  async getDashboardStats() {
    // Get total users count
    const totalUsersResult = await this.drizzle.db
      .select({ count: sql<number>`count(*)` })
      .from(users);
    const totalUsers = totalUsersResult[0]?.count || 0;

    // Get active users count (users who have logged in within the last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const activeUsersResult = await this.drizzle.db
      .select({ count: sql<number>`count(*)` })
      .from(users)
      .where(eq(users.status, UserStatus.ACTIVE));
    const activeUsers = activeUsersResult[0]?.count || 0;

    const pendingUsersResult = await this.drizzle.db
     .select({ count: sql<number>`count(*)` })
      .from(users)
      .where(eq(users.status, UserStatus.PENDING));
    const pendingUsers = pendingUsersResult[0]?.count || 0;

      const deletedUsersResult = await this.drizzle.db
     .select({ count: sql<number>`count(*)` })
      .from(users)
      .where(eq(users.status, UserStatus.DELETED));
    const deletedUsers = deletedUsersResult[0]?.count || 0;

    // Get new users this month
    const firstDayOfMonth = new Date();
    firstDayOfMonth.setDate(1);
    firstDayOfMonth.setHours(0, 0, 0, 0);
    
    const newUsersResult = await this.drizzle.db
      .select({ count: sql<number>`count(*)` })
      .from(users)
      .where(gte(users.createdAt, firstDayOfMonth));
    const newUsersThisMonth = newUsersResult[0]?.count || 0;

    // Get total admins count
    const totalAdminsResult = await this.drizzle.db
      .select({ count: sql<number>`count(*)` })
      .from(admins);
    const totalAdmins = totalAdminsResult[0]?.count || 0;
    return {
      totalUsers,
      activeUsers,
      pendingUsers,
      deletedUsers,
      newUsersThisMonth,
      totalAdmins,
    };
  }

  async getMonthlyUsers() {
    // Get user registrations for the last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 5);
    sixMonthsAgo.setDate(1);
    sixMonthsAgo.setHours(0, 0, 0, 0);

    // This query gets user counts grouped by month

    const result = await this.drizzle.db.select({
        month: sql<string>`to_char(date_trunc('month', "created_at"::timestamp), 'Mon')`,
        count: sql<number>`count(*)`,
      })
      .from(users)
      .where(gte(users.createdAt, sixMonthsAgo))
      .groupBy(sql`date_trunc('month', "created_at"::timestamp)`)
      .orderBy(sql`date_trunc('month', "created_at"::timestamp)`);

    // Map month names to their abbreviated forms
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    // Create an array with all months in the last 6 months
    const monthlyData = [];
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - 5 + i);
      const monthIndex = date.getMonth();
      
      // Find if we have data for this month
      const monthData = result.find(item => item.month === monthNames[monthIndex]);
      
      monthlyData.push({
        name: monthNames[monthIndex],
        users: monthData ? monthData.count : 0
      });
    }

    return monthlyData;
  }
}