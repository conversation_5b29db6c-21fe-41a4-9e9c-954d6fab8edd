import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsUUID, IsEmail } from 'class-validator';
import { UserStatus } from '../../common/enums/permissions.enums';

export class CreateAdminDto {
  @ApiProperty({ example: '<PERSON> Doe' })
  @IsString()
  name: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ example: 'securepassword123' })
  @IsString()
  password: string;

  @ApiProperty({ example: 'Active', enum: UserStatus })
  @IsString()
 status: string;

}

export class AdminResponseDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  adminId: string;

  @ApiProperty({ example: 'John Doe' })
  @IsString()
  name: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ example: '+1234567890', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ example: 'Manager' })
  @IsString()
  role: string;

  @ApiProperty({ example: 'active', enum: UserStatus })
  @IsString()
  status: string;

  @ApiProperty({ example: '2024-03-16T12:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ example: '2024-03-16T12:00:00.000Z' })
  updatedAt: Date;
}
