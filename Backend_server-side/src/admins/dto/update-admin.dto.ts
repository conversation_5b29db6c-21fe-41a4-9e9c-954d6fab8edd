import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsUUID, IsEmail } from 'class-validator';

export class UpdateAdminDto {
  @ApiProperty({ example: '<PERSON>', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ example: '<EMAIL>', required: false })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ example: 'newsecurepassword123', required: false })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({ example: '+1234567890', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ example: 'active', enum: ['active', 'suspended', 'inactive'], required: false })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({ example: "adama", required: false })
  @IsOptional()
  @IsString()
  location?: string;

}
