import { Controller, Get, Request, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Roles } from '../common/decorators/role.decorator';
import { Role } from '../common/enums/permissions.enums';
import { DashboardService } from './dashboard.service';
import { SkipThrottle } from '@nestjs/throttler/dist';

@Controller('admin/dashboard')
@ApiTags('admin-dashboard')
@Roles(Role.ADMIN, Role.SUPERADMIN)
@SkipThrottle({ default: true })  
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('stats')
  @ApiOperation({ summary: 'Get dashboard statistics' })
  @ApiResponse({ status: 200, description: 'Returns dashboard statistics.' })
  async getDashboardStats() {
    return this.dashboardService.getDashboardStats();
  }

  @Get('monthly-users')
  @ApiOperation({ summary: 'Get monthly user registrations' })
  @ApiResponse({ status: 200, description: 'Returns monthly user data.' })
  async getMonthlyUsers() {
    return this.dashboardService.getMonthlyUsers();
  }
}