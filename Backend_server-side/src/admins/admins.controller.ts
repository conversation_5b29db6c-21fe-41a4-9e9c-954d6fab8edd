import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Response,
  Request,
  Query,
  BadRequestException,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { AdminsService } from './admins.service';
import { UpdateAdminDto } from './dto/update-admin.dto';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { Roles } from '../common/decorators/role.decorator';
import { Role } from '../common/enums/permissions.enums';
import { CreateAdminDto } from './dto/create-admin.dto';
import { SkipThrottle } from '@nestjs/throttler/dist';
import { GetAdminsDto } from './dto/get-admin.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';

@Controller('admins')
@ApiTags('admins')
@SkipThrottle({default: true})
export class AdminsController {
  constructor(private readonly adminsService: AdminsService) {}

  @Post('create')
  @Roles(Role.SUPERADMIN)
  async createAdmin(@Body() adminData: CreateAdminDto) {
    return this.adminsService.createAdmin(adminData);
  }

  @Get('profile')
  @Roles(Role.ADMIN)
  async getAdminProfile(@Request() req) {
    return this.adminsService.getAdminProfile(req.user.id);
  }


  @Get()
  @Roles(Role.SUPERADMIN)
  async getAllAdmins(@Query() queryDto: GetAdminsDto) {
    return this.adminsService.getAllAdmins(queryDto);
  }

  @Get(':id')
  async getAdmin(@Param('id') adminId: string) {
    return this.adminsService.getAdminById(adminId);
  }

  @Put(':id')
  async updateAdmin(
    @Param('id') adminId: string,
    @Body() updateData: UpdateAdminDto,
  ) {
    console.log('Updating admin with ID:', adminId, 'Data:', updateData);
    return this.adminsService.updateAdmin(adminId, updateData);
  }

  @Delete(':id')
  @Roles(Role.SUPERADMIN)
  async deleteAdmin(@Param('id') adminId: string) {
    return this.adminsService.deleteAdmin(adminId);
  }

  @Post('profile-image')
  @Roles(Role.ADMIN)
  @UseInterceptors(
    FileInterceptor('profileImage', {
      storage: memoryStorage(),
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
          return cb(
            new BadRequestException('Only image files are allowed!'),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        profileImage: {
          type: 'string',
          format: 'binary',
          description: 'Profile image file (JPG, PNG, JPEG, GIF)',
        },
      },
    },
  })
  async uploadProfileImage(
    @Request() req,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.adminsService.uploadProfileImage(req.user.id, file);
  }
}
