import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Request,
  UploadedFile,
  UseInterceptors,
  BadRequestException,
  Put,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';
import { BlogService } from './blog.service';
import { CreateBlogDto } from './dto/create-blog.dto';
import { UpdateBlogDto } from './dto/update-blog.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { Roles } from '../common/decorators/role.decorator';
import { Role } from '../common/enums/permissions.enums';
import { GetBlogDto } from './dto/get-blog.dto';
import { SkipThrottle } from '@nestjs/throttler/dist';
import { Public } from '../common/decorators/auth.decorators';

@ApiTags('blog')
@Controller('blog')
export class BlogController {
  constructor(private readonly blogService: BlogService) {}

  @Post()
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(),
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
          return cb(
            new BadRequestException('Only image files are allowed!'),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        title: { type: 'string' },
        content: { type: 'string' },
        category: { type: 'string' },
        file: {
          type: 'string',
          format: 'binary',
          description: 'Optional image file (JPG, PNG, JPEG, GIF)',
        },
      },
      required: ['title', 'content', 'category'],
    },
  })
  @ApiOperation({ summary: 'Create a new blog post' })
  @ApiResponse({ status: 201, description: 'Blog post created successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid file type or size.' })
  async create(
    @Body() createBlogDto: CreateBlogDto,
    @Request() req,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.blogService.create(createBlogDto, req.user.id, file);
  }


  @Put(':id')
  @Roles(Role.SUPERADMIN)
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(),
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
          return cb(
            new BadRequestException('Only image files are allowed!'),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        title: { type: 'string' },
        content: { type: 'string' },
        category: { type: 'string' },
        file: {
          type: 'string',
          format: 'binary',
          description: 'Optional image file (JPG, PNG, JPEG, GIF)',
        },
      },
    },
  })
  @ApiOperation({ summary: 'Update a blog post' })
  @ApiResponse({ status: 200, description: 'Blog post updated successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid file type or size.' })
  async updateWithSuperAdmin(
    @Param('id') id: string,
    @Body() updateBlogDto: UpdateBlogDto,
    @Request() req,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.blogService.updateWithAdmin(id, updateBlogDto, req.user.id, file);
  }

  @Patch(':id')
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(),
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
          return cb(
            new BadRequestException('Only image files are allowed!'),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        title: { type: 'string' },
        content: { type: 'string' },
        category: { type: 'string' },
        file: {
          type: 'string',
          format: 'binary',
          description: 'Optional image file (JPG, PNG, JPEG, GIF)',
        },
      },
    },
  })
  @ApiOperation({ summary: 'Update a blog post' })
  @ApiResponse({ status: 200, description: 'Blog post updated successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid file type or size.' })
  async update(
    @Param('id') id: string,
    @Body() updateBlogDto: UpdateBlogDto,
    @Request() req,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.blogService.update(id, updateBlogDto, req.user.id, file);
  }


  @Get('related')
  @SkipThrottle({ default: true })
  @ApiOperation({ summary: 'Get related blog posts' })
  @ApiResponse({ status: 200, description: 'Returns related blog posts.' })
  getRelatedGuides(@Query() queryDto: GetBlogDto) {
    return this.blogService.getRelatedBlogs(
      queryDto.category,
      queryDto.excludeId,
      queryDto.limit,
    );
  }

  @Get()
  @Public()
  @SkipThrottle({ default: true })
  @ApiOperation({ summary: 'Get all blog posts' })
  @ApiResponse({ status: 200, description: 'Returns all blog posts.' })
  findAll(@Query() queryDto: GetBlogDto) {
    return this.blogService.findAll(queryDto);
  }

  @Get('my-blogs')
  @SkipThrottle({ default: true })
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  @ApiOperation({ summary: 'Get all blog posts by the authenticated admin' })
  @ApiResponse({ status: 200, description: 'Returns all blog posts.' })
  getMyBlogs(@Query() queryDto: GetBlogDto, @Request() req) {
    return this.blogService.getMyBlogs(req.user.id, queryDto);
  }

  @Get('categories')
  @Public()
  @ApiOperation({ summary: 'Get all blog categories' })
  @ApiResponse({ status: 200, description: 'Returns all blog categories.' })
  getCategories() {
    return this.blogService.getCategories();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a blog post by ID' })
  @ApiResponse({ status: 200, description: 'Returns a blog post.' })
  findOne(@Param('id') id: string) {
    return this.blogService.findOne(id);
  }

  @Delete('super-admin/:id')
  @Roles(Role.SUPERADMIN)
  @ApiOperation({ summary: 'Delete any blog post (Super Admin only)' })
  @ApiResponse({ status: 200, description: 'Blog post deleted successfully by super admin.' })
  async removeWithSuperAdmin(@Param('id') id: string) {
    console.log("asdf")
    return this.blogService.removeWithSuperAdmin(id);
  }

  @Delete(':id')
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  @ApiOperation({ summary: 'Delete a blog post' })
  @ApiResponse({ status: 200, description: 'Blog post deleted successfully.' })
  async remove(@Param('id') id: string, @Request() req) {
    return this.blogService.remove(id, req.user.id);
  }
}