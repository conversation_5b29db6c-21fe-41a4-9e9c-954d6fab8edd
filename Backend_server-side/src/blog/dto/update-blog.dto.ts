import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdateBlogDto{
    @ApiProperty({ description: 'Blog post title', required: false })
    @IsOptional()
    @IsString()
    title?: string;

    @ApiProperty({ description: 'Blog post content', required: false })
    @IsOptional()
    @IsString()
    content?: string;

    @ApiProperty({ description: 'Blog post category', required: false })
    @IsOptional()
    @IsString()
    category?: string;

    @ApiProperty({ description: 'Featured image URL', required: false })
    @IsOptional()
    @IsString()
    featuredImage?: string;
}