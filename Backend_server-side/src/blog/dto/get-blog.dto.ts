import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

export class GetBlogDto {
  @ApiProperty({ description: 'Search term for filtering blogs by title', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ description: 'Filter blogs by category', required: false })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({ description: 'Page number', required: false, default: 1 })
  @IsOptional()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ description: 'Number of items per page', required: false, default: 10 })
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({ description: 'Exclude blog by ID', required: false })
  @IsOptional()
  @IsString()
  excludeId?: string;

  @ApiProperty({ description: 'Filter blogs by author ID', required: false })
  @IsOptional()
  @IsString()
  authorId?: string;
}