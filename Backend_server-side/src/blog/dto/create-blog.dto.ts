import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty, IsString, IsEnum,
} from 'class-validator';
import { BlogCategory } from '../../common/enums/permissions.enums';

export class CreateBlogDto {
  @ApiProperty({ description: 'Blog post title' })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({ description: 'Blog post content' })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({ 
    description: 'Blog post category',
    enum: BlogCategory,
    enumName: 'BlogCategory',
    example: 'Health'
  })
  @IsNotEmpty()
  @IsEnum(BlogCategory)
  category: BlogCategory;
}
