import { Module } from '@nestjs/common';
import { BlogService } from './blog.service';
import { Blog<PERSON>ontroller } from './blog.controller';
import { NotificationModule } from '../notification/notification.module';
import { CloudinaryService } from '../utils/cloudinary.service';

@Module({
  imports: [NotificationModule],
  controllers: [BlogController],
  providers: [BlogService, CloudinaryService],
  exports: [BlogService],
})
export class BlogModule {}
