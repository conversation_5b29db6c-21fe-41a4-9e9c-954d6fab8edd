import { Injectable, NotFoundException } from '@nestjs/common';
import { DrizzleService } from '../database/drizzle.service';
import { and, eq, like, count, sql, ne, desc } from 'drizzle-orm';
import { CreateBlogDto } from '../blog/dto/create-blog.dto';
import { UpdateBlogDto } from '../blog/dto/update-blog.dto';
import { blogs } from '../database/database-schema';
import { GetBlogDto } from './dto/get-blog.dto';
import { NotificationService } from '../notification/notification.service';
import { NotificationType } from '../common/enums/permissions.enums';
import { CloudinaryService } from '../utils/cloudinary.service';

@Injectable()
export class BlogService {
  constructor(
    private drizzle: DrizzleService,
    private notificationService: NotificationService,
    private cloudinaryService: CloudinaryService,
  ) {}

  async create(
    createBlogDto: CreateBlogDto,
    authorId: string,
    file?: Express.Multer.File,
  ) {
    let featuredImageUrl: string | null = null;

    if (file) {
      try {
        const uploadResult = await this.cloudinaryService.uploadImage(
          file.buffer,
          `blog_${Date.now()}`,
        );
        featuredImageUrl = uploadResult.secure_url;
      } catch (error) {
        throw new Error('Failed to upload image to Cloudinary');
      }
    }

    const blog = await this.drizzle.db.execute(sql`
      INSERT INTO "Blogs" (
        "title", "content", "featured_image", "category", "author"
      )
      VALUES (
        ${createBlogDto.title}, ${createBlogDto.content}, ${featuredImageUrl}, ${createBlogDto.category}, ${authorId}
      )
    `);

    // Notify users
    const users = await this.drizzle.db.query.users.findMany();
    for (const user of users) {
      await this.notificationService.create({
        recipientId: String(user.userId),
        message: `New blog post: "${createBlogDto.title}"`,
        type: NotificationType.BLOG_UPDATE,
        from: 'System',
      });
    }

    return blog[0];
  }

  async getMyBlogs(authorId: string, queryDto: GetBlogDto) {
    const { page = 1, limit = 10, search, category } = queryDto;
    const offset = (page - 1) * limit;

    const conditions = [eq(blogs.authorId, authorId)];
    if (search) {
      conditions.push(like(blogs.title, `%${search}%`));
    }
    if (category) {
      conditions.push(eq(blogs.category, category));
    }

    const [blogList, countResult] = await Promise.all([
      this.drizzle.db
        .select()
        .from(blogs)
        .where(and(...conditions))
        .orderBy(desc(blogs.createdAt))
        .limit(limit)
        .offset(offset),
      this.drizzle.db
        .select({ count: sql`count(*)` })
        .from(blogs)
        .where(and(...conditions)),
    ]);

    const total = Number(countResult[0].count);
    return {
      data: blogList,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getRelatedBlogs(category: string, excludeId: string, limit = 3) {
    return this.drizzle.db
      .select()
      .from(blogs)
      .where(and(eq(blogs.category, category), ne(blogs.blogId, excludeId)))
      .orderBy(desc(blogs.createdAt))
      .limit(limit);
  }

  async findAll(queryDto: GetBlogDto) {
    const { page = 1, limit = 10, search, category, authorId } = queryDto;
    const offset = (page - 1) * limit;

    const conditions = [];
    if (authorId) {
      conditions.push(eq(blogs.authorId, authorId));
    }
    if (search) {
      conditions.push(like(blogs.title, `%${search}%`));
    }
    if (category) {
      conditions.push(eq(blogs.category, category));
    }

    const [blogList, countResult] = await Promise.all([
      this.drizzle.db
        .select()
        .from(blogs)
        .where(and(...conditions))
        .orderBy(desc(blogs.createdAt))
        .limit(limit)
        .offset(offset),
      this.drizzle.db
        .select({ count: sql`count(*)` })
        .from(blogs)
        .where(and(...conditions)),
    ]);

    const total = Number(countResult[0].count);
    return {
      data: blogList,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const [blog] = await this.drizzle.db
      .select()
      .from(blogs)
      .where(eq(blogs.blogId, id));

    if (!blog) {
      throw new NotFoundException(`Blog with ID ${id} not found`);
    }

    return blog;
  }

  async updateWithAdmin(
    id: string,
    updateBlogDto: UpdateBlogDto,
    authorId: string,
    file?: Express.Multer.File,
  ) {
    console.log("Updating blog with ID:", id);
    const [existingBlog] = await this.drizzle.db
      .select()
      .from(blogs)
      .where(and(eq(blogs.blogId, id)));

    if (!existingBlog) {
      throw new NotFoundException(
        `Blog with ID ${id} not found or unauthorized`,
      );
    }

    let featuredImageUrl = existingBlog.featuredImage;

    // Handle image upload/update
    if (file) {
      try {
        // Delete old image if exists
        if (featuredImageUrl) {
          const publicId = this.extractPublicId(featuredImageUrl);
          await this.cloudinaryService.deleteImage(publicId);
        }

        // Upload new image
        const uploadResult = await this.cloudinaryService.uploadImage(
          file.buffer,
          `blog_${Date.now()}`,
        );
        featuredImageUrl = uploadResult.secure_url;
      } catch (error) {
        throw new Error('Failed to update image on Cloudinary');
      }
    }

    const updateData = {
      ...(updateBlogDto.title && { title: updateBlogDto.title }),
      ...(updateBlogDto.content && { content: updateBlogDto.content }),
      ...(updateBlogDto.category && { category: updateBlogDto.category }),
      ...(featuredImageUrl !== undefined && { featuredImage: featuredImageUrl }),
    };

    if (Object.keys(updateData).length === 0) {
      return existingBlog;
    }

    const [updatedBlog] = await this.drizzle.db
      .update(blogs)
      .set(updateData)
      .where(eq(blogs.blogId, id))
      .returning();

    return updatedBlog;
  }


  async update(
    id: string,
    updateBlogDto: UpdateBlogDto,
    authorId: string,
    file?: Express.Multer.File,
  ) {
    const [existingBlog] = await this.drizzle.db
      .select()
      .from(blogs)
      .where(and(eq(blogs.blogId, id), eq(blogs.authorId, authorId)));

    if (!existingBlog) {
      throw new NotFoundException(
        `Blog with ID ${id} not found or unauthorized`,
      );
    }

    let featuredImageUrl = existingBlog.featuredImage;

    // Handle image upload/update
    if (file) {
      try {
        // Delete old image if exists
        if (featuredImageUrl) {
          const publicId = this.extractPublicId(featuredImageUrl);
          await this.cloudinaryService.deleteImage(publicId);
        }

        // Upload new image
        const uploadResult = await this.cloudinaryService.uploadImage(
          file.buffer,
          `blog_${Date.now()}`,
        );
        featuredImageUrl = uploadResult.secure_url;
      } catch (error) {
        throw new Error('Failed to update image on Cloudinary');
      }
    }

    const updateData = {
      ...(updateBlogDto.title && { title: updateBlogDto.title }),
      ...(updateBlogDto.content && { content: updateBlogDto.content }),
      ...(updateBlogDto.category && { category: updateBlogDto.category }),
      ...(featuredImageUrl !== undefined && { featuredImage: featuredImageUrl }),
    };

    if (Object.keys(updateData).length === 0) {
      return existingBlog;
    }

    const [updatedBlog] = await this.drizzle.db
      .update(blogs)
      .set(updateData)
      .where(eq(blogs.blogId, id))
      .returning();

    return updatedBlog;
  }

  async remove(id: string, authorId: string) {
    const [existingBlog] = await this.drizzle.db
      .select()
      .from(blogs)
      .where(and(eq(blogs.blogId, id), eq(blogs.authorId, authorId)));

    if (!existingBlog) {
      throw new NotFoundException(
        `Blog with ID ${id} not found or unauthorized`,
      );
    }

    // Delete image from Cloudinary if exists
    if (existingBlog.featuredImage) {
      const publicId = this.extractPublicId(existingBlog.featuredImage);
      await this.cloudinaryService.deleteImage(publicId);
    }

    await this.drizzle.db.delete(blogs).where(eq(blogs.blogId, id));
    return { message: 'Blog deleted successfully' };
  }

  async getCategories() {
    const categories = await this.drizzle.db
      .select({ category: blogs.category })
      .from(blogs)
      .groupBy(blogs.category);

    return categories.map((c) => c.category);
  }

  async removeWithSuperAdmin(id: string) {
    const [existingBlog] = await this.drizzle.db
      .select()
      .from(blogs)
      .where(eq(blogs.blogId, id));

    if (!existingBlog) {
      throw new NotFoundException(`Blog with ID ${id} not found`);
    }

    // Delete image from Cloudinary if exists
    if (existingBlog.featuredImage) {
      const publicId = this.extractPublicId(existingBlog.featuredImage);
      await this.cloudinaryService.deleteImage(publicId);
    }

    await this.drizzle.db.delete(blogs).where(eq(blogs.blogId, id));
    return { message: 'Blog deleted successfully by super admin' };
  }

  private extractPublicId(url: string): string {
    const parts = url.split('/');
    const filename = parts[parts.length - 1];
    return filename.split('.')[0];
  }
}