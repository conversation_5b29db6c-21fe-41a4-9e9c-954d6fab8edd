import { Controller, Get, Post, Body, Patch, Param, Delete, Query, Request, UseInterceptors, ClassSerializerInterceptor } from '@nestjs/common';
import { FoodLoggingService } from './food-logging.service';
import { CreateFoodLogDto } from './dto/create-food-log.dto';
import { UpdateFoodLogDto } from './dto/update-food-log.dto';
import { GetFoodLogsDto, GetStartAndEndDateDto } from './dto/get-food-logs.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('food-logging')
@Controller('food-logging')
@UseInterceptors(ClassSerializerInterceptor)
export class FoodLoggingController {
  constructor(private readonly foodLoggingService: FoodLoggingService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new food log entry' })
  @ApiResponse({ status: 201, description: 'The food log has been successfully created.' })
  create(@Body() createFoodLogDto: CreateFoodLogDto, @Request() req) {
    // Override userId with the authenticated user's ID
    return this.foodLoggingService.create(createFoodLogDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all food logs for the authenticated user' })
  @ApiResponse({ status: 200, description: 'Return all food logs.' })
  findAll(@Query() queryDto: GetFoodLogsDto, @Request() req) {
    return this.foodLoggingService.findAll(req.user.id, queryDto);
  }

  @Get('summary')
  @ApiOperation({ summary: 'Get nutrition summary for a date range' })
  @ApiResponse({ status: 200, description: 'Return nutrition summary.' })
  getNutritionSummary(
    @Query() startEndDate: GetStartAndEndDateDto,
    @Query('endDate') endDate: string,
    @Request() req
  ) {
    return this.foodLoggingService.getNutritionSummary(
      req.user.id,
      startEndDate
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific food log by ID' })
  @ApiResponse({ status: 200, description: 'Return the food log.' })
  @ApiResponse({ status: 404, description: 'Food log not found.' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.foodLoggingService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a food log entry' })
  @ApiResponse({ status: 200, description: 'The food log has been successfully updated.' })
  update(
    @Param('id') id: string,
    @Body() updateFoodLogDto: UpdateFoodLogDto,
    @Request() req
  ) {
    return this.foodLoggingService.update(id, req.user.id, updateFoodLogDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a food log entry' })
  @ApiResponse({ status: 200, description: 'The food log has been successfully deleted.' })
  remove(@Param('id') id: string, @Request() req) {
    return this.foodLoggingService.remove(id, req.user.id);
  }
}
