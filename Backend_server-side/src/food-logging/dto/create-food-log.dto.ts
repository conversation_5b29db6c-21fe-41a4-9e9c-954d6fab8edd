import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber, IsObject, IsDateString } from 'class-validator';

export class CreateFoodLogDto {
  @ApiProperty({ description: 'Name of the Ethiopian food consumed' })
  @IsNotEmpty()
  @IsString()
  foodName: string;

  @ApiProperty({ description: 'Primary ingredient of the food', required: false })
  @IsOptional()
  @IsString()
  baseIngredient?: string;

  @ApiProperty({ description: 'Preparation method', required: false })
  @IsOptional()
  @IsString()
  preparation?: string;

  @ApiProperty({ description: 'Caloric value of the food' })
  @IsOptional()
  @IsNumber()
  calories?: number;

  @ApiProperty({
    description: 'Nutrient breakdown of the food',
    required: false,
    example: {
      protein: 10.5,
      fat: 5.2,
      carbohydrates: 65.3,
      fiber: 12.1,
      ash: 2.3,
      calcium: 120,
      zinc: 3.2,
      copper: 0.8,
      sodium: 15,
      thiamine: 0.4,
      riboflavin: 0.3,
      ascorbicAcid: 5
    }
  })
  @IsOptional()
  @IsObject()
  nutrients?: {
    protein: number;
    fat: number;
    carbohydrates: number;
    fiber: number;
    ash: number;
    calcium: number;
    zinc: number;
    copper: number;
    sodium: number;
    thiamine: number;
    riboflavin: number;
    ascorbicAcid: number;
  };
}