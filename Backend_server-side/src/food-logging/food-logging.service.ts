import { Injectable } from '@nestjs/common';
import { DrizzleService } from '../database/drizzle.service';
import { CreateFoodLogDto } from './dto/create-food-log.dto';
import { UpdateFoodLogDto } from './dto/update-food-log.dto';
import { GetFoodLogsDto } from './dto/get-food-logs.dto';
import { foodLogs } from '../database/database-schema';
import { eq, and, gte, lte, like, sql } from 'drizzle-orm';

@Injectable()
export class FoodLoggingService {
  constructor(private drizzle: DrizzleService) {}

  async create(createFoodLogDto: CreateFoodLogDto, userId: string) {
    const result = await this.drizzle.db
      .insert(foodLogs)
      .values({
        ...createFoodLogDto,
        userId,
      })
      .returning();

    return result[0];
  }

  async findAll(id: string, queryDto: GetFoodLogsDto) {
    const { startDate, endDate, foodName, page = 1, limit = 10 } = queryDto;
    const offset = (page - 1) * limit;
    // Build query conditions
    const conditions = [eq(foodLogs.userId, id)];
    if (startDate) {
      conditions.push(gte(foodLogs.timestamp, new Date(startDate)));
    }
    if (endDate) {
      conditions.push(lte(foodLogs.timestamp, new Date(endDate)));
    }
    if (foodName) {
      conditions.push(like(foodLogs.foodName, `%${foodName}%`));
    }
    const query = this.drizzle.db
      .select()
      .from(foodLogs)
      .where(and(...conditions))
      .orderBy(sql`${foodLogs.timestamp} DESC`)
      .limit(limit)
      .offset(offset);
    const [logs, countResult] = await Promise.all([
      query,
      this.drizzle.db
        .select({ count: sql`count(*)` })
        .from(foodLogs)
        .where(and(...conditions)),
    ]);
    const total = Number(countResult[0].count);
    return {
      data: logs,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, userId: string) {
    const result = await this.drizzle.db
      .select()
      .from(foodLogs)
      .where(and(eq(foodLogs.logId, id), eq(foodLogs.userId, userId)));
    return result[0] || null;
  }

  async update(id: string, userId: string, updateFoodLogDto: UpdateFoodLogDto) {
    const result = await this.drizzle.db
      .update(foodLogs)
      .set({
        ...updateFoodLogDto,
      })
      .where(and(eq(foodLogs.logId, id), eq(foodLogs.userId, userId)))
      .returning();
    return result[0] || null;
  }

  async remove(id: string, userId: string) {
    const result = await this.drizzle.db
      .delete(foodLogs)
      .where(and(eq(foodLogs.logId, id), eq(foodLogs.userId, userId)))
      .returning();
    return result[0] || null;
  }

  async getNutritionSummary(userId: string, startEndDate) {
    const { startDate, endDate } = startEndDate;

    const conditions = [eq(foodLogs.userId, userId)];

    if (startDate) {
      conditions.push(gte(foodLogs.timestamp, new Date(startDate)));
    }

    if (endDate) {
      conditions.push(lte(foodLogs.timestamp, new Date(endDate)));
    }

    const logs = await this.drizzle.db
      .select()
      .from(foodLogs)
      .where(and(...conditions));

    // Calculate nutrition totals
    const summary = {
      totalCalories: 0,
      totalNutrients: {
        protein: 0,
        fat: 0,
        carbohydrates: 0,
        fiber: 0,
        calcium: 0,
        zinc: 0,
        copper: 0,
        sodium: 0,
        thiamine: 0,
        riboflavin: 0,
        ascorbicAcid: 0,
      },
      foodCount: logs.length,
      dateRange: {
        start: startDate,
        end: endDate,
      },
    };

    // Sum up all nutrients
    logs.forEach((log) => {
      if (log.calories) {
        summary.totalCalories += Number(log.calories);
      }

      if (log.nutrients) {
        Object.keys(summary.totalNutrients).forEach((nutrient) => {
          if (log.nutrients[nutrient]) {
            summary.totalNutrients[nutrient] += Number(log.nutrients[nutrient]);
          }
        });
      }
    });

    return summary;
  }
}
