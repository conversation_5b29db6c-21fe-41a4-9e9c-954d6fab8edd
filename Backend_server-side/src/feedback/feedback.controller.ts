import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
} from '@nestjs/common';
import { FeedbackService } from './feedback.service';
import { CreateFeedbackDto } from './dto/create-feedback.dto';
import { FeedbackStatus, UpdateFeedbackStatusDto } from './dto/update-feedback-status.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Role } from '../common/enums/permissions.enums';
import { Roles } from '../common/decorators/role.decorator';
import { GetFeedbackDto } from './dto/get-feedback.dto';
import { SkipThrottle } from '@nestjs/throttler/dist';

@ApiTags('feedback')
@Controller('feedback')
@SkipThrottle({default: true})
export class FeedbackController {
  constructor(private readonly feedbackService: FeedbackService) {}


  @Post(':id/respond')
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  @ApiOperation({ summary: 'Respond to feedback' })
  @ApiResponse({ status: 200, description: 'Feedback responded to successfully.' })
  respondToFeedback(
    @Param('id') id: string,
    @Body() response: string,
    @Request() req
  ) {
    return this.feedbackService.respondToFeedback(id, response);
  }

  @Post()
  @ApiOperation({ summary: 'Submit new feedback' })
  @ApiResponse({ status: 201, description: 'Feedback submitted successfully.' })
  create(@Body() createFeedbackDto: CreateFeedbackDto, @Request() req) {
    return this.feedbackService.create(createFeedbackDto, req.user.id);
  }

  @Get('user')
  @ApiOperation({ summary: 'Get feedback for the authenticated user' })
  @ApiResponse({ status: 200, description: 'Returns feedback for the authenticated user.' })
  getUserFeedback(
    @Request() req,
    @Query() queryDto: GetFeedbackDto,
  ) {
    return this.feedbackService.getUserFeedback(req.user.id, queryDto);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all feedback' })
  @ApiResponse({ status: 200, description: 'Returns all feedback.' })
  findAll(
    @Request() req,
    @Query('page') page: number,
    @Query('limit') limit: number,
    @Query('status') status: FeedbackStatus,
  ) {
    const isAdmin = req.user.roles.some(role => 
      [Role.ADMIN, Role.SUPERADMIN].includes(role)
    );
    console.log("sadfasdf");
    return this.feedbackService.findAll(page, limit, isAdmin, req.user.id, status);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get feedback by ID' })
  @ApiResponse({ status: 200, description: 'Returns the feedback.' })
  findOne(@Param('id') id: string, @Request() req) {
    const isAdmin = req.user.roles.some(role => 
      [Role.ADMIN, Role.SUPERADMIN].includes(role)
    );
    return this.feedbackService.findOne(id, req.user.id, isAdmin);
  }

  @Patch(':id/status')
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  @ApiOperation({ summary: 'Update feedback status' })
  @ApiResponse({ status: 200, description: 'Feedback status updated successfully.' })
  updateStatus(
    @Param('id') id: string,
    @Body() updateFeedbackStatusDto: UpdateFeedbackStatusDto,
    @Request() req
  ) {
    const isAdmin = req.user.roles.some(role => 
      [Role.ADMIN, Role.SUPERADMIN].includes(role)
    );
    return this.feedbackService.updateStatus(id, updateFeedbackStatusDto, isAdmin);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete feedback' })
  @ApiResponse({ status: 200, description: 'Feedback deleted successfully.' })
  remove(@Param('id') id: string, @Request() req) {
    const isAdmin = req.user.roles.some(role => 
      [Role.ADMIN, Role.SUPERADMIN].includes(role)
    );
    return this.feedbackService.remove(id, req.user.id, isAdmin);
  }
}