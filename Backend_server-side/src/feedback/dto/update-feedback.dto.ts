import { ApiProperty } from "@nestjs/swagger"
import { IsString, IsOptional } from "class-validator"
import { FeedbackStatus } from "./update-feedback-status.dto"

export class UpdateFeedbackDto {
  @ApiProperty({
    description: "Message content of the feedback",
    required: false,
  })
  @IsOptional()
  @IsString()
  message?: string

  @ApiProperty({
    description: "Admin response to the feedback",
    required: false,
  })
  @IsOptional()
  @IsString()
  adminResponse?: string

  @ApiProperty({
    description: "Status of the feedback",
    enum: FeedbackStatus,
    required: false,
  })
  @IsOptional()
  @IsString()
  status?: FeedbackStatus
}
