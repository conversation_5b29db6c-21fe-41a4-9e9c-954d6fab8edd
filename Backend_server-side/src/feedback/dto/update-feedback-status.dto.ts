import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEnum } from 'class-validator';

export enum FeedbackStatus {
  SUBMITTED = 'Submitted',
  REVIEWED = 'Reviewed',
  RESOLVED = 'Resolved'
}

export class UpdateFeedbackStatusDto {
  @ApiProperty({ 
    description: 'Status of the feedback',
    enum: FeedbackStatus
  })
  @IsNotEmpty()
  @IsEnum(FeedbackStatus)
  status: FeedbackStatus;
}