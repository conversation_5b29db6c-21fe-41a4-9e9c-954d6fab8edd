import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsEnum, IsInt, Min, Max } from 'class-validator';
import { FeedbackStatus } from './update-feedback-status.dto';

export enum FeedbackType {
  GENERAL = 'general',
  CONTENT = 'content',
  USABILITY = 'usability',
  FEATURE = 'feature',
  BUG = 'bug'
}

export class CreateFeedbackDto {
  @ApiProperty({ description: 'Feedback message from the user' })
  @IsNotEmpty()
  @IsString()
  message: string;

  @ApiProperty({ 
    description: 'Type of feedback',
    enum: FeedbackType,
    default: FeedbackType.GENERAL
  })
  @IsOptional()
  @IsEnum(FeedbackType)
  feedbackType?: FeedbackType;

  @ApiProperty({ 
    description: 'User rating (1-5)',
    minimum: 1,
    maximum: 5
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  rating?: number;

  @ApiProperty({ 
    description: 'Status of the feedback',
    enum: FeedbackStatus,
    default: FeedbackStatus.SUBMITTED
  })
  @IsOptional()
  @IsEnum(FeedbackStatus)
  status?: FeedbackStatus;


  @ApiProperty({ 
    description: 'Optional attachment URL or file path',
    required: false 
  })
  @IsOptional()
  @IsString()
  feedbackAttachment?: string;
}
