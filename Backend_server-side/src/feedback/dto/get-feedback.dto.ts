import { ApiProperty } from "@nestjs/swagger"
import { IsOptional, IsString } from "class-validator"
import { Type } from "class-transformer"
import { FeedbackStatus } from "./update-feedback-status.dto"

export class GetFeedbackDto {
  @ApiProperty({ description: "Search term for filtering feedback by message", required: false })
  @IsOptional()
  @IsString()
  search?: string

  @ApiProperty({ description: "Filter feedback by status", required: false })
  @IsOptional()
  @IsString()
  status?: FeedbackStatus

  @ApiProperty({ description: "Filter by response status (true = has response, false = no response)", required: false })
  @IsOptional()
  @IsString()
  hasResponse?: string

  @ApiProperty({ description: "Page number", required: false, default: 1 })
  @IsOptional()
  @Type(() => Number)
  page?: number = 1

  @ApiProperty({ description: "Number of items per page", required: false, default: 10 })
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10
}
