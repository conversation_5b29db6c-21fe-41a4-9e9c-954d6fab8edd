import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { DrizzleService } from '../database/drizzle.service';
import { CreateFeedbackDto, FeedbackType } from './dto/create-feedback.dto';
import { GetFeedbackDto } from './dto/get-feedback.dto';
import { FeedbackStatus, UpdateFeedbackStatusDto } from './dto/update-feedback-status.dto';
import { feedback } from '../database/database-schema';
import { eq, and, sql, desc } from 'drizzle-orm';
import { NotificationService } from '../notification/notification.service';
import { NotificationType } from '../common/enums/permissions.enums';

@Injectable()
export class FeedbackService {
  constructor(private drizzle: DrizzleService,
    private readonly notificationService: NotificationService
  ) {}

  async respondToFeedback(feedbackId: string, response: string) {
    const [existingFeedback] = await this.drizzle.db
      .select()
      .from(feedback)
      .where(eq(feedback.feedbackId, feedbackId));

    if (!existingFeedback) {
      throw new NotFoundException(`Feedback with ID ${feedbackId} not found`);
    }

    const updatedFeedback = await this.drizzle.db.execute(sql`
    UPDATE "Feedback" SET admin_response = ${response}, status = ${FeedbackStatus.REVIEWED} WHERE feedback_id = ${feedbackId}
    `);

    return updatedFeedback;
  }

  async create(createFeedbackDto: CreateFeedbackDto, userId: string) {
    const result = await this.drizzle.db
      .insert(feedback)
      .values({
        userId,
        ...createFeedbackDto
      })
      .returning();


      await this.notificationService.sendNotificationForAdmins(
         'A new feedback has been submitted. Please check it out!'
      );

    return "Thank you for your valuable feedback!";
  }

  async findAll(page = 1, limit = 10, isAdmin = false, userId?: string, status?: string) {
    console.log("Fetching feedback with params:", { page, limit, isAdmin, userId, status });
    const offset = (page - 1) * limit;

    const conditions = [];
    // If not admin, only show user's own feedback
    if (!isAdmin && userId) {
      conditions.push(eq(feedback.userId, userId));
    }
    if (status && status !== 'all') {
      conditions.push(eq(feedback.status, status as FeedbackStatus));
    }

    const [totalCount] = await this.drizzle.db
      .select({ count: sql`count(*)` })
      .from(feedback)
      .where(and(...conditions));

    const results = await this.drizzle.db
      .select()
      .from(feedback)
      .where(and(...conditions))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(feedback.createdAt));

    console.log("Feedback results:", results);

    return {
      data: results,
      meta: {
        total: Number(totalCount.count),
        page,
        limit,
        totalPages: Math.ceil(Number(totalCount.count) / limit),
      },
    };
  }

  async getUserFeedback(userId: string, queryDto: GetFeedbackDto) {
    const { page = 1, limit = 10 } = queryDto;
    const offset = (page - 1) * limit;
    const results = await this.drizzle.db
      .select()
      .from(feedback)
      .where(eq(feedback.userId, userId))
      .orderBy(desc(feedback.createdAt))
      .limit(limit)
      .offset(offset);

    const [totalCount] = await this.drizzle.db
      .select({ count: sql`count(*)` })
      .from(feedback)
      .where(eq(feedback.userId, userId));

    return {
      data: results,
      meta: {
        total: Number(totalCount.count || 0),
        page,
        limit,
        totalPages: Math.ceil(Number(totalCount.count || 0) / limit),
      },
    };
  }

  async findOne(id: string, userId: string, isAdmin = false) {
    const conditions = [];
    // If not admin, only allow viewing own feedback
    if (!isAdmin) {
      conditions.push(eq(feedback.userId, userId));
    }

    const [result] = await this.drizzle.db
      .select()
      .from(feedback)
      .where(and(eq(feedback.feedbackId, id), ...conditions))
      .orderBy(feedback.createdAt);

    if (!result) {
      throw new NotFoundException(`Feedback with ID ${id} not found`);
    }

    return result;
  }

  async updateStatus(
    id: string,
    updateFeedbackStatusDto: UpdateFeedbackStatusDto,
    isAdmin = false,
  ) {
    if (!isAdmin) {
      throw new ForbiddenException(
        'Only administrators can update feedback status',
      );
    }

    const [existingFeedback] = await this.drizzle.db
      .select()
      .from(feedback)
      .where(eq(feedback.feedbackId, id));

    if (!existingFeedback) {
      throw new NotFoundException(`Feedback with ID ${id} not found`);
    }

    const updatedFeedback = await this.drizzle.db.execute(sql`
    UPDATE "Feedback" SET status = ${updateFeedbackStatusDto.status} WHERE feedback_id = ${id}
    `);

    // Notify user that their feedback was addressed
    const feedbackRecord = await this.drizzle.db.query.feedback.findFirst({
      where: (f, { eq }) => eq(f.feedbackId, id)
    });
    
    await this.notificationService.create({
      recipientId: feedbackRecord?.userId,
      message: `Your feedback has been ${updateFeedbackStatusDto.status.toLowerCase()}`,
      type: NotificationType.FEEDBACK_ALERT,
      from: "Admin",
    });

    return updatedFeedback;
  }

  async remove(id: string, userId: string, isAdmin = false) {

    const conditions = [];
    // If not admin, only allow deleting own feedback
    if (!isAdmin) {
      conditions.push(eq(feedback.userId, userId));
    }

    const [existingFeedback] = await this.drizzle.db
      .select()
      .from(feedback)
      .where(and(...conditions));

    if (!existingFeedback) {
      throw new NotFoundException(
        `Feedback with ID ${id} not found or unauthorized`,
      );
    }

    await this.drizzle.db.delete(feedback).where(eq(feedback.feedbackId, id));

    return { message: 'Feedback deleted successfully' };
  }
}
