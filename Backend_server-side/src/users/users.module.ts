import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { JwtService } from '@nestjs/jwt';
import { PasswordService } from '../auth/services/password.service';
import { NutritionService } from './nutrition.service';
import { NutritionController } from './nutrition.controller';

@Module({
  providers: [UsersService, NutritionService, PasswordService, JwtService],
  controllers: [UsersController, NutritionController],
  exports: [UsersService],
})
export class UsersModule {}
