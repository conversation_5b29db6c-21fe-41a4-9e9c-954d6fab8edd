import {
  ConflictException,
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { RegisterUserDto } from './dto/register-user.dto';
import { DrizzleService } from '../database/drizzle.service';
import { databaseSchema, users } from '../database/database-schema';
import { userHealthMetrics } from '../database/database-schema';
import { desc, eq, sql, and, like, not } from 'drizzle-orm';
import { UpdateUserDto } from './dto/update-user.dto';
import { PasswordService } from '../auth/services/password.service';
import { Role, UserStatus } from '../common/enums/permissions.enums';
import * as bcrypt from 'bcryptjs';
import { GetUsersDto } from './dto/get-users.dto';
import { CloudinaryService } from '../utils/cloudinary.service';
import { Logger } from '@nestjs/common';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly passwordService: PasswordService,
    private readonly cloudinaryService: CloudinaryService,
  ) {}

  async createUser(userDto: RegisterUserDto): Promise<any> {
    const existingUser = await this.getUserByEmail(userDto.email);

    if (existingUser) throw new ConflictException('Email already in use.');

    const hashedPassword = await this.passwordService.hashPassword(
      userDto.password,
    );

    await this.drizzle.db.insert(users).values({
      email: userDto.email,
      password: hashedPassword,
      name: userDto.name,
      status: userDto.status,
    } as typeof users.$inferInsert);

    return { message: 'User created successfully' };
  }

  async getUserByEmail(email: string): Promise<any> {
    return this.drizzle.db.query.users.findFirst({
      where: eq(databaseSchema.users.email, email),
    });
  }

  async getUserProfile(id: string) {
    const user = await this.drizzle.db.query.users.findFirst({
      where: eq(databaseSchema.users.userId, id),
    });

    if (!user) throw new NotFoundException('User not found');

    const healthMetrics =
      await this.drizzle.db.query.userHealthMetrics.findFirst({
        where: eq(userHealthMetrics.userId, id),
      });

    const recentRecommendation =
      await this.drizzle.db.query.recommendations.findFirst({
        where: eq(databaseSchema.recommendations.userId, id),
        orderBy: desc(databaseSchema.recommendations.createdAt),
      });

    const dailyCalorieTarget =
      recentRecommendation?.nutritionTargets['Daily Calorie Target'];

    // Sanitize the response by removing sensitive data
    const {
      password,
      refreshToken,
      resetToken,
      resetTokenExpiry,
      ...sanitizedUser
    } = user;

    // Calculate target weight based on weight goal
    const currentWeight = Number(healthMetrics?.weight);


    // Calculate BMI if height and weight are available
    const height = Number(healthMetrics?.height);
    const weight = Number(healthMetrics?.weight);
    const bmi =
      height && weight ? (weight / Math.pow(height / 100, 2)).toFixed(1) : 0;

    const userProfile: any = {
      id: user.userId,
      name: user.name,
      email: user.email,
      profilePicture: user.profilePicture,
      // Add the new fields from backend
      targetWeight: healthMetrics?.targetWeight,
      startingWeight: healthMetrics?.startingWeight,
      bmi: bmi,
      dailyCalorieTarget: dailyCalorieTarget,
      // For backward compatibility
      healthMetrics: {
        age: healthMetrics?.age,
        weight: healthMetrics?.weight,
        height: healthMetrics?.height,
        gender: healthMetrics?.gender,
        activityLevel: healthMetrics?.activityLevel,
        dietaryPreference: healthMetrics?.dietaryPreference,
        location: healthMetrics?.location,
        healthIssues: healthMetrics?.healthIssues,
        fasting: healthMetrics?.fasting,
        goals: healthMetrics?.weightGoal,
      },
    };

    return {
      userProfile
    };
  }

  async findOneByEmail(email: string): Promise<any> {
    const user = await this.drizzle.db.query.users.findFirst({
      where: eq(databaseSchema.users.email, email),
    });

    return user;
  }

  async findOneById(userId: string) {
    const user = await this.drizzle.db.query.users.findFirst({
      where: eq(users.userId, userId),
    });

    if (!user) throw new NotFoundException('User not found');
    const { password, ...data } = user;
    return data;
  }

  async userProfile(id: string) {
    const user = await this.drizzle.db.query.users.findFirst({
      where: eq(databaseSchema.users.userId, id),
    });

    if (!user) throw new NotFoundException('User not found');
    return user;
  }

  async updateUserStatus(userId: string, status: string) {
    const user = await this.findOneById(userId);

    // Validate the status
    if (!Object.values(UserStatus).includes(status as UserStatus)) {
      throw new BadRequestException('Invalid status value');
    }

    if (user.status === status) {
      return { message: 'User Status already updated' };
    }

    await this.drizzle.db.execute(sql`
    UPDATE "Users" SET status = ${status}, "updated_at" = NOW() WHERE user_id = ${userId}
    `);

    return {
      message: 'User status updated successfully',
    };
  }

  async setRefreshToken(
    id: string,
    refreshToken: string | null,
    roles: Role[],
  ) {
    let hashedRefreshToken: string | null = null;
    if (refreshToken) {
      hashedRefreshToken = await bcrypt.hash(refreshToken, 8);
    }

    if (roles.includes(Role.ADMIN)) {
      await this.drizzle.db.execute(sql`
        UPDATE "Admins"
        SET "refresh_token" = ${hashedRefreshToken}
        WHERE "admin_id" = ${id};
      `);

      const admin = await this.drizzle.db.query.admins.findFirst({
        where: eq(databaseSchema.admins.adminId, id),
      });

      if (!admin) throw new NotFoundException('Admin not found');
      return admin;
    }

    if (roles.includes(Role.SUPERADMIN)) {
      await this.drizzle.db.execute(sql`
        UPDATE "SuperAdmins"
        SET "refresh_token" = ${hashedRefreshToken}
        WHERE "super_admin_id" = ${id};
      `);

      const superAdmin = await this.drizzle.db.query.superAdmins.findFirst({
        where: eq(databaseSchema.superAdmins.superAdminId, id),
      });

      if (!superAdmin) throw new NotFoundException('SuperAdmin not found');
      return superAdmin;
    }

    if (roles.includes(Role.USER)) {
      await this.drizzle.db.execute(sql`
        UPDATE "Users"
        SET "refresh_token" = ${hashedRefreshToken}
        WHERE "user_id" = ${id};
      `);

      const user = await this.drizzle.db.query.users.findFirst({
        where: eq(databaseSchema.users.userId, id),
      });

      if (!user) throw new NotFoundException('User not found');
      return user;
    }
  }

  async getAllUsers(getUsersDto: GetUsersDto) {
    const { page = 1, limit = 10, search } = getUsersDto;
    const offset = (page - 1) * limit;

    const conditions = [];
    if (search) {
      conditions.push(
        like(users.name, `%${search}%`),
        like(users.email, `%${search}%`),
      );
    }

    const [data, countResult] = await Promise.all([
      this.drizzle.db.query.users.findMany({
        where: conditions.length > 0 ? and(...conditions) : undefined,
        limit,
        offset,
      }),
      this.drizzle.db
        .select({ count: sql<number>`count(*)` })
        .from(users)
        .where(conditions.length > 0 ? and(...conditions) : undefined),
    ]);

    const total = Number(countResult[0].count);

    return {
      users: data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async updateUser(userId: string, updateData: UpdateUserDto) {
    const user = await this.drizzle.db.query.users.findFirst({
      where: eq(users.userId, userId),
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (updateData.email) {
      const existingUser = await this.drizzle.db.query.users.findFirst({
        where: eq(users.email, updateData.email),
      });
      if (existingUser && existingUser.userId !== userId) {
        throw new BadRequestException('Email already exists');
      }
    }

    await this.drizzle.db
      .update(users)
      .set({ ...updateData })
      .where(eq(users.userId, userId));

    return { message: 'User updated successfully' };
  }

  async deleteUser(userId: string) {
    const user = await this.drizzle.db.query.users.findFirst({
      where: eq(users.userId, userId),
    });
    if (!user) throw new NotFoundException('User not found');

    // Soft delete - update status to DELETED instead of actually deleting
    await this.drizzle.db.execute(sql`
      UPDATE "Users"
      SET "status" = ${UserStatus.DELETED}
      WHERE "user_id" = ${userId};
    `);

    return { message: 'User deleted successfully' };
  }

  async markEmailAsConfirmed(email: string) {
    await this.drizzle.db.execute(sql`
      UPDATE "Users"
      SET "is_email_confirmed" = true, "status" = ${UserStatus.ACTIVE}
      WHERE "email" = ${email};
    `);

    return { message: 'Email confirmed successfully' };
  }

  async getRecentUsers(limit: number = 10) {
    const deletedUser = UserStatus.DELETED;


    const recentUsers = await this.drizzle.db
      .select({
        userId: users.userId,
        name: users.name,
        email: users.email,
        createdAt: users.createdAt,
        status: users.status
      })
      .from(users)
      .where(not(eq(users.status, deletedUser)))
      .orderBy(desc(users.createdAt))
      .limit(limit);

    return recentUsers;
  }

  async uploadProfileImage(userId: string, file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    const user = await this.drizzle.db.query.users.findFirst({
      where: (users, { eq }) => eq(users.userId, userId),
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    try {
      // Upload new image to Cloudinary
      const uploadResult = await this.cloudinaryService.uploadImage(
        file.buffer,
        `user_${userId}_${Date.now()}`,
      );

      // Update the profile picture URL in the database
      await this.drizzle.db.execute(
        sql`UPDATE "Users" SET profile_picture = ${uploadResult.secure_url} WHERE user_id = ${userId}`
      );

      return {
        message: 'Profile image uploaded successfully',
        profilePicture: uploadResult.secure_url,
      };
    } catch (error) {
      this.logger.error('Failed to upload profile image:', error);
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Failed to upload profile image'
      );
    }
  }
}
