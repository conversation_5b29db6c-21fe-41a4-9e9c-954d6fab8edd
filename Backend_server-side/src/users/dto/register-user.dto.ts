import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, MinLength } from 'class-validator';

export class RegisterUserDto {
  @ApiProperty({
    example: 'abc',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    example: '123123',
  })
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({
    example: 'active',
  })
  @IsNotEmpty()
  @IsString()
  status: string;
}

export class RegisterUserResponseDto {
  @IsString()
  message: string;

  @ApiProperty({
    example: 'token',
  })
  token: string;
}
