import { IsOptional, IsString, <PERSON><PERSON>UI<PERSON>, <PERSON>O<PERSON>, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

class HealthMetricsDto {
  @IsOptional()
  age?: number;

  @IsOptional()
  weight?: number;

  @IsOptional()
  height?: number;

  @IsOptional()
  @IsString()
  healthIssues?: string;

  @IsOptional()
  @IsString()
  goals?: string;
}

export class UpdateUserDto {

  @IsOptional()
  @IsString()
  @ApiProperty({
    example: "John Doe"
  })
  name?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({
    example: "<EMAIL>"
  })
  email?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({
    example: "password"
  })
  password?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({
    example: "08012345678"
  })
  phone?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({
    example: "active"
  })
  status?: string; // Default is 'pending' in schema

  @IsOptional()
  @IsString()
  @ApiProperty({
    example: "base64-encoded-image"
  })
  profilePicture?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => HealthMetricsDto)
  healthMetrics?: HealthMetricsDto;
}
