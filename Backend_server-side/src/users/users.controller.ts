import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Request,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { RegisterUserDto } from './dto/register-user.dto';
import { ApiBody, ApiConsumes, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UpdateUserDto } from './dto/update-user.dto';
import { Roles } from '../common/decorators/role.decorator';
import { Role } from '../common/enums/permissions.enums';
import { SkipThrottle } from '@nestjs/throttler';
import { GetUsersDto } from './dto/get-users.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';

@Controller('users')
@ApiTags('users')
export class UsersController {
  constructor(private usersService: UsersService) {}

  @Post('create')
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  async register(@Body() registerBody: RegisterUserDto): Promise<any> {
    console.log(registerBody);
    return await this.usersService.createUser(registerBody);
  }
  
  @SkipThrottle({default: true})
  @Get('profile')
  @Roles(Role.USER)
  async getUserProfile(@Request() req) {
    return await this.usersService.getUserProfile(req.user.id);
  }

  @Get('recent')
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  @ApiOperation({ summary: 'Get recently registered users' })
  @ApiResponse({ status: 200, description: 'Returns recent users.' })
  async getRecentUsers(@Query('limit') limit: number = 10) {
    return this.usersService.getRecentUsers(limit);
  }

  @Get()
  @SkipThrottle({default: true})
  @Roles(Role.ADMIN, Role.SUPERADMIN)
  @ApiOperation({ summary: 'Get all users with pagination and filtering' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns paginated users with optional filtering.' 
  })
  async getAllUsers(@Query() queryDto: GetUsersDto) {
    return await this.usersService.getAllUsers(queryDto);
  }

  @Get(':id')
  async getUserById(@Param('id') userId: string) {
    return await this.usersService.findOneById(userId);
  }

  @Put(':id')
  async updateUser(
    @Param('id') userId: string,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    const response = await this.usersService.updateUser(userId, updateUserDto);
    return response
  }

  @Delete(':id')
  async deleteUser(@Param('id') userId: string) {
    return await this.usersService.deleteUser(userId);
  }

  @Patch('status/:id')
  async updateUserStatus(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<{ message: string }> {
    return await this.usersService.updateUserStatus(id, updateUserDto.status);
  }

  @Post('profile-image')
  @Roles(Role.USER)
  @UseInterceptors(
    FileInterceptor('profileImage', {
      storage: memoryStorage(),
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
          return cb(
            new BadRequestException('Only image files are allowed!'),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        profileImage: {
          type: 'string',
          format: 'binary',
          description: 'Profile image file (JPG, PNG, JPEG, GIF)',
        },
      },
    },
  })
  async uploadProfileImage(
    @Request() req,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.usersService.uploadProfileImage(req.user.id, file);
  }
}
