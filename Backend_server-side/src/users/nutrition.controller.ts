import { Controller, Get, Request } from "@nestjs/common";
import { UsersService } from "./users.service";
import { NutritionService } from "./nutrition.service";
import { ApiTags } from "@nestjs/swagger";
import { SkipThrottle } from "@nestjs/throttler/dist";

@Controller("users/nutrition")
@ApiTags("nutrition")
@SkipThrottle({default: true})
export class NutritionController {
    constructor(private userService: UsersService,
        private nutirtionService: NutritionService
    ) {}

    @Get("macro-nutrients")
    async getMacronutrientData(@Request() req) {
        return await this.nutirtionService.getMacronutrientDistribution(req.user.id);
    }

    @Get("nutrients")
    async getMicronutrientData(@Request() req) {
        return await this.nutirtionService.getNutrientData(req.user.id);
    }

    @Get("weekly-target-calorie-data")
    async getWeeklyTargetCalorieData(@Request() req) {
        return await this.nutirtionService.getWeeklyTargetCalorieData(req.user.id);
    }
}
