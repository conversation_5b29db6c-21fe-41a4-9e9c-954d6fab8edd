import { Injectable } from '@nestjs/common';
import { and, desc, eq, gte, sql } from "drizzle-orm";
import { recommendations, userHealthMetrics } from "../database/database-schema";
import { DrizzleService } from "../database/drizzle.service";

@Injectable()
export class NutritionService {
    constructor (private readonly drizzle: DrizzleService) {}

    async getMacronutrientDistribution(userId: string) {
        const result = await this.drizzle.db.select({
            protein: sql<number>`sum((${recommendations.nutritionTargets} ->> 'protein')::numeric)`,
            carbohydrate: sql<number>`sum((${recommendations.nutritionTargets} ->> 'carbohydrates')::numeric)`,
            fat: sql<number>`sum((${recommendations.nutritionTargets} ->> 'fat')::numeric)`
        })
        .from(recommendations)
        .where(eq(recommendations.userId, userId));

        // Calculate the distribution
        const total = Number(result[0].protein || 0) + Number(result[0].carbohydrate || 0) + Number(result[0].fat || 0);
        
        if (total === 0) {
            return {
                protein: 33,
                carbohydrate: 33,
                fat: 34
            };
        }

        const macro_distribution = {
            protein: Math.round((Number(result[0].protein || 0) / total) * 100),
            carbohydrate: Math.round((Number(result[0].carbohydrate || 0) / total) * 100),
            fat: Math.round((Number(result[0].fat || 0) / total) * 100),
        };

        return macro_distribution;
    }

  async getNutrientData(userId: string) {
    // Daily RDA (mg or g, matching nutritionTargets units)
    const RDA = {
      sodium: 2300, // mg
      fiber: 28, // g
      sugar: 50, // g
      protein: 50, // g
      carbohydrate: 300, // g
      fat: 70, // g
    };

    // Query sum of nutrient targets across all records
    const result = await this.drizzle.db
      .select({
        sodium: sql<number>`COALESCE(sum((${recommendations.nutritionTargets} ->> 'sodium')::numeric), 0)`,
        fiber: sql<number>`COALESCE(sum((${recommendations.nutritionTargets} ->> 'fiber')::numeric), 0)`,
        sugar: sql<number>`COALESCE(sum((${recommendations.nutritionTargets} ->> 'sugar')::numeric), 0)`,
        protein: sql<number>`COALESCE(sum((${recommendations.nutritionTargets} ->> 'protein')::numeric), 0)`,
        carbohydrate: sql<number>`COALESCE(sum((${recommendations.nutritionTargets} ->> 'carbohydrates')::numeric), 0)`,
        fat: sql<number>`COALESCE(sum((${recommendations.nutritionTargets} ->> 'fat')::numeric), 0)`,
      })
      .from(recommendations)
      .where(eq(recommendations.userId, userId));

    console.log('Raw nutrient query result:', result);

    // Extract values and validate
    const sodium = Math.min(Number(result[0]?.sodium || 0), RDA.sodium * 2);
    const fiber = Math.min(Number(result[0]?.fiber || 0), RDA.fiber * 2);
    const sugar = Math.min(Number(result[0]?.sugar || 0), RDA.sugar * 2);
    const protein = Math.min(Number(result[0]?.protein || 0), RDA.protein * 2);
    const carbohydrate = Math.min(Number(result[0]?.carbohydrate || 0), RDA.carbohydrate * 2);
    const fat = Math.min(Number(result[0]?.fat || 0), RDA.fat * 2);

    console.log('Validated nutrient values:', {
      sodium,
      fiber,
      sugar,
      protein,
      carbohydrate,
      fat
    });

    // Calculate percentages
    const nutrientDistribution = [
      {
        nutrient: 'Sodium',
        amount: Math.min(Math.round((sodium / RDA.sodium) * 100), 100),
        fullMark: 100,
      },
      {
        nutrient: 'Fiber',
        amount: Math.min(Math.round((fiber / RDA.fiber) * 100), 100),
        fullMark: 100,
      },
      {
        nutrient: 'Sugar',
        amount: Math.min(Math.round((sugar / RDA.sugar) * 100), 100),
        fullMark: 100,
      },
      {
        nutrient: 'Protein',
        amount: Math.min(Math.round((protein / RDA.protein) * 100), 100),
        fullMark: 100,
      },
      {
        nutrient: 'Carbohydrate',
        amount: Math.min(Math.round((carbohydrate / RDA.carbohydrate) * 100), 100),
        fullMark: 100,
      },
      {
        nutrient: 'Fat',
        amount: Math.min(Math.round((fat / RDA.fat) * 100), 100),
        fullMark: 100,
      },
    ];

    console.log('Nutrient distribution:', nutrientDistribution);
    return nutrientDistribution;
  }

async getWeeklyTargetCalorieData(userId: string) {
    const today = new Date(); 
    const dayOfWeek = today.getDay();

    const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;

    console.log(mondayOffset);
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() + mondayOffset);
    startOfWeek.setHours(0, 0, 0, 0);
    console.log(startOfWeek);
    const recommendationData = await this.drizzle.db
    .select({
        recordedAt: recommendations.createdAt,
        targetCalories: sql<number>`(${recommendations.nutritionTargets} ->> 'daily_calorie_target')::numeric`,
    })
      .from(recommendations)
      .where(
        and(
          eq(recommendations.userId, userId),
          gte(recommendations.createdAt, startOfWeek)
        )
      );
      
      console.log("recomm", recommendationData);
    // Fetch fallback daily calorie target
    const fallbackData = await this.drizzle.db
      .select({
        targetCalories: sql<number>`(${recommendations.nutritionTargets} ->> 'daily_calorie_target')::numeric`,
      })
      .from(recommendations)
      .where(eq(recommendations.userId, userId))
      .orderBy(desc(recommendations.createdAt))
      .limit(1);

    const dailyCalorieTarget = Math.round(Number(fallbackData[0]?.targetCalories)) || 2200;
    // Generate data for all 7 days
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const result = days.map((day, index) => {
      const dayDate = new Date(startOfWeek);
      dayDate.setDate(startOfWeek.getDate() + index);
      const dayData = recommendationData.find(
        (entry) =>
          new Date(entry.recordedAt).toDateString() === dayDate.toDateString()
      );

      const now = new Date();
      const isPastDay = (new Date(dayDate.getTime() + 24 * 60 * 60 * 1000)) < now;

      return {
        day,
        target: dayData
          ? Math.ceil(Number(dayData.targetCalories))
          : isPastDay
          ? dailyCalorieTarget
          : null,
        isFallback: !dayData && isPastDay
      };
    });

    // Return all days, but keep null for future days
    return result;
  }
}

