import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { sql } from 'drizzle-orm';
import { config } from 'dotenv';
import { resolve } from 'path';

config({ path: resolve(__dirname, '../.env.test') });

async function globalTeardown() {
  const pool = new Pool({
    host: process.env.POSTGRES_HOST,
    port: Number(process.env.POSTGRES_PORT),
    user: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
  });

  const db = drizzle(pool);

  // Clean up all tables
  await db.execute(sql`TRUNCATE TABLE "Users", "UserHealthMetrics", "WeightHistory", "Admins", "SuperAdmins", "FoodLogs", "Blogs", "Feedback", "Notifications", "EthiopianFoods" CASCADE`);

  // Close the pool
  await pool.end();
}

export default globalTeardown; 