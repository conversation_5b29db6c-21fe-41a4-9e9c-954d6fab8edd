import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;

  // Define public paths accessible without authentication
  const publicPaths = [
    '/', '/login', '/signup',
    '/adminLogin', '/adminSignup',
    '/superAdminLogin', '/superAdminSignup',
    '/forgot-password', '/reset-password',
    '/Blog',
  ];

  // Check if the current path is public
  const isPublicPath = publicPaths.some(publicPath => 
    path === publicPath || path.startsWith(publicPath + '/')
  );

  // Get tokens and role from cookies
  const accessToken = request.cookies.get('access_token')?.value;
  const refreshToken = request.cookies.get('refresh_token')?.value;
  const userRole = request.cookies.get('user_role')?.value || '';
  const isAuthenticated = !!(accessToken && refreshToken);

  // Helper to determine the appropriate login page based on path
  const getLoginPath = (path: string) => {
    if (path.startsWith('/admin') || path.startsWith('/superAdmin')) return '/adminLogin';
    return '/login';
  };

  // Helper to determine the dashboard based on role
  const getDashboardPath = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return '/admin';
      case 'SUPERADMIN':
        return '/superAdmin';
      default:
        return '/userDashboard';
    }
  };

  // If it's a public path, allow access without authentication
  if (isPublicPath) {
    // Only redirect authenticated users away from login/signup pages to their dashboard
    if (isAuthenticated && (path === '/login' || path === '/signup' || path === '/adminLogin' || path === '/adminSignup')) {
      const dashboard = getDashboardPath(userRole);
      return NextResponse.redirect(new URL(dashboard, request.url));
    }
    return NextResponse.next();
  }

  // Handle unauthenticated users for protected paths
  if (!isAuthenticated) {
    const loginPath = getLoginPath(path);
    const redirectUrl = new URL(loginPath, request.url);
    redirectUrl.searchParams.set('redirect', path);
    return NextResponse.redirect(redirectUrl);
  }

  // Role-based access control for authenticated users
  if (path.startsWith('/admin') && userRole !== 'ADMIN' && userRole !== 'SUPERADMIN') {
    return NextResponse.redirect(new URL('/userDashboard', request.url));
  }
  
  if (path.startsWith('/superAdmin') && userRole !== 'SUPERADMIN') {
    return NextResponse.redirect(new URL('/userDashboard', request.url));
  }
  
  if (path.startsWith('/userDashboard') && userRole !== 'USER') {
    const dashboard = getDashboardPath(userRole);
    if (path !== dashboard) {
      return NextResponse.redirect(new URL(dashboard, request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};