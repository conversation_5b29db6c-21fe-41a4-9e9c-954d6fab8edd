import { create } from 'zustand';
import axios, { AxiosError } from 'axios';

// Initialize healthMetrics from localStorage with fallback
const getInitialHealthMetrics = (): HealthMetrics | null => {
  if (typeof window !== 'undefined') {
    try {
      const saved = localStorage.getItem('healthMetrics');
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (e) {
      console.error('Failed to parse stored health metrics:', e);
    }
  }
  return null;
};

export interface HealthMetrics {
  age: number;
  gender: string;
  height: number;
  weight: number;
  activity_level: string;
  dietary_preference: string;
  weight_goal: string;
  health_issues: string[];
  fasting: string;
  location: string;
}

interface NutritionalDetail {
  daily_calorie_target: number;
  protein: number;
  sugar: number;
  sodium: number;
  calories: number;
  carbohydrates: number;
  fiber: number;
  fat: number;
}

interface Nutrition {
  calories: number;
  protein: number;
  fat: number;
  carbohydrates: number;
  fiber: number;
  sodium: number;
}

export interface FoodRecommendation {
  local_name: string;
  ingredient: string;
  location: string;
  region: string;
  category: string;
  nutrition: Nutrition;
  serving_size_grams: number;
  serving_nutrition: Nutrition;
}

interface Recommendations {
  breakfast: FoodRecommendation[];
  lunch: FoodRecommendation[];
  snack: FoodRecommendation[];
  dinner: FoodRecommendation[];
}

export interface PredictionResponse {
  nutritional_detail: NutritionalDetail;
  recommendations: Recommendations;
}

interface ValidInputs {
  height: any;
  age: any;
  weight: any;
  genders: string[];
  activity_levels: string[];
  dietary_preferences: string[];
  weight_goals: string[];
  health_issues: string[];
  fasting_statuses: string[];
  supported_locations: string[];
}

interface RecommendationState {
  healthMetrics: HealthMetrics | null;
  predictionResponse: PredictionResponse | null;
  validInputs: ValidInputs | null;
  isLoading: boolean;
  error: string | null;
  setHealthMetrics: (data: HealthMetrics) => void;
  fetchValidInputs: () => Promise<ValidInputs>;
  fetchPrediction: (healthMetrics?: HealthMetrics) => Promise<PredictionResponse>;
  reset: () => void;
}

const API_BASE_URL = 'https://ml-backend.duckdns.org';
const SAVE_API_URL = process.env.NEXT_PUBLIC_API_URL;

export const useRecommendationStore = create<RecommendationState>((set, get) => ({
  healthMetrics: getInitialHealthMetrics(),
  predictionResponse: null,
  validInputs: null,
  isLoading: false,
  error: null,

  /**
   * Sets health metrics and persists to localStorage
   * @param data - Health metrics data
   */
  setHealthMetrics: (data) => {
    const sanitizedData = {
      ...data,
      health_issues: Array.isArray(data.health_issues) ? data.health_issues : [],
    };
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('healthMetrics', JSON.stringify(sanitizedData));
      } catch (e) {
        console.error('Failed to save health metrics to localStorage:', e);
      }
    }
    set({ healthMetrics: sanitizedData, error: null });
  },

  /**
   * Fetches valid input options from the API
   * @returns Promise resolving to valid inputs
   */
  fetchValidInputs: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await axios.get(`${API_BASE_URL}/valid-inputs`, {
        timeout: 10000,
      });
      set({ validInputs: response.data, isLoading: false });
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<{ detail?: string | { msg: string }[] | object }>;
      const errorMessage =
        axiosError.response?.data?.detail
          ? Array.isArray(axiosError.response.data.detail)
            ? axiosError.response.data.detail.map((err) => (typeof err === 'object' ? err.msg : err)).join(', ')
            : typeof axiosError.response.data.detail === 'object'
              ? JSON.stringify(axiosError.response.data.detail)
              : axiosError.response.data.detail
          : axiosError.message || 'Failed to fetch valid inputs';
      set({ error: errorMessage, isLoading: false });
      throw new Error(errorMessage);
    }
  },

  /**
   * Fetches meal plan prediction based on health metrics
   * @param healthMetrics - Optional health metrics; uses store state if not provided
   * @returns Promise resolving to prediction response
   */
  fetchPrediction: async (healthMetrics?: HealthMetrics) => {
    set({ isLoading: true, error: null });
    try {
      const metrics = healthMetrics || get().healthMetrics;
      if (!metrics) {
        throw new Error('No health metrics provided');
      }

      // Validate required fields
      const requiredFields: (keyof HealthMetrics)[] = [
        'age',
        'gender',
        'height',
        'weight',
        'activity_level',
        'dietary_preference',
        'weight_goal',
        'fasting',
        'location',
      ];
      for (const field of requiredFields) {
        if (metrics[field] === undefined || metrics[field] === null || metrics[field] === '') {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      const formattedData: HealthMetrics = {
        age: metrics.age,
        gender: metrics.gender,
        height: metrics.height,
        weight: metrics.weight,
        activity_level: metrics.activity_level,
        dietary_preference: metrics.dietary_preference,
        weight_goal: metrics.weight_goal,
        health_issues: Array.isArray(metrics.health_issues) ? metrics.health_issues : [],
        fasting: metrics.fasting,
        location: metrics.location,
      };

      const response = await axios.post(`${API_BASE_URL}/predict-and-recommend`, formattedData, {
        headers: {
          accept: 'application/json',
          'Content-Type': 'application/json',
        },
        timeout: 15000,
      });

      set({ predictionResponse: response.data, isLoading: false });

      // Save recommendation to backend
      try {
        console.log('Saving recommendation data:', { userData: formattedData, prediction: response.data });
        await axios.post(
          `${SAVE_API_URL}/recommendations/save`,
          {
            userData: formattedData,
            prediction: response.data,
          },
          {
            withCredentials: true,
            timeout: 10000,
          }
        );
      } catch (saveError) {
        console.warn('Failed to save recommendation:', saveError);
      }

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<{ detail?: string | { msg: string }[] | object }>;
      let errorMessage = 'Failed to fetch prediction';

      if (axiosError.response?.data?.detail) {
        if (Array.isArray(axiosError.response.data.detail)) {
          errorMessage = axiosError.response.data.detail
            .map((err) => (typeof err === 'object' ? err.msg || JSON.stringify(err) : err))
            .join(', ');
        } else if (typeof axiosError.response.data.detail === 'object') {
          errorMessage = JSON.stringify(axiosError.response.data.detail);
        } else {
          errorMessage = axiosError.response.data.detail;
        }
      } else if (axiosError.message) {
        errorMessage = axiosError.message;
      }

      set({ error: errorMessage, isLoading: false });
      throw new Error(errorMessage);
    }
  },

  /**
   * Resets the store to initial state
   */
  reset: () => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem('healthMetrics');
      } catch (e) {
        console.error('Failed to clear health metrics from localStorage:', e);
      }
    }
    set({
      healthMetrics: null,
      predictionResponse: null,
      validInputs: null,
      isLoading: false,
      error: null,
    });
  },
}));