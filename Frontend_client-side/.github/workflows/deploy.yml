name: CI/CD Pipeline for Next.js to AWS EC2

on:
  push:
    branches:
      - main

jobs:
  build-and-lint:
    runs-on: ubuntu-latest
    steps:
      # Checkout the latest main branch commit
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: main
          fetch-depth: 0 # Fetch full history for git diff

      # Check for changes in source files
      - name: Check for source changes
        id: source-changes
        run: |
          if git diff --name-only HEAD^ HEAD | grep -E '^(app|components|contexts|hooks|lib|middleware\.ts|next\.config\.js|next\.config\.ts|postcss\.config\.mjs|server\.js|services|store|tailwind\.config\.ts|tsconfig\.json|utils)/'; then
            echo "Source files changed, proceeding with build"
            echo "has_changes=true" >> $GITHUB_OUTPUT
          else
            echo "No source files changed, skipping build"
            echo "has_changes=false" >> $GITHUB_OUTPUT
          fi

      # Set up Node.js with cached node_modules
      - name: Setup Node.js
        if: steps.source-changes.outputs.has_changes == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: '18.x'
          cache: 'npm'

      # Restore cached node_modules
      - name: Cache node_modules
        id: cache-npm
        if: steps.source-changes.outputs.has_changes == 'true'
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-npm-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-

      # Install dependencies if cache miss
      - name: Install dependencies
        if: steps.source-changes.outputs.has_changes == 'true' && steps.cache-npm.outputs.cache-hit != 'true'
        run: npm install

      # Run linting
      - name: Run linting
        if: steps.source-changes.outputs.has_changes == 'true'
        run: npm run lint

      # Build Next.js app
      - name: Build Next.js app
        if: steps.source-changes.outputs.has_changes == 'true'
        run: npm run build

      # Archive build artifacts
      - name: Archive build artifacts
        if: steps.source-changes.outputs.has_changes == 'true'
        run: |
          zip -r nextjs-app.zip .next package.json package-lock.json public Dockerfile app components contexts hooks lib middleware.ts next.config.js next.config.ts postcss.config.mjs server.js services store tailwind.config.ts tsconfig.json utils || {
            echo "Failed to create zip archive";
            exit 1;
          }

      # Upload build artifacts
      - name: Upload build artifacts
        if: steps.source-changes.outputs.has_changes == 'true'
        uses: actions/upload-artifact@v4
        with:
          name: nextjs-app
          path: nextjs-app.zip

  deploy-production:
    needs: build-and-lint
    runs-on: ubuntu-latest
    steps:
      # Checkout code for deployment scripts
      - name: Checkout code
        uses: actions/checkout@v4

      # Download build artifacts (if they exist)
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: nextjs-app
          path: .
        continue-on-error: true # Continue if no new artifact (no source changes)

      # Verify artifact or use existing files
      - name: Verify artifact
        run: |
          if [ -f nextjs-app.zip ]; then
            echo "Artifact found, proceeding with deployment"
          else
            echo "No new artifact, deploying existing files"
          fi

      # Configure AWS credentials
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      # Test SSH connectivity to EC2
      - name: Test SSH Connectivity
        env:
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_SSH_KEY: ${{ secrets.EC2_SSH_KEY }}
        run: |
          echo "$EC2_SSH_KEY" > key.pem
          chmod 400 key.pem
          ssh -o StrictHostKeyChecking=no -i key.pem $EC2_USER@$EC2_HOST "echo 'SSH connection successful'" || {
            echo "SSH failed. Checking key and host...";
            ls -l key.pem;
            head -n 2 key.pem;
            ping -c 4 $EC2_HOST || echo "Ping failed";
            telnet $EC2_HOST 22 || echo "Telnet to port 22 failed";
            exit 1;
          }
          rm key.pem

      # Deploy to EC2, conditionally rebuild Docker image
      - name: Deploy to AWS EC2 (Production)
        env:
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_SSH_KEY: ${{ secrets.EC2_SSH_KEY }}
        run: |
          echo "$EC2_SSH_KEY" > key.pem
          chmod 400 key.pem
          
          # Transfer artifact if it exists
          if [ -f nextjs-app.zip ]; then
            scp -o StrictHostKeyChecking=no -i key.pem nextjs-app.zip $EC2_USER@$EC2_HOST:/home/<USER>/Frontend_client-side/
          fi

          ssh -o StrictHostKeyChecking=no -i key.pem $EC2_USER@$EC2_HOST << EOF
            sudo apt-get update
            sudo apt-get install -y unzip || { echo "Failed to install unzip"; exit 1; }

            # Install Docker if not present
            if ! command -v docker >/dev/null 2>&1; then
              sudo apt-get install -y docker.io
              sudo systemctl start docker
              sudo systemctl enable docker
              sudo usermod -aG docker ubuntu
            fi

            cd /home/<USER>/Frontend_client-side || { echo "Failed to change directory"; exit 1; }

            # Unzip artifact if it exists
            if [ -f nextjs-app.zip ]; then
              mv /home/<USER>/Frontend_client-side/nextjs-app.zip .
              unzip -o nextjs-app.zip -d . || { echo "Failed to unzip artifact"; exit 1; }
              rm -f nextjs-app.zip
            fi

            # Update .env with secrets
            echo "NODE_ENV=production" > .env
            echo "NEXT_PUBLIC_API_URL=${{ secrets.NEXT_PUBLIC_API_URL }}" >> .env
            echo "NEXT_PUBLIC_GOOGLE_CLIENT_ID=${{ secrets.NEXT_PUBLIC_GOOGLE_CLIENT_ID }}" >> .env
            echo "API_BASE_URL=${{ secrets.API_BASE_URL }}" >> .env
            echo "NEXT_PUBLIC_COOKIE_DOMAIN=${{ secrets.NEXT_PUBLIC_COOKIE_DOMAIN }}" >> .env

            # Check for changes in Dockerfile or package.json to rebuild image
            REBUILD_IMAGE=false
            if [ -f nextjs-app.zip ] && unzip -l nextjs-app.zip | grep -E 'Dockerfile|package\.json'; then
              REBUILD_IMAGE=true
            fi

            # Stop and remove existing container
            docker stop nutri-focus-app || true
            docker rm nutri-focus-app || true

            # Build new image only if required
            if [ "$REBUILD_IMAGE" = true ]; then
              docker build --no-cache -t nutri-focus_v2 . || { echo "Failed to build Docker image"; exit 1; }
            fi

            # Run new container
            docker run -d --name nutri-focus-app -p 3000:3000 --env-file .env nutri-focus_v2 || { echo "Failed to start Docker container"; exit 1; }

            # Clean up unused images
            docker image prune -f

            # Sync Git repository to match deployed commit
            if [ -d .git ]; then
              git fetch origin
              git reset --hard origin/main || { echo "Failed to sync Git repository"; exit 1; }
            fi
          EOF
          rm key.pem