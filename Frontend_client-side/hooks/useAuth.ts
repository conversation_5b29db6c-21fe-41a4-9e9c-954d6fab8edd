import { useState, useEffect } from "react";
import { authService } from "../services/auth.service";

export function useAuth() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuth();
  }, []);

  useEffect(() => {
    // Log the updated user value whenever it changes
    console.log("Updated user:", user);
  }, [user]);

  const checkAuth = async () => {
    try {
      const userData = await authService.getCurrentUser();
      console.log("Fetched userData:", userData);
      setUser(userData);
      console.log("user", user);
    } catch (error) {
      console.log("Error fetching user:", error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials :any) => {
    const response = await authService.userLogin(credentials);
    const userData = await authService.getCurrentUser();
    setUser(userData);
    return response;
  };

  const superAdminLogin = async (credentials :any) => {
    const response = await authService.superAdminLogin(credentials);
    const userData = await authService.getCurrentUser();
    setUser(userData);
    return response;
  };

  const logout = async () => {
    await authService.logout();
    setUser(null);
  };

  return { 
    user, 
    loading, 
    login, 
    superAdminLogin, 
    logout,
    isAuthenticated: !!user 
  };
}