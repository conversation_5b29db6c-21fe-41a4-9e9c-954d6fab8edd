{"name": "project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@react-oauth/google": "^0.12.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "fns": "^1.0.0", "js-cookie": "^3.0.5", "lucide-react": "^0.476.0", "next": "^15.3.2", "nstall": "^0.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-spinners": "^0.15.0", "recharts": "^2.15.1", "sonner": "^2.0.3", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.3", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}