"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  Search,
  X,
  Check,
  Trash2,
  Refresh<PERSON><PERSON>,
  AlertCircle,
} from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "react-hot-toast";
import notificationService, {
  Notification,
} from "@/services/notification.service";

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedNotification, setSelectedNotification] =
    useState<Notification | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 1,
  });
  const [counts, setCounts] = useState({
    all: 0,
    unread: 0,
    read: 0,
  });

  useEffect(() => {
    loadNotifications();
  }, [activeTab, selectedCategory, pagination.page]);

  // Fetch notifications and counts from the backend
  const loadNotifications = async () => {
    try {
      setLoading(true);
      setIsRefreshing(true);
      setError(null);

      // Define parameters based on active tab
      let isRead: boolean | undefined = undefined;
      if (activeTab === "read") isRead = true;
      if (activeTab === "unread") isRead = false;

      // Create params object
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        ...(isRead !== undefined && { isRead }),
        ...(selectedCategory !== 'all' && { category: selectedCategory })
      };

      // Fetch notifications with proper params
      const response = await notificationService.getNotifications(
        pagination.page,
        pagination.limit,
        isRead,
        selectedCategory !== 'all' ? selectedCategory : undefined
      );

      // Fetch counts for all, read, and unread
      const countsResponse = await notificationService.getNotificationCounts();

      if (response.data.length === 0 && pagination.page > 1) {
        setPagination({
          ...pagination,
          page: 1,
        });
        return;
      }

      setNotifications(response.data);
      setPagination({
        ...pagination,
        total: response.meta.total,
        totalPages: response.meta.totalPages || 1,
      });
      setCounts({
        all: countsResponse.all || response.meta.total,
        unread: countsResponse.unread || response.data.filter(n => n.status === "unread").length,
        read: countsResponse.read || response.data.filter(n => n.status === "read").length,
      });
    } catch (err) {
      console.error("Error loading notifications:", err);
      setError("Failed to load notifications. Please try again later.");
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Mark a notification as read
  const handleMarkAsRead = async (id: string, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }

    try {
      await notificationService.markAsRead(id);
      setNotifications(
        notifications.map((notification) =>
          notification.id === id
            ? { ...notification, status: "read" }
            : notification
        )
      );
      setCounts({
        ...counts,
        unread: counts.unread - 1,
        read: counts.read + 1,
      });
      toast.success("Notification marked as read.");
    } catch (err) {
      toast.error("Failed to mark notification as read.");
    }
  };

  // Mark all notifications as read
  const handleMarkAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead();
      setNotifications(
        notifications.map((notification) => ({
          ...notification,
          status: "read",
        }))
      );
      setCounts({
        ...counts,
        unread: 0,
        read: counts.all,
      });
      toast.success("All notifications marked as read.");
    } catch (err) {
      toast.error("Failed to mark all notifications as read.");
    }
  };

  // Delete a notification
  const handleDelete = async (id: string, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }

    try {
      await notificationService.deleteNotification(id);
      setNotifications(
        notifications.filter((notification) => notification.id !== id)
      );
      setCounts({
        ...counts,
        all: counts.all - 1,
        unread: notifications.find(n => n.id === id)?.status === "unread" ? counts.unread - 1 : counts.unread,
        read: notifications.find(n => n.id === id)?.status === "read" ? counts.read - 1 : counts.read,
      });
      toast.success("Notification deleted successfully.");
    } catch (err) {
      toast.error("Failed to delete notification.");
    }
  };

  // Show notification details
  const handleShowDetails = (notification: Notification) => {
    setSelectedNotification(notification);
    setIsDetailsOpen(true);

    if (notification.status === "unread") {
      handleMarkAsRead(notification.id);
    }
  };

  // Filter notifications based on search query
  const filteredNotifications = notifications.filter((notification) => {
    const matchesSearch =
      notification.from.toLowerCase().includes(searchQuery.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSearch;
  });

  // Get all unique categories
  const categories = [
    "all",
    ...Array.from(new Set(notifications.map((n) => n.category || "Other"))),
  ];

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();

    if (now.getTime() - date.getTime() < 24 * 60 * 60 * 1000) {
      return formatDistanceToNow(date, { addSuffix: true });
    }
    return format(date, "MMM d, yyyy 'at' h:mm a");
  };

  // Get notification type icon

  const getNotificationTypeIcon = (type?: string) => {
    switch (type) {
      case "success":
        return (
          <Badge className="bg-green-500 text-white h-5 w-5 flex items-center justify-center p-0 rounded-full">
            <Check className="h-3 w-3" />
          </Badge>
        );
      case "warning":
        return (
          <Badge className="bg-yellow-500 text-white h-5 w-5 flex items-center justify-center p-0 rounded-full">
            <AlertCircle className="h-3 w-3" />
          </Badge>
        );
      case "error":
        return (
          <Badge className="bg-red-500 text-white h-5 w-5 flex items-center justify-center p-0 rounded-full">
            <X className="h-3 w-3" />
          </Badge>
        );
      case "info":
      default:
        return (
          <Badge className="bg-blue-500 text-white h-5 w-5 flex items-center justify-center p-0 rounded-full">
            <Bell className="h-3 w-3" />
          </Badge>
        );
    }
  };

  // Handle pagination
  const handleNextPage = () => {
    if (pagination.page < pagination.totalPages) {
      setPagination({
        ...pagination,
        page: pagination.page + 1,
      });
    }
  };

  const handlePrevPage = () => {
    if (pagination.page > 1) {
      setPagination({
        ...pagination,
        page: pagination.page - 1,
      });
    }
  };

  // Loading skeleton
  if (loading && !isRefreshing) {
    return (
      <div className="container mx-auto py-8 px-4 md:px-6 bg-background">
        <div className="flex justify-between items-center mb-6">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-10 w-40" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div
                  key={i}
                  className="flex items-start space-x-4 p-4 border rounded-md"
                >
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-4 w-full" />
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto py-8 px-4 md:px-6 bg-background">
        <Card className="border-destructive/20">
          <CardContent className="pt-6 flex flex-col items-center justify-center text-center">
            <AlertCircle className="h-12 w-12 text-destructive mb-4" />
            <CardTitle className="text-destructive mb-2">
              Unable to Load Notifications
            </CardTitle>
            <CardDescription className="mb-4 text-foreground">{error}</CardDescription>
            <Button onClick={loadNotifications} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" /> Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 bg-background">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold flex items-center text-foreground">
            <Bell className="mr-2 h-5 w-5" /> Notifications
            {counts.unread > 0 && (
              <Badge variant="secondary" className="ml-2">
                {counts.unread} new
              </Badge>
            )}
          </h1>
          <p className="mt-1 text-foreground">
            Stay updated with important information and alerts
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="lg"
            onClick={handleMarkAllAsRead}
            disabled={counts.unread === 0}
          >
            <Check className="mr-1.5 h-4 w-4" /> Mark All Read
          </Button>

          <DropdownMenu>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={loadNotifications}>
                <RefreshCw className="mr-2 h-4 w-4" /> Refresh
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex-1 w-full sm:max-w-md">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Search notifications..."
                  className="pl-9 pr-9 border-border text-foreground"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery("")}
                    className="absolute right-2.5 top-2.5 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2 w-full sm:w-auto">
              <Button
                variant="outline"
                size="icon"
                onClick={loadNotifications}
                disabled={isRefreshing}
                className="flex-shrink-0"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
                />
              </Button>
            </div>
          </div>

          <Tabs
            defaultValue="all"
            className="mt-4"
            onValueChange={setActiveTab}
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">
                All{" "}
                <Badge variant="outline" className="ml-2">
                  {counts.all}
                </Badge>
              </TabsTrigger>
              <TabsTrigger value="unread">
                Unread{" "}
                <Badge variant="outline" className="ml-2">
                  {counts.unread}
                </Badge>
              </TabsTrigger>
              <TabsTrigger value="read">
                Read{" "}
                <Badge variant="outline" className="ml-2">
                  {counts.read}
                </Badge>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>

        <CardContent>
          {filteredNotifications.length === 0 ? (
            <div className="text-center py-12">
              <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
              <h3 className="text-lg font-medium text-foreground mb-1">
                No notifications found
              </h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery ||
                selectedCategory !== "all" ||
                activeTab !== "all"
                  ? "Try adjusting your filters or search term"
                  : "You don't have any notifications at the moment"}
              </p>
              {(searchQuery ||
                selectedCategory !== "all" ||
                activeTab !== "all") && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery("");
                    setSelectedCategory("all");
                    setActiveTab("all");
                  }}
                >
                  Clear Filters
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  onClick={() => handleShowDetails(notification)}
                  className={`
                    flex items-start p-4 rounded-md border transition-colors cursor-pointer
                    ${
                      notification.status === "unread"
                        ? "border-border/50 bg-background/50"
                        : "border-border"
                    }
                  `}
                >
                  <div className="flex-shrink-0 mr-4">
                    <div className="relative">
                      <div className="h-10 w-10 rounded-full bg-muted text-foreground flex items-center justify-center">
                        {notification.from.charAt(0).toUpperCase()}
                      </div>
                      <div className="absolute -bottom-0.5 -right-0.5">
                        {getNotificationTypeIcon(notification.type)}
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium text-foreground truncate">
                        {notification.from}
                      </h4>
                      {notification.status === "unread" && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          New
                        </Badge>
                      )}
                    </div>

                    <p className="text-sm text-muted-foreground line-clamp-2 mb-1">
                      {notification.message}
                    </p>

                    <div className="flex items-center justify-between text-xs text-muted-foreground mt-2">
                      <div className="flex items-center">
                        <span>{formatDate(notification.createdAt)}</span>
                        {notification.category && (
                          <>
                            <span className="mx-2">•</span>
                            <Badge
                              variant="outline"
                              className="text-xs font-normal"
                            >
                              {notification.category}
                            </Badge>
                          </>
                        )}
                      </div>

                      <div className="flex items-center space-x-2">
                        {notification.status === "unread" && (
                          <Button
                            variant="ghost"
                            size="lg"
                            className="h-7 px-2 text-xs text-foreground hover:bg-muted"
                            onClick={(e) =>
                              handleMarkAsRead(notification.id, e)
                            }
                          >
                            <Check className="h-3.5 w-3.5 mr-1" /> Mark read
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="lg"
                          className="h-7 px-2 text-xs text-destructive hover:bg-destructive/10"
                          onClick={(e) => handleDelete(notification.id, e)}
                        >
                          <Trash2 className="h-3.5 w-3.5 mr-1" /> Delete
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>

        <CardFooter className="flex justify-between border-t p-4">
          <div className="flex items-center gap-2 text-muted-foreground">
            <p className="text-sm">
              Showing {filteredNotifications.length} of {pagination.total}{" "}
              notifications
            </p>
            <p className="text-sm">
              Page {pagination.page} of {pagination.totalPages}
            </p>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="lg"
              onClick={handlePrevPage}
              disabled={pagination.page <= 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={handleNextPage}
              disabled={pagination.page >= pagination.totalPages}
            >
              Next
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={loadNotifications}
              disabled={isRefreshing}
            >
              <RefreshCw
                className={`mr-2 h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          </div>
        </CardFooter>
      </Card>

      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Notification Details</DialogTitle>
            <DialogDescription>
              View the complete details of this notification
            </DialogDescription>
            {selectedNotification?.category && (
              <div className="mt-1">
                <Badge variant="outline">{selectedNotification.category}</Badge>
              </div>
            )}
          </DialogHeader>

          {selectedNotification && (
            <>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-foreground">From</h4>
                  <div className="text-foreground">{selectedNotification.from}</div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-foreground">Message</h4>
                  <div className="text-foreground">{selectedNotification.message}</div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-foreground">Received</h4>
                  <div className="text-foreground">
                    {format(new Date(selectedNotification.createdAt), "PPpp")}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-foreground">Status</h4>
                  <div className="flex items-center">
                    {selectedNotification.status === "read" ? (
                      <Badge variant="outline">Read</Badge>
                    ) : (
                      <Badge>Unread</Badge>
                    )}
                  </div>
                </div>
              </div>

              <Separator />

              <DialogFooter className="sm:justify-between">
                <Button
                  variant="outline"
                  size="lg"
                  className="text-destructive hover:bg-destructive/10"
                  onClick={() => {
                    handleDelete(selectedNotification.id);
                    setIsDetailsOpen(false);
                  }}
                >
                  <Trash2 className="mr-2 h-4 w-4" /> Delete
                </Button>

                <Button size="lg" onClick={() => setIsDetailsOpen(false)}>
                  Close
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}