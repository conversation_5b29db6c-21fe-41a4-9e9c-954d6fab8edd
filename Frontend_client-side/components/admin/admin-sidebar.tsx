"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  Users,
  FileText,
  Bell,
  User,
  X,
  MessageSquare,
} from "lucide-react";
import { useEffect, useState } from "react";
import { Menu } from "lucide-react";
import { heroData } from "@/data/HomeData/data";
import Image from "next/image";
import { Button } from "../ui/button";
import { cn } from "@/lib/utils";

export default function AdminSidebar() {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  const hero = heroData[0];
  const [currentTheme, setCurrentTheme] = useState<string>("light");
  const logoSrc = currentTheme === "dark" ? hero.logo2 : hero.logo1;

  useEffect(() => {
    const getTheme = () => {
      if (typeof window !== "undefined") {
        const storedTheme = localStorage.getItem("ui-theme");
        return storedTheme || "light";
      }
      return "light";
    };
    setCurrentTheme(getTheme());

    if (typeof window !== "undefined") {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (
            mutation.type === "attributes" &&
            mutation.attributeName === "class"
          ) {
            const isDarkMode = document.documentElement.classList.contains("dark");
            setCurrentTheme(isDarkMode ? "dark" : "light");
          }
        });
      });

      observer.observe(document.documentElement, { attributes: true });

      const handleStorageChange = () => {
        setCurrentTheme(getTheme());
      };
      window.addEventListener("storage", handleStorageChange);

      return () => {
        observer.disconnect();
        window.removeEventListener("storage", handleStorageChange);
      };
    }
  }, []);

  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  const toggleSidebar = () => setIsOpen(!isOpen);

  const menuItems = [
    { name: "Dashboard", icon: LayoutDashboard, path: "/admin" },
    { name: "Manage user", icon: Users, path: "/admin/users" },
    { name: "Blogs", icon: FileText, path: "/admin/blogs" },
    { name: "Feedback", icon: MessageSquare, path: "/admin/feedback" }, // Added Feedback
    { name: "Notification", icon: Bell, path: "/admin/notification" },
    { name: "Account", icon: User, path: "/admin/account" },
  ];

  return (
    <>
      {/* Menu toggle button */}
      <div className="md:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={toggleSidebar}
          aria-label={isOpen ? "Close menu" : "Open menu"}
        >
          {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
        </Button>
      </div>

      {/* Sidebar */}
      <div
        className={cn(
          "fixed md:relative md:translate-x-0 z-40 w-64 h-full bg-card border-r transition-transform duration-300 ease-in-out",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        {/* Logo section */}
        <div className="p-5 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center w-full">
              <Image
                src={logoSrc}
                alt="NutriFocus Logo"
                width={120}
                height={40}
                className="h-6 w-auto m-auto"
              />
            </div>
            {/* Close button for mobile */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsOpen(false)}
                aria-label="Close menu"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="p-4 space-y-2">
          {menuItems.map((item) => {
            if (!item) return null;
            const isActive = pathname === item.path;
            return (
              <Link
                key={item.name}
                href={item.path}
                className={cn(
                  "flex items-center p-3 rounded-md transition-colors",
                  isActive
                    ? "bg-primary/10 text-primary font-medium border-l-4 border-primary"
                    : "hover:bg-muted"
                )}
              >
                <item.icon className="h-5 w-5 mr-3" />
                <span>{item.name}</span>
              </Link>
            );
          })}
        </nav>
      </div>
    </>
  );
}