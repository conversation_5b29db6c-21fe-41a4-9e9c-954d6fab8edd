"use client";
import type React from "react";
import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { User, LogOut, Settings, ChevronDown } from "lucide-react";
import Link from "next/link";
import { toast } from "react-hot-toast";
import { api } from "@/services/api";
import { adminService } from "@/services/admin.service";

interface UserProfileProps {
  initialUserData?: {
    name: string;
    email: string;
    image?: string | null;
  };
}

export default function AdminProfile({
  initialUserData = {
    name: "Guest User",
    email: "<EMAIL>",
    image: null,
  },
}: UserProfileProps) {
  const [userData, setUserData] = useState(initialUserData);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchAdminProfile = async () => {
      try {
        const response = await adminService.getAdminProfile();
        setUserData({
          name: response.name,
          email: response.email,
          image: response.profilePicture || null
        });
      } catch (error) {
        console.error("Failed to fetch admin profile:", error);
        toast.error("Failed to load profile data");
      }
    };

    fetchAdminProfile();
  }, []);

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append('file', file);

      const response = await api.post('/admins/profile/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.profilePicture) {
        setUserData(prev => ({
          ...prev,
          image: response.data.profilePicture
        }));
        toast.success('Profile image updated successfully');
      }
    } catch (error) {
      console.error('Failed to upload profile image:', error);
      toast.error('Failed to upload profile image');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  return (
    <div className="relative">
      <button
        className="flex items-center space-x-1 focus:outline-none"
        onClick={toggleDropdown}
        aria-expanded={isDropdownOpen}
        aria-haspopup="true"
      >
        <div className="relative w-9 h-9 rounded-full overflow-hidden border-2 border-gray-200">
          {userData.image ? (
            <Image
              src={userData.image}
              alt="User profile"
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
              <User className="h-5 w-5 text-gray-500" />
            </div>
          )}
        </div>
        <ChevronDown className="h-4 w-4 text-gray-500" />
      </button>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleImageChange}
        accept="image/*"
        className="hidden"
      />

      {isDropdownOpen && (
        <div className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div
                className="relative w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200 cursor-pointer"
                onClick={handleImageClick}
              >
                {userData.image ? (
                  <Image
                    src={userData.image}
                    alt="User profile"
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                    <User className="h-6 w-6 text-gray-500" />
                  </div>
                )}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {userData.name}
                </p>
                <p className="text-xs text-gray-500">{userData.email}</p>
              </div>
            </div>
            <button
              onClick={handleImageClick}
              className="mt-3 text-xs text-blue-600 hover:text-blue-800"
            >
              Change profile photo
            </button>
          </div>
          <div className="py-1">
            <Link
              href="/admin/account"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <Settings className="h-4 w-4 mr-2" />
              Account Settings
            </Link>
            <button
              onClick={() => {
                // Handle logout
                localStorage.removeItem('token');
                window.location.href = '/login';
              }}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign out
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
