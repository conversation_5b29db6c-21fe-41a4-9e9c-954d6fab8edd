"use client";
import type React from "react";
import { useState, useRef } from "react";
import Image from "next/image";
import { User, LogOut, Settings, ChevronDown } from "lucide-react";
import Link from "next/link";
interface UserProfileProps {
  initialUserData?: {
    name: string;
    email: string;
    image?: string | null;
  };
}

export default function SperAdminP({
  initialUserData = {
    name: "Guest User",
    email: "<EMAIL>",
    image: null,
  },
}: UserProfileProps) {
  const [userData, setUserData] = useState(initialUserData);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setUserData({
          ...userData,
          image: event.target?.result as string,
        });
        // In a real app, we would upload the image to our server here
      };
      reader.readAsDataURL(file);
    }
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  return (
    <div className="relative">
      <button
        className="flex items-center space-x-1 focus:outline-none"
        onClick={toggleDropdown}
        aria-expanded={isDropdownOpen}
        aria-haspopup="true"
      >
        <div className="relative w-9 h-9 rounded-full overflow-hidden border-2 border-gray-200">
          {userData.image ? (
            <Image
              src={userData.image || "/placeholder.svg"}
              alt="User profile"
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
              <User className="h-5 w-5 text-gray-500" />
            </div>
          )}
        </div>
        <ChevronDown className="h-4 w-4 text-gray-500" />
      </button>
      {/* Hidden file input for image upload */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleImageChange}
        accept="image/*"
        className="hidden"
      />

      {/* Dropdown menu */}
      {isDropdownOpen && (
        <div className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div
                className="relative w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200 cursor-pointer"
                onClick={handleImageClick}
              >
                {userData.image ? (
                  <Image
                    src={userData.image || "/placeholder.svg"}
                    alt="User profile"
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                    <User className="h-6 w-6 text-gray-500" />
                  </div>
                )}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {userData.name}
                </p>
                <p className="text-xs text-gray-500">{userData.email}</p>
              </div>
            </div>
            <button
              onClick={handleImageClick}
              className="mt-3 text-xs text-blue-600 hover:text-blue-800"
            >
              Change profile photo
            </button>
          </div>
          <div className="py-1" role="menu" aria-orientation="vertical">
            <Link
              href="/superAdmin/account"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              role="menuitem"
              onClick={() => setIsDropdownOpen(false)}
            >
              <User className="mr-3 h-4 w-4 text-gray-500" />
              Your Profile
            </Link>
            <Link
              href="/settings"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              role="menuitem"
              onClick={() => setIsDropdownOpen(false)}
            >
              <Settings className="mr-3 h-4 w-4 text-gray-500" />
              Settings
            </Link>
            <button
              className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              role="menuitem"
              onClick={() => setIsDropdownOpen(false)}
            >
              <LogOut className="mr-3 h-4 w-4 text-gray-500" />
              Sign out
            </button>
          </div>
        </div>
      )}

      <div className="hidden group-hover:block absolute right-0 top-full mt-1 bg-white rounded-md shadow-md p-2 z-50 w-48 text-sm">
        <p className="font-medium text-gray-900">{userData.name}</p>
        <p className="text-gray-500 text-xs">{userData.email}</p>
      </div>
    </div>
  );
}
