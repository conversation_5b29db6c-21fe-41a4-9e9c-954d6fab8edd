"use client";
import {
  <PERSON>cilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  Search,
  BanIcon,
  ClockIcon,
  X,
  UserIcon,
  MailIcon,
  PlusIcon,
} from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { adminService, Admin, AdminStatus } from "@/services/admin.service";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format } from "date-fns";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Label } from "@/components/ui/label";
import Image from "next/image";


export default function ManageAdmins() {
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [addAdminDialogOpen, setAddAdminDialogOpen] = useState(false);
  const [selectedAdmin, setSelectedAdmin] = useState<Admin | null>(null);
  const [editFormData, setEditFormData] = useState({
    name: "",
    email: "",
    status: "",
  });

  const [newAdminData, setNewAdminData] = useState({
    name: "",
    email: "",
    password: "",
    status: AdminStatus.ACTIVE,
  });

  const fetchAdmins = async (searchParams = {}) => {
    try {
      setIsLoading(true);
      console.log("Fetching admins with params:", {
        page: pagination.page,
        limit: pagination.limit,
        search: searchTerm,
        status: statusFilter === "all" ? undefined : statusFilter,
      });

      const response = await adminService.getAllAdmins({
        page: pagination.page,
        limit: pagination.limit,
        search: searchTerm || undefined,
        status: statusFilter === "all" ? undefined : statusFilter,
        ...searchParams,
      });

      console.log("API response:", response);

      if (response && response.admins && Array.isArray(response.admins)) {
        setAdmins(response.admins);
        setPagination({
          page: response.meta?.page || 1,
          limit: response.meta?.limit || 10,
          total: response.meta?.total || response.admins.length,
          totalPages: response.meta?.totalPages || 1,
        });
      } else {
        console.error("API did not return expected data format:", response);
        setAdmins([]);
        setPagination({
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
        });
        toast.error("Received invalid data format from server");
      }
    } catch (error) {
      console.error("Error fetching admins:", error);
      toast.error("Failed to load admins");
      setAdmins([]);
      setPagination({
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAdmins();
  }, []);

  const handleClearSearch = () => {
    setSearchTerm("");
  };

  const handleSearch = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchAdmins({ page: 1 });
  };

  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status);
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchAdmins({
      page: 1,
      status: status || undefined,
    });
  };

  const handleAddAdmin = () => {
    setAddAdminDialogOpen(true);
  };

  const handleEdit = (admin: Admin) => {
    setSelectedAdmin(admin);
    setEditFormData({
      name: admin.name,
      email: admin.email,
      status: admin.status,
    });
    setEditDialogOpen(true);
  };

  const handleSaveEdit = async () => {
    if (!selectedAdmin) return;

    try {
      const response = await adminService.updateAdmin(
        selectedAdmin.adminId,
        editFormData
      );
      fetchAdmins();
      toast.success(response.message || "Admin updated successfully");
      setEditDialogOpen(false);
    } catch (error: any) {
      console.error("Error updating admin:", error);
      toast.error(error.response?.data?.message || "Failed to update admin");
    }
  };

  const handleDelete = (admin: Admin) => {
    setSelectedAdmin(admin);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!selectedAdmin) return;

    try {
      await adminService.deleteAdmin(selectedAdmin.adminId);
      fetchAdmins();
      toast.success("Admin deleted successfully");
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting admin:", error);
      toast.error("Failed to delete admin");
    }
  };

  const handleToggleStatus = async (id: string, currentStatus: string) => {
    const newStatus = window.prompt("Select new status:", currentStatus);

    if (!newStatus || newStatus === currentStatus) return;

    if (!Object.values(AdminStatus).includes(newStatus as AdminStatus)) {
      toast.error("Invalid status value");
      return;
    }

    try {
      await adminService.updateAdminStatus(id, newStatus);
      setAdmins((prev) =>
        prev.map((admin) =>
          admin.adminId === id ? { ...admin, status: newStatus } : admin
        )
      );
      toast.success("Admin status updated");
    } catch (error) {
      console.error("Error updating admin status:", error);
      toast.error("Failed to update status");
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMMM d, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case AdminStatus.ACTIVE:
        return "bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-100 dark:border-green-700";
      case AdminStatus.INACTIVE:
        return "bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-100 dark:border-red-700";
      case AdminStatus.SUSPENDED:
        return "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-100 dark:border-yellow-700";
      case AdminStatus.PENDING:
        return "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-100 dark:border-blue-700";
      case AdminStatus.DELETED:
        return "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case AdminStatus.ACTIVE:
        return <CheckCircleIcon className="h-4 w-4 mr-1" />;
      case AdminStatus.INACTIVE:
        return <XCircleIcon className="h-4 w-4 mr-1" />;
      case AdminStatus.SUSPENDED:
        return <BanIcon className="h-4 w-4 mr-1" />;
      case AdminStatus.PENDING:
        return <ClockIcon className="h-4 w-4 mr-1" />;
      case AdminStatus.DELETED:
        return <TrashIcon className="h-4 w-4 mr-1" />;
      default:
        return null;
    }
  };

  async function handleCreateAdmin() {
    try {
      if (!newAdminData.name || !newAdminData.email || !newAdminData.password) {
        toast.error("Please fill out all required fields.");
        return;
      }

      const response = await adminService.createAdmin(newAdminData);

      fetchAdmins();

      toast.success("Admin created successfully!");

      setAddAdminDialogOpen(false);
      setNewAdminData({
        name: "",
        email: "",
        password: "",
        status: AdminStatus.ACTIVE,
      });
    } catch (error: any) {
      toast.error(error.message || "Failed to create admin. Please try again.");
    }
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <div className="max-w-6xl mx-auto mb-10">
        <h1 className="text-3xl font-bold text-center text-foreground mb-3">
          Admin Management
        </h1>
        <p className="text-muted-foreground text-center mb-8">
          Manage admin accounts, permissions, and access to the system
        </p>

        <div className="flex flex-col sm:flex-row gap-3 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              type="text"
              placeholder="Search by name or email..."
              className="pl-10 h-11"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
            />
            {searchTerm && (
              <button
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                onClick={handleClearSearch}
                aria-label="Clear search"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>

          <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
            <SelectTrigger className="w-full sm:w-[180px] h-11">
              <SelectValue placeholder="Status Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value={AdminStatus.ACTIVE}>Active</SelectItem>
              <SelectItem value={AdminStatus.INACTIVE}>Inactive</SelectItem>
              <SelectItem value={AdminStatus.SUSPENDED}>Suspended</SelectItem>
              <SelectItem value={AdminStatus.PENDING}>Pending</SelectItem>
              <SelectItem value={AdminStatus.DELETED}>Deleted</SelectItem>
            </SelectContent>
          </Select>

          <Button className="h-11" onClick={handleSearch}>
            Search
          </Button>

          <Button
            className="h-11 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800"
            onClick={handleAddAdmin}
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Admin
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="max-w-6xl mx-auto">
          <Skeleton className="h-12 w-full mb-4 bg-muted dark:bg-muted/50" />
          <Skeleton className="h-64 w-full bg-muted dark:bg-muted/50" />
        </div>
      ) : admins.length > 0 ? (
        <div className="max-w-6xl mx-auto">
          <div className="overflow-x-auto rounded-md border border-border dark:border-border">
            <table className="min-w-full divide-y divide-border dark:divide-border">
              <thead className="bg-muted dark:bg-muted/50">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-muted-foreground dark:text-muted-foreground uppercase tracking-wider"
                  >
                    Admin
                  </th>
                  <th


                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-muted-foreground dark:text-muted-foreground uppercase tracking-wider"
                  >
                    Email
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-muted-foreground dark:text-muted-foreground uppercase tracking-wider"
                  >
                    Joined
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-muted-foreground dark:text-muted-foreground uppercase tracking-wider"
                  >
                    Status
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium text-muted-foreground dark:text-muted-foreground uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-background dark:bg-background divide-y divide-border dark:divide-border">
                {admins.map((admin) => (
                  <tr
                    key={admin.adminId}
                    className="hover:bg-muted/50 dark:hover:bg-muted/50 transition-colors"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-full overflow-hidden bg-muted dark:bg-muted/50 border border-border dark:border-border flex items-center justify-center text-muted-foreground dark:text-muted-foreground mr-4">
                          {admin.profilePicture ? (
                            <Image
                              src={admin.profilePicture}
                              alt={admin.name}
                              width={80}
                              height={80}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <UserIcon className="h-5 w-5" />
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-foreground dark:text-foreground">
                            {admin.name}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-muted-foreground dark:text-muted-foreground">
                        <MailIcon className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
                        <span>{admin.email}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-muted-foreground dark:text-muted-foreground">
                        {formatDate(admin.createdAt)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge
                        variant="outline"
                        className={`flex items-center w-fit ${getStatusBadgeColor(
                          admin.status
                        )}`}
                      >
                        {getStatusIcon(admin.status)}
                        {admin.status.charAt(0).toUpperCase() +
                          admin.status.slice(1)}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="lg"
                          className="text-blue-600 dark:text-blue-400 dark:border-border"
                          onClick={() => handleEdit(admin)}
                        >
                          <PencilIcon className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="lg"
                          className="text-red-600 dark:text-red-400 dark:border-border"
                          onClick={() => handleDelete(admin)}
                        >
                          <TrashIcon className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {pagination.totalPages > 1 && (
            <div className="flex justify-center items-center gap-2 mt-10">
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  setPagination((prev) => ({ ...prev, page: 1 }));
                  fetchAdmins({ page: 1 });
                }}
                disabled={pagination.page === 1 || isLoading}
              >
                First
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  const prevPage = pagination.page - 1;
                  setPagination((prev) => ({ ...prev, page: prevPage }));
                  fetchAdmins({ page: prevPage });
                }}
                disabled={pagination.page === 1 || isLoading}
              >
                Previous
              </Button>

              <span className="mx-2 text-sm text-muted-foreground dark:text-muted-foreground">
                Page {pagination.page} of {pagination.totalPages}
              </span>

              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  const nextPage = pagination.page + 1;
                  setPagination((prev) => ({ ...prev, page: nextPage }));
                  fetchAdmins({ page: nextPage });
                }}
                disabled={
                  pagination.page === pagination.totalPages || isLoading
                }
              >
                Next
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  setPagination((prev) => ({
                    ...prev,
                    page: pagination.totalPages,
                  }));
                  fetchAdmins({ page: pagination.totalPages });
                }}
                disabled={
                  pagination.page === pagination.totalPages || isLoading
                }
              >
                Last
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-12 max-w-md mx-auto">
          <UserIcon className="h-12 w-12 mx-auto text-muted-foreground dark:text-muted-foreground mb-4" />
          <h3 className="text-xl font-medium text-foreground dark:text-foreground mb-2">
            No admins found
          </h3>
          <p className="text-muted-foreground dark:text-muted-foreground">
            No admins match your search criteria. Try adjusting your search term
            or status filter.
          </p>
          {searchTerm && (
            <Button
              variant="outline"
              className="mt-4"
              onClick={handleClearSearch}
            >
              Clear Search
            </Button>
          )}
        </div>
      )}

      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Admin</DialogTitle>
            <DialogDescription>
              Make changes to the admin's information here.
            </DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleSaveEdit();
            }}
          >
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={editFormData.name}
                  onChange={(e) =>
                    setEditFormData({ ...editFormData, name: e.target.value })
                  }
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  Email
                </Label>
                <Input
                  id="email"
                  value={editFormData.email}
                  onChange={(e) =>
                    setEditFormData({ ...editFormData, email: e.target.value })
                  }
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="status" className="text-right">
                  Status
                </Label>
                <Select
                  value={editFormData.status}
                  onValueChange={(value) =>
                    setEditFormData({ ...editFormData, status: value })
                  }
                >
                  <SelectTrigger id="status" className="col-span-3">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={AdminStatus.ACTIVE}>Active</SelectItem>
                    <SelectItem value={AdminStatus.INACTIVE}>Inactive</SelectItem>
                    <SelectItem value={AdminStatus.SUSPENDED}>
                      Suspended
                    </SelectItem>
                    <SelectItem value={AdminStatus.PENDING}>Pending</SelectItem>
                    <SelectItem value={AdminStatus.DELETED}>Deleted</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setEditDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit">Save changes</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will mark the admin as deleted. The admin will no longer
              be able to access the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          {selectedAdmin && (
            <div className="mt-2 p-3 bg-muted dark:bg-muted/50 rounded-md">
              <div className="flex items-center mb-2">
                <UserIcon className="h-4 w-4 mr-2 text-muted-foreground dark:text-muted-foreground" />
                <span className="font-medium">{selectedAdmin.name}</span>
              </div>
              <div className="flex items-center text-sm text-muted-foreground dark:text-muted-foreground">
                <MailIcon className="h-3.5 w-3.5 mr-2" />
                <span>{selectedAdmin.email}</span>
              </div>
            </div>
          )}
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={addAdminDialogOpen} onOpenChange={setAddAdminDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Admin</DialogTitle>
            <DialogDescription>
              Create a new admin account by filling out the form below.
            </DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleCreateAdmin();
            }}
          >
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="new-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="new-name"
                  value={newAdminData.name}
                  onChange={(e) =>
                    setNewAdminData({ ...newAdminData, name: e.target.value })
                  }
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="new-email" className="text-right">
                  Email
                </Label>
                <Input
                  id="new-email"
                  type="email"
                  value={newAdminData.email}
                  onChange={(e) =>
                    setNewAdminData({ ...newAdminData, email: e.target.value })
                  }
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="new-password" className="text-right">
                  Password
                </Label>
                <Input
                  id="new-password"
                  type="password"
                  value={newAdminData.password}
                  onChange={(e) =>
                    setNewAdminData({ ...newAdminData, password: e.target.value })
                  }
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="new-status" className="text-right">
                  Status
                </Label>
                <Select
                  value={newAdminData.status}
                  onValueChange={(value: AdminStatus) =>
                    setNewAdminData({ ...newAdminData, status: value })
                  }
                >
                  <SelectTrigger id="new-status" className="col-span-3">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={AdminStatus.ACTIVE}>Active</SelectItem>
                    <SelectItem value={AdminStatus.INACTIVE}>Inactive</SelectItem>
                    <SelectItem value={AdminStatus.SUSPENDED}>
                      Suspended
                    </SelectItem>
                    <SelectItem value={AdminStatus.PENDING}>Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setAddAdminDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit">Create Admin</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}