"use client"

import {useEffect, useState, type FormEvent, type ChangeEvent } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useAuthContext } from "@/contexts/AuthContext"
import { toast } from "react-hot-toast"
import Image from "next/image"
import Link from "next/link" 
import { ArrowRight, LogIn, Lock, Mail, AlertCircle, Loader2, CheckCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardDescription, CardTitle } from "@/components/ui/card"
import GoogleLoginButton from "./google/GoogleLoginButton"
import { heroData } from "@/data/HomeData/data"

export default function Login() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [formError, setFormError] = useState<string | null>(null)
  const router = useRouter()
  const searchParams = useSearchParams()
  const { login } = useAuthContext()  
  const hero = heroData[0]
  const [currentTheme, setCurrentTheme] = useState<string>("light")

    useEffect(() => {
      // Function to get current theme
      const getTheme = () => {
        if (typeof window !== "undefined") {
          const storedTheme = localStorage.getItem("ui-theme")
          return storedTheme || "light"
        }
        return "light"
      }
      // Set initial theme
      setCurrentTheme(getTheme())
      
      // Create a MutationObserver to watch for theme class changes on <html>
      if (typeof window !== "undefined") {
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (
              mutation.type === "attributes" && 
              mutation.attributeName === "class"
            ) {
              // HTML class changed, check if dark mode is active
              const isDarkMode = document.documentElement.classList.contains("dark")
              setCurrentTheme(isDarkMode ? "dark" : "light")
            }
          })
        })
        
        // Start observing the document with the configured parameters
        observer.observe(document.documentElement, { attributes: true })
        
        // Also listen for storage events (for changes in other tabs)
        const handleStorageChange = () => {
          setCurrentTheme(getTheme())
        }
        window.addEventListener("storage", handleStorageChange)
        
        // Cleanup
        return () => {
          observer.disconnect()
          window.removeEventListener("storage", handleStorageChange)
        }
      }
    }, [searchParams])
    
    const logoSrc = currentTheme === "dark" ? hero.logo2 : hero.logo1

  const handleEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value)
    if (formError) setFormError(null)
  }

  const handlePasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value)
    if (formError) setFormError(null)
  }

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)
    setFormError(null)

    try {
      await login({ email, password })
      toast.success("Login successful")
      router.push("/userDashboard")
    } catch (error: any) {
      console.log("Login error:", error);
      console.error("Login error details:", error)

      // Handle the error message from the server
      const errorMessage = error.response?.data?.message || error.message || "Invalid email or password"
      setFormError(errorMessage)
      
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-b from-background to-muted/30">
      <div className="w-full max-w-5xl animate-fade-in">
        <Card className="w-full shadow-lg overflow-hidden border-muted/20">
          <div className="grid md:grid-cols-2 gap-0">
            {/* Form Section */}
            <div className="p-6 md:p-8 space-y-6">
              <div className="flex justify-between items-center">
                <Link href="/" className="flex items-center space-x-2">
                  <Image
                    src={logoSrc || "/placeholder.svg"}
                    alt="NutriFocus Logo"
                    width={120}
                    height={40}
                    className="h-6 w-auto"
                  />
                </Link>

                <div className="flex items-center space-x-4">
                  <div className="text-sm font-medium text-primary">Login</div>
                  <Link
                    href="/signup"
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Sign Up
                  </Link>
                </div>
              </div>

              <div className="space-y-6 animate-fade-in">
                <div className="space-y-2 text-center">
                  <CardTitle className="text-2xl font-bold">Welcome back</CardTitle>
                  <CardDescription>Enter your credentials to access your account</CardDescription>
                </div>

                <div className="flex justify-center">
                  <GoogleLoginButton />
                </div>

                <div className="relative flex items-center">
                  <div className="flex-grow border-t border-muted"></div>
                  <span className="mx-4 text-xs text-muted-foreground">or continue with email</span>
                  <div className="flex-grow border-t border-muted"></div>
                </div>

                {formError && (
                  <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md flex items-start">
                    <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                    <p>{formError}</p>
                  </div>
                )}

                <form className="space-y-4" onSubmit={handleSubmit}>
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium">
                      Email
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={handleEmailChange}
                        className="pl-10"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="password" className="text-sm font-medium">
                        Password
                      </Label>
                      <Link
                        href="/forgot-password"
                        className="text-xs text-primary hover:underline focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1 rounded"
                      >
                        Forgot password?
                      </Link>
                    </div>
                    <div className="relative">
                      <Lock className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                      <Input
                        id="password"
                        type="password"
                        value={password}
                        onChange={handlePasswordChange}
                        className="pl-10"
                        placeholder="••••••••"
                        required
                      />
                    </div>
                  </div>

                  <Button type="submit" className="w-full group" size="lg" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Logging in...
                      </>
                    ) : (
                      <>
                        Sign in
                        <LogIn className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </>
                    )}
                  </Button>
                </form>

                <div className="text-center text-sm">
                  <span className="text-muted-foreground">Don't have an account? </span>
                  <Link href="/signup" className="text-primary font-medium hover:underline">
                    Sign up
                  </Link>
                </div>
              </div>
            </div>

            {/* Image Section */}
            <div className="hidden md:block relative bg-gradient-to-br from-primary/5 to-primary/30 dark:from-primary/10 dark:to-primary/20">
              <div className="absolute inset-0 bg-[url('/placeholder.svg?height=600&width=600')] bg-no-repeat bg-center bg-contain opacity-90 mix-blend-overlay"></div>
              <div className="relative h-full flex flex-col justify-center items-center p-6 text-center">
                <div className="max-w-md space-y-4">
                  <h3 className="text-2xl font-bold text-foreground">Achieve Your Nutrition Goals</h3>
                  <p className="text-muted-foreground">
                    Track your progress, get personalized recommendations, and transform your health journey with
                    NutriFocus.
                  </p>
                  <div className="pt-4">
                    <div className="inline-flex items-center justify-center space-x-2 bg-background/80 backdrop-blur-sm px-4 py-2 rounded-full text-sm">
                      <span className="flex h-2 w-2 rounded-full bg-primary"></span>
                      <span className="font-medium">Join over 10,000+ happy users</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}