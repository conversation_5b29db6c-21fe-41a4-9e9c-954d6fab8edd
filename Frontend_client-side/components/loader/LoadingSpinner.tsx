import { Loader2 } from "lucide-react";

interface LoadingSpinnerProps {
  text?: string;
  size?: number;
  fullScreen?: boolean;
}

export function LoadingSpinner({ 
  text = "Loading...", 
  size = 48, 
  fullScreen = true 
}: LoadingSpinnerProps) {
  if (fullScreen) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm z-50">
        <div className="text-center space-y-4">
          <Loader2 
            className="text-teal-500 animate-spin mx-auto" 
            style={{ width: size, height: size }} 
          />
          {text && (
            <p className="text-lg font-medium text-gray-700 dark:text-gray-300">{text}</p>
          )}
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex items-center justify-center w-full h-full min-h-[200px]">
      <div className="text-center space-y-4">
        <Loader2 
          className="text-teal-500 animate-spin mx-auto" 
          style={{ width: size, height: size }} 
        />
        {text && (
          <p className="text-lg font-medium text-gray-700 dark:text-gray-300">{text}</p>
        )}
      </div>
    </div>
  );
}
