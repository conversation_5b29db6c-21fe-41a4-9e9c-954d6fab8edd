
"use client"

import { useState } from "react"
import { ChevronDown } from "lucide-react"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { But<PERSON> } from "@/components/ui/button"
import { faqItems } from "@/data/HomeData/data"

export default function FAQ() {
  const [visibleItems, setVisibleItems] = useState(4)

  return (
    <section id="faq" className="bg-muted/50 py-16 md:py-24">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">Frequently Asked Questions</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Find answers to common questions about our platform
          </p>
        </div>

        <div className="max-w-3xl mx-auto">
          <Accordion type="single" collapsible className="space-y-4">
            {faqItems.slice(0, visibleItems).map((item) => (
              <AccordionItem key={item.value} value={item.value} className="bg-background border rounded-lg shadow-sm">
                <AccordionTrigger className="px-6 py-4 hover:no-underline hover:bg-muted/20 rounded-t-lg">
                  <h3 className="text-lg font-medium text-foreground text-left">{item.question}</h3>
                </AccordionTrigger>
                <AccordionContent className="px-6 py-4 text-muted-foreground">
                  <p className="text-base">{item.answer}</p>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>

          {visibleItems < faqItems.length && (
            <div className="flex justify-center mt-8">
              <Button
                variant="outline"
                onClick={() => setVisibleItems((prev) => prev + 4)}
                className="flex items-center gap-2"
              >
                Show More
                <ChevronDown className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
