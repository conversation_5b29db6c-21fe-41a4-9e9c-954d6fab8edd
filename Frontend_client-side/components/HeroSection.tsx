"use client"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { heroData } from "@/data/HomeData/data"
import { useEffect, useState } from "react"

export default function HeroSection() {
  const router = useRouter()
  const hero = heroData[0]
  const [currentTheme, setCurrentTheme] = useState<string>("light")

  useEffect(() => {
    // Function to get current theme
    const getTheme = () => {
      if (typeof window !== "undefined") {
        const storedTheme = localStorage.getItem("ui-theme")
        return storedTheme || "light"
      }
      return "light"
    }
    // Set initial theme
    setCurrentTheme(getTheme())

    // Create a MutationObserver to watch for theme class changes on <html>
    if (typeof window !== "undefined") {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (
            mutation.type === "attributes" &&
            mutation.attributeName === "class"
          ) {
            // HTML class changed, check if dark mode is active
            const isDarkMode = document.documentElement.classList.contains("dark")
            setCurrentTheme(isDarkMode ? "dark" : "light")
          }
        })
      })

      // Start observing the document with the configured parameters
      observer.observe(document.documentElement, { attributes: true })

      // Also listen for storage events (for changes in other tabs)
      const handleStorageChange = () => {
        setCurrentTheme(getTheme())
      }
      window.addEventListener("storage", handleStorageChange)

      // Cleanup
      return () => {
        observer.disconnect()
        window.removeEventListener("storage", handleStorageChange)
      }
    }
  }, [])

  // Determine which logo to use based on theme
  const logoSrc = currentTheme === "dark" ? hero.logo2 : hero.logo1

  return (
    <section id="home" className="container mx-auto py-10 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-center items-center mb-4 md:mb-8">
        <h2 className="text-3xl md:text-4xl font-bold text-foreground">
          Welcome !
        </h2>
        {/* <Image
          src={logoSrc || "/placeholder.svg"}
          alt="Company Logo"
          width={120}
          height={40}
          className="mt-4 md:mt-0"
        /> */}
      </div>

      <h1 className="flex flex-col text-3xl md:text-4xl lg:text-5xl font-bold text-center text-foreground max-w-3xl mx-auto mb-12">
        {hero.title}<span className="text-2xl animate-pulse font-light mt-3">{hero.sub_title}</span>
      </h1>

      <div className="grid md:grid-cols-2 gap-8 md:gap-12 items-center justify-center mx-auto max-w-7xl p-4 px-10">
        <div className="flex flex-col justify-center items-start gap-4 md:gap-6">
          <h2 className="text-2xl md:text-3xl font-bold text-foreground text-center md:text-left">
            {hero.hero_title}
          </h2>
          <p className="text-lg text-muted-foreground leading-relaxed text-center md:text-left">
            {hero.hero_desc}
          </p>
          <div className="pt-6 w-full flex justify-center md:justify-start">
            <Button
              size="lg"
              onClick={() => router.push("/signup")}
              className="w-full md:w-auto px-8 py-6 text-base font-medium hover:bg-primary/90 transition-all hover:translate-y-[-2px]"
            >
              Get Started <span className="ml-1">→</span>
            </Button>
          </div>
        </div>
        <div className="flex justify-end items-center">
          <Image
            src={hero.about || "/placeholder.svg"}
            alt="Hero illustration showing our product in action"
            width={430}
            height={300}
            className="max-w-full h-auto rounded-lg shadow-md"
            priority
          />
        </div>
      </div>
    </section>
  );
}
