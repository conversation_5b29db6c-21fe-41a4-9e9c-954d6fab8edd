// import Link from "next/link";
// import { Twitter, Instagram } from "lucide-react";

// export default function Footer() {
//   return (
//     <footer className="bg-[#3C3C3C] text-white py-12">
//       <div className="container mx-auto px-4">
//         <div className="grid grid-cols-1 md:grid-cols-3 gap-8">

//           <div className="flex flex-col items-center md:items-start">
//             <div className="flex items-center">
//               <span className="font-serif italic text-2xl">Nutri</span>
//               <span className="bg-[#8A8A8A] rounded px-2 py-1 text-2xl font-bold">
//                 Focus
//               </span>
//             </div>
//             <p className="mt-2 text-sm">Empower Your Wellness Goals.</p>
//           </div>
//           <div className="flex flex-col items-center md:items-start">
//             <h3 className="text-xl font-semibold mb-2">Contact Us</h3>
//             <p className="text-sm mb-2">
//               For inquiries, support, or feedback, reach out to us at:
//             </p>
//             <div className="flex flex-col">
//               <div className="mb-1">
//                 <span className="font-semibold">Email:</span>{" "}
//                 <a
//                   href="mailto:<EMAIL>"
//                   className="hover:underline"
//                 >
//                   <EMAIL>
//                 </a>
//               </div>
//               <div>
//                 <span className="font-semibold">Phone:</span> +****************
//               </div>
//             </div>
//           </div>

//           {/* Social Media */}
//           <div className="flex flex-col items-center md:items-start">
//             <h3 className="text-xl font-semibold mb-4">Visit us on</h3>
//             <div className="flex space-x-4">
//               <Link href="#" aria-label="Twitter" className="hover:opacity-80">
//                 <Twitter size={28} />
//               </Link>
//               <Link href="#" aria-label="Reddit" className="hover:opacity-80">
//                 <svg
//                   width="28"
//                   height="28"
//                   viewBox="0 0 24 24"
//                   fill="none"
//                   xmlns="http://www.w3.org/2000/svg"
//                 >
//                   <path
//                     d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
//                     stroke="white"
//                     strokeWidth="2"
//                     fill="none"
//                   />
//                   <path
//                     d="M17.5 12C17.5 10.6193 16.3807 9.5 15 9.5C14.5605 9.5 14.1502 9.62078 13.7998 9.83332C13.0364 9.30477 12.0644 9 11 9C9.93559 9 8.96361 9.30477 8.20018 9.83332C7.84979 9.62078 7.43945 9.5 7 9.5C5.61929 9.5 4.5 10.6193 4.5 12C4.5 12.8229 4.97079 13.5438 5.66084 13.9161C5.6203 14.1054 5.6 14.3007 5.6 14.5C5.6 16.433 7.99238 18 11 18C14.0076 18 16.4 16.433 16.4 14.5C16.4 14.3007 16.3797 14.1054 16.3392 13.9161C17.0292 13.5438 17.5 12.8229 17.5 12Z"
//                     stroke="white"
//                     strokeWidth="2"
//                     fill="none"
//                   />
//                   <circle cx="8.5" cy="13.5" r="1.5" fill="white" />
//                   <circle cx="13.5" cy="13.5" r="1.5" fill="white" />
//                   <path
//                     d="M9 16C9.5 16.5 10.3333 17 12 17C13.6667 17 14.5 16.5 15 16"
//                     stroke="white"
//                     strokeWidth="2"
//                     strokeLinecap="round"
//                   />
//                 </svg>
//               </Link>
//               <Link href="#" aria-label="LinkedIn" className="hover:opacity-80">
//                 <svg
//                   width="28"
//                   height="28"
//                   viewBox="0 0 24 24"
//                   fill="none"
//                   xmlns="http://www.w3.org/2000/svg"
//                 >
//                   <rect
//                     x="2"
//                     y="2"
//                     width="20"
//                     height="20"
//                     rx="2"
//                     stroke="white"
//                     strokeWidth="2"
//                     fill="none"
//                   />
//                   <path
//                     d="M8 10V16"
//                     stroke="white"
//                     strokeWidth="2"
//                     strokeLinecap="round"
//                   />
//                   <path
//                     d="M12 16V13"
//                     stroke="white"
//                     strokeWidth="2"
//                     strokeLinecap="round"
//                   />
//                   <path
//                     d="M16 16V11C16 9.89543 15.1046 9 14 9C12.8954 9 12 9.89543 12 11V13"
//                     stroke="white"
//                     strokeWidth="2"
//                     strokeLinecap="round"
//                   />
//                   <circle cx="8" cy="7" r="1" fill="white" />
//                 </svg>
//               </Link>
//               <Link
//                 href="#"
//                 aria-label="Instagram"
//                 className="hover:opacity-80"
//               >
//                 <Instagram size={28} />
//               </Link>
//             </div>
//           </div>
//         </div>
//         <div className="mt-12 text-center md:text-left">
//           <p className="text-sm">NutriFocus © 2024 All Rights Reserved.</p>
//         </div>
//       </div>
//     </footer>
//   );
// }


import type React from "react"
import Link from "next/link"
import { Twitter, Instagram, Linkedin, MessageCircle, Mail, Phone } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-primary text-primary-foreground py-12 mt-auto">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
          {/* Brand */}
          <div className="flex flex-col items-center md:items-start space-y-4 animate-fade-in">
            <div className="flex items-center">
              <span className="font-serif italic text-2xl">Nutri</span>
              <span className="bg-muted/30 rounded px-2 py-1 text-2xl font-bold">Focus</span>
            </div>
            <p className="text-sm text-primary-foreground/80">Empower Your Wellness Goals.</p>
            <p className="text-xs text-primary-foreground/70 max-w-xs">
              Helping you achieve optimal nutrition and wellness through personalized guidance and support.
            </p>
          </div>

          {/* Contact */}
          <div
            className="flex flex-col items-center md:items-start space-y-4 animate-fade-in"
            style={{ animationDelay: "100ms" }}
          >
            <h3 className="text-xl font-semibold">Contact Us</h3>
            <p className="text-sm text-primary-foreground/80 max-w-xs text-center md:text-left">
              For inquiries, support, or feedback, reach out to us:
            </p>
            <div className="flex flex-col space-y-2">
              <Link
                href="mailto:<EMAIL>"
                className="flex items-center gap-2 text-sm hover:text-primary-foreground/90 transition-colors group"
              >
                <Mail size={16} className="group-hover:scale-110 transition-transform" />
                <span><EMAIL></span>
              </Link>
              <Link
                href="tel:+18001234567"
                className="flex items-center gap-2 text-sm hover:text-primary-foreground/90 transition-colors group"
              >
                <Phone size={16} className="group-hover:scale-110 transition-transform" />
                <span>+****************</span>
              </Link>
              <Link
                href="/contact"
                className="flex items-center gap-2 text-sm hover:text-primary-foreground/90 transition-colors group"
              >
                <MessageCircle size={16} className="group-hover:scale-110 transition-transform" />
                <span>Send us a message</span>
              </Link>
            </div>
          </div>

          {/* Social Media */}
          <div
            className="flex flex-col items-center md:items-start space-y-4 animate-fade-in"
            style={{ animationDelay: "200ms" }}
          >
            <h3 className="text-xl font-semibold">Connect With Us</h3>
            <p className="text-sm text-primary-foreground/80 max-w-xs text-center md:text-left">
              Follow us on social media for tips, updates, and inspiration.
            </p>
            <div className="flex space-x-4">
              <SocialLink href="https://twitter.com/nutrifocus" label="Twitter">
                <Twitter size={20} />
              </SocialLink>
              <SocialLink href="https://linkedin.com/company/nutrifocus" label="LinkedIn">
                <Linkedin size={20} />
              </SocialLink>
              <SocialLink href="https://instagram.com/nutrifocus" label="Instagram">
                <Instagram size={20} />
              </SocialLink>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-6 border-t border-primary-foreground/20 flex flex-col md:flex-row justify-between items-center gap-4">
          <p className="text-sm text-primary-foreground/70">
            NutriFocus © {new Date().getFullYear()} All Rights Reserved.
          </p>
          <div className="flex space-x-6 text-sm text-primary-foreground/70">
            <Link href="/privacy" className="hover:text-primary-foreground transition-colors">
              Privacy Policy
            </Link>
            <Link href="/terms" className="hover:text-primary-foreground transition-colors">
              Terms of Service
            </Link>
            <Link href="/sitemap" className="hover:text-primary-foreground transition-colors">
              Sitemap
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}

// Helper component for social links
function SocialLink({
  children,
  href,
  label,
}: {
  children: React.ReactNode
  href: string
  label: string
}) {
  return (
    <Link
      href={href}
      aria-label={label}
      className="bg-primary-foreground/10 hover:bg-primary-foreground/20 p-3 rounded-full transition-all hover:scale-110 focus:ring-2 focus:ring-primary-foreground/50 focus:outline-none"
    >
      {children}
    </Link>
  )
}
