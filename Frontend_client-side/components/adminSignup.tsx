"use client";
import Image from "next/image";
import img from "@/app/assets/images/image_fx_ (1) 1.svg";
import logo from "@/app/assets/images/light-logo.svg";
import { FcGoogle } from "react-icons/fc";
import { FaArrowCircleRight } from "react-icons/fa";
import { IoMdAddCircle } from "react-icons/io";
import { useState,  ChangeEvent, FormEvent } from "react";
import { useRouter } from "next/navigation"; 
interface FormData {
  fullName: string;
  email: string;
  gender: string;
  location: string;
}
export default function Signup() {
  const [formData, setFormData] = useState<FormData>({
    fullName: "",
    email: "",
    gender: "",
    location: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setError(null); // Clear error when user makes changes
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    const apiUrl = process.env.NEXT_PUBLIC_API_URL + "/auth/signup";

    try {
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: { 
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        credentials: 'include',
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Network response was not ok");
      }

      const data = await response.json();
      console.log("Response from API:", data);
      
      if (data.success) {
        router.push("/adminLogin");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      setError(error instanceof Error ? error.message : "An error occurred during signup");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="w-full max-w-3xl  p-10 space-y-6 bg-field shadow-md rounded-lg">
        <div className="flex mx-10 justify-between bg-Iheader rounded-lg">
          <div className="flex justify-around items-center gap-10">
            <button
              className="ml-10  flex gap-4 items-center"
              onClick={() => router.push("/adminLogin")}
            >
              Login <FaArrowCircleRight />
            </button>
            <div className="flex">
              <button className="bg-Ibn  rounded-t-xl flex items-center gap-4">
                Sign Up
                <IoMdAddCircle />
              </button>
            </div>
          </div>
          <div>
            <Image
              src={logo}
              alt="Logo"
              className="p-2 md:order-3 order-2"
              width={120}
              height={200}
              onClick={() => router.push("/")}
            />
          </div>
        </div>
        <div className="flex justify-center">
          <button className="flex gap-5 items-center bg-slate-100 rounded-sm px-4 p-2 mx-2 text-center w-full max-w-xs text-slate-400">
            <FcGoogle /> Continue with Google
          </button>
        </div>
        <div className="flex items-center">
          <hr className="flex-grow border-t border-white border-2" />
          <span className="mx-2 text-gray-600">or</span>
          <hr className="flex-grow border-t border-white border-2" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-stretch px-10">
          <form className="space-y-4" onSubmit={handleSubmit}>
            {error && (
              <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {error}
              </div>
            )}
            <input
              type="text"
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              className="w-full p-2 mt-1 border bg-Input rounded-md focus:ring-2 focus:ring-blue-500"
              placeholder="Full Name"
              required
              disabled={isLoading}
            />
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="w-full p-2 mt-1 border rounded-md bg-Input focus:ring-2 focus:ring-blue-500"
              placeholder="Email"
              required
              disabled={isLoading}
            />
            <button 
              className="w-full px-4 py-2 text-white bg-[#5779D8] rounded-md hover:bg-blue-700 disabled:bg-blue-300"
              disabled={isLoading}
            >
              {isLoading ? "Signing up..." : "Get verification code via email"}
            </button>
          </form>
          <div className="hidden elative md:flex justify-center items-center border-l-4 border-white pl-6">
            <Image src={img} alt="Nutrient" width={300} height={300} />
          </div>
        </div>
      </div>
    </div>
  );
}
