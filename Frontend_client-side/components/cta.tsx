// "use client"
// import { useRouter } from "next/navigation"
// import { Button } from "@/components/ui/button"

// export default function CTA() {
//   const router = useRouter()

//   return (
//     <section className="py-16 md:py-24 px-4 md:px-6">
//       <div className="container mx-auto text-center">
//         <div className="max-w-2xl mx-auto space-y-6">
//           <h2 className="text-3xl md:text-4xl font-bold text-foreground">Ready to Get Started?</h2>
//           <p className="text-lg text-muted-foreground">
//             Join thousands of satisfied customers who have transformed their experience with our platform.
//           </p>
//           <div className="pt-4 flex flex-col sm:flex-row justify-center gap-4">
//             <Button size="lg" onClick={() => router.push("/Signup")}>
//               Sign Up Now
//             </Button>
//             <Button size="lg" variant="outline" onClick={() => router.push("/Contact")}>
//               Contact Sales
//             </Button>
//           </div>
//         </div>
//       </div>
//     </section>
//   )
// }


"use client"
import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { ArrowRight, Sparkles, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"

export default function CTA() {
  const router = useRouter()
  const [isVisible, setIsVisible] = useState(false)
  const sectionRef = useRef<HTMLElement>(null)
  const [hoverButton, setHoverButton] = useState<string | null>(null)

  // Intersection Observer to trigger animations when section is in view
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.unobserve(entry.target)
        }
      },
      { threshold: 0.2 },
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current)
      }
    }
  }, [])

  return (
    <section ref={sectionRef} className="relative py-20 md:py-28 px-4 md:px-6 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl" />
      </div>

      {/* Animated Dots */}
      <div className="absolute inset-0 -z-10">
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className={cn("absolute w-2 h-2 rounded-full bg-primary/30", isVisible ? "animate-ping" : "opacity-0")}
            style={{
              top: `${20 + i * 30}%`,
              left: `${10 + i * 30}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${3 + i}s`,
            }}
          />
        ))}
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className={cn("absolute w-2 h-2 rounded-full bg-primary/30", isVisible ? "animate-ping" : "opacity-0")}
            style={{
              top: `${30 + i * 20}%`,
              right: `${15 + i * 25}%`,
              animationDelay: `${i * 0.7}s`,
              animationDuration: `${4 + i}s`,
            }}
          />
        ))}
      </div>

      <div className="container mx-auto relative">
        <div
          className={cn(
            "max-w-3xl mx-auto space-y-8 text-center transition-all duration-700 transform",
            isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0",
          )}
        >
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full text-sm font-medium text-primary mb-4">
            <Sparkles className="h-4 w-4 mr-2" />
            <span>Start your journey today</span>
          </div>

          <h2
            className={cn(
              "text-3xl md:text-4xl lg:text-5xl font-bold text-foreground transition-all duration-700 delay-100",
              isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0",
            )}
          >
            Ready to Transform Your <span className="text-primary">Wellness</span> Journey?
          </h2>

          <p
            className={cn(
              "text-lg text-muted-foreground max-w-2xl mx-auto transition-all duration-700 delay-200",
              isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0",
            )}
          >
            Join thousands of satisfied customers who have revolutionized their health and nutrition with our platform.
          </p>

          <div
            className={cn(
              "pt-6 flex flex-col sm:flex-row justify-center gap-4 transition-all duration-700 delay-300",
              isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0",
            )}
          >
            {/* Primary CTA Button with Animation */}
            <Button
              size="lg"
              className={cn(
                "group relative overflow-hidden transition-all duration-300 shadow-lg shadow-primary/20 hover:shadow-primary/40",
                "bg-gradient-to-r from-primary to-primary/80 hover:to-primary",
                "border-none text-base font-medium",
                hoverButton === "signup" && "scale-105",
              )}
              onMouseEnter={() => setHoverButton("signup")}
              onMouseLeave={() => setHoverButton(null)}
              onClick={() => router.push("/signup")}
            >
              <span className="relative z-10 flex items-center">
                Sign Up Now
                <ArrowRight
                  className={cn(
                    "ml-2 h-4 w-4 transition-transform duration-300",
                    hoverButton === "signup" ? "translate-x-1" : "",
                  )}
                />
              </span>
              <span className="absolute inset-0 -z-10 bg-gradient-to-r from-primary/80 to-primary/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </Button>

            {/* Secondary CTA Button with Animation */}
            <Button
              size="lg"
              variant="outline"
              className={cn(
                "group relative border-2 text-base font-medium",
                "hover:bg-primary/5 hover:border-primary/50 transition-all duration-300",
                hoverButton === "contact" && "scale-105",
              )}
              onMouseEnter={() => setHoverButton("contact")}
              onMouseLeave={() => setHoverButton(null)}
              onClick={() => router.push("/Contact")}
            >
              <span className="flex items-center">
                Contact Sales
                <ChevronRight
                  className={cn(
                    "ml-1 h-4 w-4 transition-all duration-300",
                    hoverButton === "contact" ? "translate-x-1 opacity-100" : "opacity-70",
                  )}
                />
              </span>
            </Button>
          </div>

          {/* Trust Indicators */}
          <div
            className={cn(
              "flex flex-wrap justify-center gap-x-8 gap-y-4 pt-8 text-sm text-muted-foreground transition-all duration-700 delay-400",
              isVisible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0",
            )}
          >
            <div className="flex items-center">
              <svg className="h-5 w-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <span>30-day free trial</span>
            </div>
            <div className="flex items-center">
              <svg className="h-5 w-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <span>No credit card required</span>
            </div>
            <div className="flex items-center">
              <svg className="h-5 w-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <span>Cancel anytime</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
