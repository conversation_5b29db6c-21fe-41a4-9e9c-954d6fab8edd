"use client";
import React from "react";
import { GoogleLogin, GoogleOAuthProvider } from "@react-oauth/google";
import useGoogleAuthentication from "@/services/useGoogleAuthentication";
import "dotenv/config";

function GoogleButton() {
  const { handleSuccess } = useGoogleAuthentication();
  const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID ?? "";

  return (
    <div className="flex justify-center gap-4">
      <GoogleOAuthProvider clientId={clientId}>
        {/* Sign Up Button */}
        <GoogleLogin
          onSuccess={(response) => handleSuccess(response, true)} // Pass true to indicate sign-up
          text="signup_with" // Use a custom text for the button
          shape="rectangular"
          size="large"
        />
        
        {/* Login Button */}
        <GoogleLogin
          onSuccess={(response) => handleSuccess(response, false)} // Pass false to indicate login
          text="signin_with" // Use a custom text for the button
          shape="rectangular"
          size="large"
        />
      </GoogleOAuthProvider>
    </div>
  );
}

export default GoogleButton;
