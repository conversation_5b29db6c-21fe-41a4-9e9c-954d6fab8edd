"use client";

import { useEffect, useState, type FormEvent } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import {
  ArrowLeft,
  Mail,
  AlertCircle,
  Loader2,
  CheckCircle2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardDescription, CardTitle } from "@/components/ui/card";
import { heroData } from "@/data/HomeData/data";
import { authService } from "@/services/auth.service";


export default function ForgotPassword() {
    const [email, setEmail] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [formError, setFormError] = useState<string | null>(null);
    const [isSubmitted, setIsSubmitted] = useState(false);
    const router = useRouter();
    const hero = heroData[0];

  const [currentTheme, setCurrentTheme] = useState<string>("light");

  useEffect(() => {
    // Function to get current theme
    const getTheme = () => {
      if (typeof window !== "undefined") {
        const storedTheme = localStorage.getItem("ui-theme");
        return storedTheme || "light";
      }
      return "light";
    };
    // Set initial theme
    setCurrentTheme(getTheme());

    // Create a MutationObserver to watch for theme class changes on <html>
    if (typeof window !== "undefined") {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (
            mutation.type === "attributes" &&
            mutation.attributeName === "class"
          ) {
            // HTML class changed, check if dark mode is active
            const isDarkMode =
              document.documentElement.classList.contains("dark");
            setCurrentTheme(isDarkMode ? "dark" : "light");
          }
        });
      });

      // Start observing the document with the configured parameters
      observer.observe(document.documentElement, { attributes: true });

      // Also listen for storage events (for changes in other tabs)
      const handleStorageChange = () => {
        setCurrentTheme(getTheme());
      };
      window.addEventListener("storage", handleStorageChange);

      // Cleanup
      return () => {
        observer.disconnect();
        window.removeEventListener("storage", handleStorageChange);
      };
    }
  }, []);

  // Determine which logo to use based on theme
  const logoSrc = currentTheme === "dark" ? hero.logo2 : hero.logo1;

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setFormError(null);

    try {
      
      await authService.forgotPassword(email);
      await new Promise((resolve) => setTimeout(resolve, 1500));

      setIsSubmitted(true);
    } catch (error: any) {
      console.error("Password reset error:", error);
      setFormError(
        error.response?.data?.message ||
          "Failed to send reset email. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-b from-background to-muted/30">
      <div className="w-full max-w-md animate-fade-in">
        <Card className="w-full shadow-lg overflow-hidden border-muted/20">
          <div className="p-6 md:p-8 space-y-6">
            <div className="flex justify-between items-center">
              <Link href="/" className="flex items-center space-x-2">
                <Image
                  src={logoSrc}
                  alt="NutriFocus Logo"
                  width={120}
                  height={40}
                  className="h-6 w-auto"
                />
              </Link>
              <Link
                href="/login"
                className="flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                <ArrowLeft className="mr-1 h-4 w-4" />
                Back to login
              </Link>
            </div>

            {!isSubmitted ? (
              <div className="space-y-6 animate-fade-in">
                <div className="space-y-2 text-center">
                  <CardTitle className="text-2xl font-bold">
                    Forgot your password?
                  </CardTitle>
                  <CardDescription>
                    Enter your email address and we'll send you a link to reset
                    your password.
                  </CardDescription>
                </div>

                {formError && (
                  <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md flex items-start">
                    <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                    <p>{formError}</p>
                  </div>
                )}

                <form className="space-y-4" onSubmit={handleSubmit}>
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium">
                      Email address
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="pl-10"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    size="lg"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Sending reset link...
                      </>
                    ) : (
                      "Send reset link"
                    )}
                  </Button>
                </form>
              </div>
            ) : (
              <div className="space-y-6 animate-fade-in text-center py-8">
                <div className="flex justify-center">
                  <div className="rounded-full bg-primary/10 p-3">
                    <CheckCircle2 className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <div className="space-y-2">
                  <CardTitle className="text-2xl font-bold">
                    Check your email
                  </CardTitle>
                  <CardDescription className="max-w-sm mx-auto">
                    We've sent a password reset link to{" "}
                    <span className="font-medium text-foreground">{email}</span>
                    . Please check your inbox and follow the instructions.
                  </CardDescription>
                </div>
                <div className="pt-4">
                  <p className="text-sm text-muted-foreground">
                    Didn't receive an email?{" "}
                    <Button
                      variant="link"
                      className="p-0 h-auto"
                      onClick={() => setIsSubmitted(false)}
                    >
                      Try again
                    </Button>
                  </p>
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
}
