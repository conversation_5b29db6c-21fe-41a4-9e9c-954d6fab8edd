// import { features } from "@/data/HomeData/heroData";
// import Image from "next/image";
// import brand from "@/app/assets/images/Accessibility Tools.svg";
// const FeatureCard = ({
//   img,
//   title,
//   children,
// }: {
//   img: string;
//   title: string;
//   children: string;
// }) => (
//   <div className="group relative backdrop-blur-sm rounded-2xl  hover:shadow-xl transition-all duration-300 ease-in-out flex flex-col justify-end">
//     <div className="absolute inset-0 rounded-2xl" />

//     <div className="flex items-start space-x-4">
//       <Image
//         src={img}
//         alt={title}
//         width={100}
//         height={100}
//         className="rounded-lg"
//       />
//       <h3 className="text-xl font-bold font-mont">{title}</h3>
//     </div>
//     <div>
//       <p className="text-[#4A5568] font-mont  leading-relaxed">{children}</p>
//     </div>
//   </div>
// );

// const FeatureSection = () => {
//   return (
//     <section className="py-16 px-4 sm:px-6 lg:px-8">
//       <div className="max-w-6xl bg-[#C2C4D1]  mx-auto bg-">
//         <div className="text-center ">
//           <h2 className="text-4xl font-bold text-[#2D3748] p-6 mb-6 ">
//             Powerful Features
//           </h2>
//         </div>
//         <div className="grid grid-cols-1 md:grid-cols-3 p-6">
//           {features.map((feature, index) => (
//             <div
//               key={index}
//               className={`
//                ${
//                  index === 0 || index === 2
//                    ? "items-end text-star justify-end self-end"
//                    : ""
//                }
//                 ${index === 0 ? "md:ml-10" : ""}

//                 ${index === 5 ? "md:col-start-2 text-center"  : ""}
//                 ${
//                   index === 1
//                     ? "md:col-span-2 md:col-start-2 md:ml-64 md:mr-10 mx-auto flex flex-col md:items-end md:text-end"
//                     : ""
//                 }
//                      ${index === 4 ? "flex items-end md:text-end " : ""}
//               `}
//             >
//               {index === 3 ? (
//                 <div className="md:flex justify-center hidden">
//                   <Image
//                     src={brand}
//                     alt="Brand Logo"
//                     width={200}
//                     height={100}
//                     className="rounded-xl mb-5"
//                   />
//                 </div>
//               ) : (
//                 <FeatureCard img={feature.img} title={feature.title}>
//                   {feature.desc}
//                 </FeatureCard>
//               )}
//             </div>
//           ))}
//         </div>
//       </div>
//     </section>
//   );
// };

// export default FeatureSection;

import type React from "react";
import Image from "next/image";
import { features } from "@/data/HomeData/data";
import { Card, CardContent } from "@/components/ui/card";

interface FeatureCardProps {
  img: string;
  title: string;
  children: React.ReactNode;
  className?: string;
}

function FeatureCard({
  img,
  title,
  children,
  className = "",
}: FeatureCardProps) {
  return (
    <Card
      className={`overflow-hidden transition-all duration-300 hover:shadow-lg ${className}`}
    >
      <CardContent className="p-6">
        <div className="flex items-start gap-4 mb-4">
          <Image
            src={img || "/placeholder.svg"}
            alt={`${title} feature icon`}
            width={60}
            height={60}
            className="rounded-md"
          />
          <h3 className="text-xl font-semibold text-foreground">{title}</h3>
        </div>
        <p className="text-muted-foreground leading-relaxed">{children}</p>
      </CardContent>
    </Card>
  );
}

export default function FeatureSection() {
  return (
    <section id="features" className="py-16 md:py-24 px-4 md:px-6">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Powerful Features
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover the tools that make our platform stand out from the
            competition
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              img={feature.img}
              title={feature.title}
              className={index === 3 ? "md:col-span-1 lg:col-span-1" : ""}
            >
              {feature.desc}
            </FeatureCard>
          ))}
        </div>
      </div>
    </section>
  );
}
