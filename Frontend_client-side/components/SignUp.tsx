"use client"

import { useEffect, useState, type ChangeEvent, type FormEvent } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { toast } from "react-hot-toast"
import Link from "next/link"
import { authService } from "@/services/auth.service"
import { ArrowRight, UserPlus, Lock, Mail, User, MapPin, AlertCircle, Loader2, UserCircle } from 'lucide-react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardDescription, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import GoogleSignUpButton from "./google/GoogleSignUpButton"
import { heroData } from "@/data/HomeData/data"

interface FormData {
  name: string
  email: string
  password: string
  // gender: string
  // location: string
}

export default function Signup() {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    password: "",
    // gender: "",
    // location: "",
  })
  const [isLoading, setIsLoading] = useState(false)
  const [formError, setFormError] = useState<string | null>(null)
  const [passwordStrength, setPasswordStrength] = useState(0)
  const router = useRouter()
    const hero = heroData[0]
    const [currentTheme, setCurrentTheme] = useState<string>("light")
  
      useEffect(() => {
        // Function to get current theme
        const getTheme = () => {
          if (typeof window !== "undefined") {
            const storedTheme = localStorage.getItem("ui-theme")
            return storedTheme || "light"
          }
          return "light"
        }
        // Set initial theme
        setCurrentTheme(getTheme())
        
        // Create a MutationObserver to watch for theme class changes on <html>
        if (typeof window !== "undefined") {
          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (
                mutation.type === "attributes" && 
                mutation.attributeName === "class"
              ) {
                // HTML class changed, check if dark mode is active
                const isDarkMode = document.documentElement.classList.contains("dark")
                setCurrentTheme(isDarkMode ? "dark" : "light")
              }
            })
          })
          
          // Start observing the document with the configured parameters
          observer.observe(document.documentElement, { attributes: true })
          
          // Also listen for storage events (for changes in other tabs)
          const handleStorageChange = () => {
            setCurrentTheme(getTheme())
          }
          window.addEventListener("storage", handleStorageChange)
          
          // Cleanup
          return () => {
            observer.disconnect()
            window.removeEventListener("storage", handleStorageChange)
          }
        }
      }, [])
      
      const logoSrc = currentTheme === "dark" ? hero.logo2 : hero.logo1

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData({ ...formData, [name]: value })
    
    if (name === "password") {
      // Simple password strength calculation
      let strength = 0
      if (value.length >= 8) strength += 1
      if (/[A-Z]/.test(value)) strength += 1
      if (/[0-9]/.test(value)) strength += 1
      if (/[^A-Za-z0-9]/.test(value)) strength += 1
      setPasswordStrength(strength)
    }
    
    if (formError) setFormError(null)
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData({ ...formData, [name]: value })
    if (formError) setFormError(null)
  }

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsLoading(true)
    setFormError(null)

    try {
      const response = await authService.register({
        name: formData.name,
        email: formData.email,
        password: formData.password,
      })

      toast.success(response.message || "Registration successful! Please check your email for verification.")
      router.push("/login")
    } catch (error: any) {
      console.error("Registration error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        config: error.config
      })
      
      const errorMessage = error.response?.data?.message 
        || error.message 
        || "Registration failed. Please check your information and try again."
      
      setFormError(errorMessage)
      
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-b from-background to-muted/30">
      <div className="w-full max-w-5xl animate-fade-in">
        <Card className="w-full shadow-lg overflow-hidden border-muted/20">
          <div className="grid md:grid-cols-2 gap-0">
            {/* Form Section */}
            <div className="p-6 md:p-8 space-y-6">
              <div className="flex justify-between items-center">
                <Link href="/" className="flex items-center space-x-2">
                  <Image
                    src={logoSrc || "/placeholder.svg"}
                    alt="NutriFocus Logo"
                    width={120}
                    height={40}
                    className="h-7 w-auto"
                  />
                </Link>

                {/* Auth Navigation */}
                <div className="flex items-center space-x-4">
                  <Link href="/login" className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                    Login
                  </Link>
                  <div className="text-sm font-medium text-primary">Sign Up</div>
                </div>
              </div>

              <div className="space-y-6 animate-fade-in">
                <div className="space-y-2 text-center">
                  <CardTitle className="text-2xl font-bold">Create your account</CardTitle>
                  <CardDescription>Enter your information to get started with NutriFocus</CardDescription>
                </div>

                <div className="flex justify-center">
                  <GoogleSignUpButton />
                </div>

                <div className="relative flex items-center">
                  <div className="flex-grow border-t border-muted"></div>
                  <span className="mx-4 text-xs text-muted-foreground">or continue with email</span>
                  <div className="flex-grow border-t border-muted"></div>
                </div>

                {formError && (
                  <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md flex items-start">
                    <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                    <p>{formError}</p>
                  </div>
                )}

                <form className="space-y-4" onSubmit={handleSubmit}>
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-medium">
                      Full Name
                    </Label>
                    <div className="relative">
                      <User className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        value={formData.name}
                        onChange={handleChange}
                        className="pl-10"
                        placeholder="John Doe"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium">
                      Email
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        className="pl-10"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-sm font-medium">
                      Password
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                      <Input
                        id="password"
                        name="password"
                        type="password"
                        value={formData.password}
                        onChange={handleChange}
                        className="pl-10"
                        placeholder="••••••••"
                        required
                        minLength={6}
                      />
                    </div>

                    {/* Password strength indicator */}
                    {formData.password && (
                      <div className="mt-2">
                        <div className="flex gap-1 h-1">
                          {[...Array(4)].map((_, i) => (
                            <div
                              key={i}
                              className={`h-full flex-1 rounded-full transition-colors ${
                                i < passwordStrength
                                  ? passwordStrength === 1
                                    ? "bg-destructive/80"
                                    : passwordStrength === 2
                                      ? "bg-orange-500"
                                      : passwordStrength === 3
                                        ? "bg-yellow-500"
                                        : "bg-green-500"
                                  : "bg-muted"
                              }`}
                            />
                          ))}
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {passwordStrength === 0 && "Use 8+ characters with letters, numbers & symbols"}
                          {passwordStrength === 1 && "Weak - Add uppercase letters, numbers or symbols"}
                          {passwordStrength === 2 && "Fair - Add more variety"}
                          {passwordStrength === 3 && "Good - Almost there"}
                          {passwordStrength === 4 && "Strong password"}
                        </p>
                      </div>
                    )}
                  </div>

                  <Button type="submit" className="w-full group" size="lg" disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating account...
                      </>
                    ) : (
                      <>
                        Create account
                        <UserPlus className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </>
                    )}
                  </Button>
                </form>

                <div className="text-center text-sm">
                  <span className="text-muted-foreground">Already have an account? </span>
                  <Link href="/login" className="text-primary font-medium hover:underline">
                    Sign in
                  </Link>
                </div>
              </div>
            </div>

            {/* Image Section */}
            <div className="hidden md:block relative bg-gradient-to-br from-primary/5 to-primary/30 dark:from-primary/10 dark:to-primary/20">
              <div className="absolute inset-0 bg-[url('/placeholder.svg?height=600&width=600')] bg-no-repeat bg-center bg-contain opacity-90 mix-blend-overlay"></div>
              <div className="relative h-full flex flex-col justify-center items-center p-6 text-center">
                <div className="max-w-md space-y-4">
                  <h3 className="text-2xl font-bold text-foreground">Start Your Wellness Journey</h3>
                  <p className="text-muted-foreground">
                    Create your personalized nutrition plan, track your progress, and achieve your health goals with
                    NutriFocus.
                  </p>
                  <div className="pt-4 space-y-4">
                    <div className="inline-flex items-center justify-center space-x-2 bg-background/80 backdrop-blur-sm px-4 py-2 rounded-full text-sm">
                      <span className="flex h-2 w-2 rounded-full bg-primary"></span>
                      <span className="font-medium">Free 30-day trial</span>
                    </div>

                    <div className="flex flex-col space-y-2">
                      {["Personalized nutrition plans", "Progress tracking", "Expert guidance"].map(
                        (feature, index) => (
                          <div key={index} className="flex items-center space-x-2 text-sm">
                            <svg
                              className="h-4 w-4 text-primary"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                              strokeWidth="2"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                            </svg>
                            <span>{feature}</span>
                          </div>
                        ),
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}

