"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { ChevronDown, ArrowRight, Search } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { blogService, BlogPost, BlogQueryParams } from "@/services/blog.service";
import { format } from "date-fns";
import Image from "next/image";
import { any } from "zod";

export default function Blog() {
  const [blogs, setBlogs] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 8,
    total: 0,
    totalPages: 0,
  });
  const router = useRouter();

  const fetchBlogs = async (params: BlogQueryParams = {}) => {
    try {
      setLoading(true);
      const response = await blogService.getAllBlogs({
        page: pagination.page,
        limit: pagination.limit,
        search: searchQuery || undefined,
        category: selectedCategory === "all" ? undefined : selectedCategory,
        ...params,
      });
      
      setBlogs(response.data);
      setPagination({
        page: response.meta.page,
        limit: response.meta.limit,
        total: response.meta.total,
        totalPages: response.meta.totalPages,
      });
      setError(null);
    } catch (err) {
      console.error("Error fetching blogs:", err);
      setError("Failed to load nutrition guides. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const categoriesList = await blogService.getCategories();
      setCategories(categoriesList);
    } catch (err) {
      console.error("Error fetching categories:", err);
    }
  };

  useEffect(() => {
    fetchCategories();
    fetchBlogs();
  }, []);

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchBlogs({ page: 1 });
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchBlogs({ 
      page: 1, 
      category: category === 'all' ? undefined : category 
    });
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMMM d, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold text-slate-600">
            Ethiopian Nutrition Guides
          </h1>
          <p className=" mt-2 text-slate-500">
            Discover the nutritional benefits of traditional Ethiopian cuisine
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search guides..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
            />
          </div>
          <Select value={selectedCategory} onValueChange={handleCategoryChange}>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button onClick={handleSearch}>Search</Button>
        </div>
      </div>

      {loading && blogs.length === 0 ? (
        <div className="text-center py-12">
          <p className="">Loading nutrition guides...</p>
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-500">{error}</p>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => fetchBlogs()}
          >
            Try Again
          </Button>
        </div>
      ) : blogs.length > 0 ? (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {blogs.map((blog) => (
              <Card
                key={blog.blogId}
                className="overflow-hidden transition-all duration-200 hover:shadow-lg"
              >
                <div className="aspect-video relative overflow-hidden bg-slate-100">
                  <Image
                    src={blog.featuredImage || "/placeholder.svg"}
                    alt={blog.title}
                    width={400}
                    height={225}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                  />
                  <Badge className="absolute top-3 right-3" variant="secondary">
                    {blog.category}
                  </Badge>
                </div>
                <CardContent className="p-5">
                  <p className="text-sm font-medium ">
                    {formatDate(blog.createdAt)}
                  </p>
                  <h2 className="text-xl font-semibold mt-2  line-clamp-1">
                    {blog.title}
                  </h2>
                  <p className=" mt-2 line-clamp-2 text-sm">
                    {blog.content.substring(0, 150)}...
                  </p>
                </CardContent>
                <CardFooter className="px-5 pb-5 pt-0">
                 <Button
  variant="outline"
  className="w-full justify-between"
  onClick={() => {

      // Store the intended destination in sessionStorage or pass as query param
      sessionStorage.setItem('redirectAfterLogin', `/userDashboard/learn-dite/${blog.blogId}`);
      router.push('/login');
  }}
>
                    Read More <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
          
          {/* Pagination Controls */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center items-center gap-2 mt-10">
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  setPagination(prev => ({ ...prev, page: 1 }));
                  fetchBlogs({ page: 1 });
                }}
                disabled={pagination.page === 1 || loading}
              >
                First
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  const prevPage = pagination.page - 1;
                  setPagination(prev => ({ ...prev, page: prevPage }));
                  fetchBlogs({ page: prevPage });
                }}
                disabled={pagination.page === 1 || loading}
              >
                Previous
              </Button>
              
              <span className="mx-2 text-sm text-slate-500">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  const nextPage = pagination.page + 1;
                  setPagination(prev => ({ ...prev, page: nextPage }));
                  fetchBlogs({ page: nextPage });
                }}
                disabled={pagination.page === pagination.totalPages || loading}
              >
                Next
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  setPagination(prev => ({ ...prev, page: pagination.totalPages }));
                  fetchBlogs({ page: pagination.totalPages });
                }}
                disabled={pagination.page === pagination.totalPages || loading}
              >
                Last
              </Button>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <h3 className="text-xl font-medium">
            No nutrition guides found
          </h3>
          <p className=" mt-2">
            Try adjusting your search or category filter
          </p>
        </div>
      )}
    </div>
  );
}
