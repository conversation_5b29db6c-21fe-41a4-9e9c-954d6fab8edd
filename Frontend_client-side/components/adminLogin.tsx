"use client";

import Image from "next/image";
import { FaArrowCircleRight } from "react-icons/fa";
import img from "@/app/assets/images/image_fx_ (1) 1.svg";
import { useState, ChangeEvent, FormEvent, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { api } from "@/services/api";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { heroData } from "@/data/HomeData/data"
import Link from "next/link";
import { Eye, EyeOff } from "lucide-react";
import GoogleSignUpButton from "./google/GoogleSignUpButton";

export default function Login() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const hero = heroData[0]
  const [currentTheme, setCurrentTheme] = useState<string>("light")
  const logoSrc = currentTheme === "dark" ? hero.logo2 : hero.logo1
  
  useEffect(() => {
    // Function to get current theme
    const getTheme = () => {
      if (typeof window !== "undefined") {
        const storedTheme = localStorage.getItem("ui-theme")
        return storedTheme || "light"
      }
      return "light"
    }
    // Set initial theme
    setCurrentTheme(getTheme())
    
    // Create a MutationObserver to watch for theme class changes on <html>
    if (typeof window !== "undefined") {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (
            mutation.type === "attributes" && 
            mutation.attributeName === "class"
          ) {
            // HTML class changed, check if dark mode is active
            const isDarkMode = document.documentElement.classList.contains("dark")
            setCurrentTheme(isDarkMode ? "dark" : "light")
          }
        })
      })
      
      // Start observing the document with the configured parameters
      observer.observe(document.documentElement, { attributes: true })
      
      // Also listen for storage events (for changes in other tabs)
      const handleStorageChange = () => {
        setCurrentTheme(getTheme())
      }
      window.addEventListener("storage", handleStorageChange)
      
      // Cleanup
      return () => {
        observer.disconnect()
        window.removeEventListener("storage", handleStorageChange)
      }
    }
  }, [])
  const handleEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (error) setError(null);
  };

  const handlePasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    if (error) setError(null);
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await api.post("/auth/AdminLogin", { email, password });
      const user = await api.get("/auth/check-user");
      const userRole = user.data.roles[0];

      localStorage.setItem("userRole", userRole || "ADMIN");
      toast.success("Login successful");
      
      // Redirect based on role
      if (userRole === "SUPERADMIN") {
        router.push("/superAdmin");
      } else {
        router.push("/admin");
      }
    } catch (error: any) {
      console.error("Login error:", error);
      if (error.response) {
        setError(error.response.data?.message || "Invalid email or password");
        toast.error(error.response.data?.message || "Login failed");
      } else if (error.request) {
        setError("No response from server. Please try again later.");
        toast.error("Network error. Please check your connection.");
      } else {
        setError("An unexpected error occurred. Please try again.");
        toast.error("An unexpected error occurred");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background dark:bg-background">
      <Card className="w-full max-w-3xl p-10 border-border dark:border-border shadow-md">
        <CardContent>
          <div className="flex mx-10 justify-between bg-muted dark:bg-muted/50 rounded-lg my-5">
            <div className="flex justify-center items-center gap-10 w-100">
              <Button
                variant="ghost"
                className="ml-10 flex gap-4 items-center text-foreground dark:text-foreground hover:bg-muted/50 dark:hover:bg-muted/70"
              >
                Login <FaArrowCircleRight />
              </Button>
            </div>
            <div>
             <Link href="/" className="flex items-center space-x-2">
                  <Image
                    src={logoSrc || "/placeholder.svg"}
                    alt="NutriFocus Logo"
                    width={100}
                    height={40}
                    className="h-7 w-auto"
                  />
                </Link>
            </div>
          </div>


                <div className="flex justify-center mb-5">
                  <GoogleSignUpButton />
                </div>

                <div className="relative flex items-center mb-2">
                  <div className="flex-grow border-t border-muted"></div>
                  <span className="mx-4 text-xs text-muted-foreground">or continue with email</span>
                  <div className="flex-grow border-t border-muted"></div>
                </div>
          <div className="flex items-center">
            <hr className="flex-grow border-t border-muted-foreground/20 dark:border-muted-foreground/20" />
            <span className="mx-2 text-muted-foreground dark:text-muted-foreground">or</span>
            <hr className="flex-grow border-t border-muted-foreground/20 dark:border-muted-foreground/20" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center mt-6">
            <div className="flex flex-col justify-center">
              {error && (
                <div className="bg-destructive/10 text-destructive dark:text-destructive p-3 rounded-md text-sm mb-4">
                  {error}
                </div>
              )}
              <form className="space-y-4" onSubmit={handleSubmit}>
                <div>
                  <Input
                    type="email"
                    value={email}
                    onChange={handleEmailChange}
                    className="w-full border-border dark:border-border text-foreground dark:text-foreground placeholder-muted-foreground dark:placeholder-muted-foreground"
                    placeholder="Email"
                    required
                    disabled={isLoading}
                  />
                </div>
                <div>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={handlePasswordChange}
                      className="w-full border-border dark:border-border text-foreground dark:text-foreground placeholder-muted-foreground dark:placeholder-muted-foreground pr-10"
                      placeholder="Password"
                      required
                      disabled={isLoading}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                      <span className="sr-only">
                        {showPassword ? "Hide password" : "Show password"}
                      </span>
                    </Button>
                  </div>
                </div>
                <Button
                  type="submit"
                  className="w-full bg-blue-600 dark:bg-blue-700 text-white hover:bg-blue-700 dark:hover:bg-blue-800 disabled:bg-blue-300 dark:disabled:bg-blue-500 disabled:cursor-not-allowed"
                  disabled={isLoading}
                >
                  {isLoading ? "Logging in..." : "Login"}
                </Button>
              </form>
              <div className="flex justify-between py-2">
                <a
                  href="/forgot-password"
                  className="text-blue-500 dark:text-blue-400 hover:underline text-xs"
                >
                  Password forgotten
                </a>
                <a
                  href="/adminSignup"
                  className="text-blue-500 dark:text-blue-400 hover:underline text-xs"
                >
                  I don't have an account
                </a>
              </div>
            </div>
            <div className="hidden relative md:flex justify-center items-center border-l-4 border-muted-foreground/20 dark:border-muted-foreground/20 pl-6">
              <Image className="object-contain h-full" src={img} alt="Nutrient" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}