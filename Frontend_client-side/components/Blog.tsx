import React from "react";
import Image from 'next/image';

interface Product {
  id: number;
  title: string;
  price: number;
  description: string;
  category: string;
  image: string;
}

async function getProduct(id: string): Promise<Product> {
  const res = await fetch(`https://fakestoreapi.com/products/${id}`);
  if (!res.ok) throw new Error("Failed to fetch product");
  return res.json();
}

const Post = async ({ params }: { params: { id: string } }) => {
  const product = await getProduct(params.id);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-4">{product.title}</h1>
      <Image
        src={product.image}
        alt={product.title}
        width={500}
        height={300}
        className="w-full h-auto"
        priority
      />
      <p className="text-lg text-gray-700 mt-4">${product.price}</p>
      <p className="text-md text-gray-600 mt-2">{product.description}</p>
      <p className="text-sm italic text-gray-500 mt-1">
        Category: {product.category}
      </p>
    </div>
  );
};

export default Post;
