"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { Loader2, X, Calendar, Activity, Utensils, Brain, ArrowUpRight } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Alert, AlertDescription } from "@/components/ui/alert"
import chartDataService, { type RecommendationHistoryItem } from "@/services/chart.data.service"

interface RecommendationHistoryDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export default function RecommendationHistoryDialog({ open, onOpenChange }: RecommendationHistoryDialogProps) {
  const [history, setHistory] = useState<any>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (open) {
      fetchRecommendationHistory()
    }
  }, [open])

  const fetchRecommendationHistory = async () => {
    setIsLoading(true)
    setError(null)
    try {
      const data = await chartDataService.getRecommendationHistory()
      setHistory(data)
    } catch (err) {
      console.error("Failed to fetch recommendation history:", err)
      setError("Failed to load recommendation history. Please try again later.")
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "PPP")
    } catch (e) {
      return dateString
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case "diet":
        return <Utensils className="h-5 w-5 text-amber-500" />
      case "activity":
        return <Activity className="h-5 w-5 text-green-500" />
      case "nutrition":
        return <Brain className="h-5 w-5 text-blue-500" />
      default:
        return <Calendar className="h-5 w-5 text-primary" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase() || "") {
      case "diet":
        return "bg-amber-500/10 border-amber-200 dark: border-amber-800"
      case "activity":
        return "bg-green-500/10 border-green-200 dark:border-green-800"
      case "nutrition":
        return "bg-blue-500/10 border-blue-200 dark:border-blue-800"
      default:
        return "bg-primary/10 border-primary/20"
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence > 0.9) return "border-green-500 text-green-700 dark:text-green-300"
    if (confidence > 0.7) return "border-amber-500 text-amber-700 dark:text-amber-300"
    return "border-red-500 text-red-700 dark:text-red-300"
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl">Recommendation History</DialogTitle>
          <DialogDescription>
            View your past nutrition recommendations based on AI analysis.
          </DialogDescription>
        </DialogHeader>
        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>

        {isLoading ? (
          <div className="space-y-4 py-4">
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          </div>
        ) : error ? (
          <Alert variant="destructive" className="my-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : (
          <ScrollArea className="max-h-[60vh] pr-4">
            <div className="space-y-6 py-4">
              {history.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <Calendar className="h-12 w-12 text-muted-foreground mb-2" />
                  <h3 className="text-lg font-medium">No recommendation history</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    You don't have any saved recommendations yet. Check back after using the AI recommendations feature.
                  </p>
                </div>
              ) : (
                history.map((item :any) => (
                  <div key={item.recommendationId} className={`p-4 rounded-lg border ${getCategoryColor("diet")}`}>
                    <div className="flex items-start gap-3">
                      <div className="rounded-full p-2 bg-background">{getCategoryIcon("diet")}</div>
                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">Food Recommendation</h4>
                          </div>
                          <div className="text-sm text-muted-foreground">{formatDate(item.createdAt)}</div>
                        </div>
                        
                        {/* Nutrition Targets */}
                        <div className="mt-3 pt-3 border-t border-border/50">
                          <h5 className="text-xs font-medium uppercase text-muted-foreground mb-2">
                            Daily Nutritional Targets
                          </h5>
                          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 text-sm mb-4">
                            {item.nutritionTargets && Object.entries(item.nutritionTargets).map(([key, value]) => (
                              <div key={key} className="bg-teal-50 dark:bg-teal-900/30 p-2 rounded-lg">
                                <span className="text-xs font-medium text-gray-700 dark:text-gray-300 capitalize">
                                  {key.replace(/([A-Z])/g, ' $1').trim()}
                                </span>
                                <div className="font-medium text-sm">
                                  {typeof value === 'number' ? value.toFixed(1) : String(value)}
                                  {key.includes('Calorie') ? '' : 'g'}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        {/* Recommended Foods */}
                        {item.predictions && (
                          <div className="mt-3 pt-3 border-t border-border/50">
                            <h5 className="text-xs font-medium uppercase text-muted-foreground mb-2">
                              Recommended Foods
                            </h5>
                            <div className="space-y-4">
                              {Object.entries(item.predictions).map(([mealType, foods]) => (
                                <div key={mealType} className="space-y-2">
                                  <div className="flex items-center justify-between">
                                  <h6 className="text-sm font-medium text-teal-600 dark:text-teal-400 capitalize">
                                    {mealType}
                                  </h6>
                                  <h6 className="text-sm font-medium text-teal-600 dark:text-teal-400 capitalize">
                                    SERVING SIZE
                                  </h6>
                                  </div>
                                  <div className="grid grid-cols-1 gap-2">
                                    {Array.isArray(foods) && foods.map((food, index) => (
                                      <div 
                                        key={index}
                                        className="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm"
                                      >
                                        <span className="text-sm text-gray-900 dark:text-gray-100">{food.local_name}</span>
                                        <span className="text-xs text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                                          {(food.serving_size_grams).toFixed(2)} g
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        )}

        <div className="flex justify-between mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          <Button onClick={fetchRecommendationHistory} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                Refresh
                <ArrowUpRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
