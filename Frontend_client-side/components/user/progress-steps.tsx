"use client"

import { useRouter } from "next/navigation"
import { ArrowLeft } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface ProgressStepsProps {
  currentStep: number
  showBackButton?: boolean
  backUrl?: string
}

export default function ProgressSteps({ currentStep, showBackButton = false, backUrl }: ProgressStepsProps) {
  const router = useRouter()
  const steps = ["Your Details", "Verify Info", "Recommendation"]

  return (
    <div className="flex flex-col items-center justify-center w-full max-w-4xl mx-auto">
      {showBackButton && backUrl && (
        <Button
          variant="outline"
          size="lg"
          onClick={() => router.push(backUrl)}
          className="mb-6 self-start"
          aria-label="Go back"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
      )}
      <div className="flex items-center justify-center w-full bg-card/80 backdrop-blur-sm rounded-lg p-4 shadow-sm">
        {steps.map((step, index) => (
          <div key={step} className="flex items-center flex-1">
            <div className="flex flex-col items-center relative">
              <div
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300",
                  currentStep >= index + 1
                    ? "bg-primary text-primary-foreground shadow-sm"
                    : "bg-muted text-muted-foreground",
                )}
                aria-current={currentStep === index + 1 ? "step" : undefined}
              >
                {index + 1}
              </div>
              <span className="mt-2 text-xs font-medium text-center">{step}</span>
            </div>
            {index < steps.length - 1 && (
              <div
                className={cn(
                  "flex-1 h-1 mx-2 transition-all duration-300",
                  currentStep > index + 1 ? "bg-primary" : "bg-muted",
                )}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
