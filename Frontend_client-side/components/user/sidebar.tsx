// "use client";

// import Link from "next/link";
// import { usePathname, useRouter } from "next/navigation";
// import {
//   LayoutDashboard,
//   MessageSquare,
//    Search,
//   BookOpen,
//   Bell,
//   Sparkles,
//   Menu,
//   X,
//   User

// } from "lucide-react";
// import { useState, useEffect } from "react";
// import api from "@/services/api";

// export default function Sidebar() {
//   const pathname = usePathname();
//   const router = useRouter();
//   const [isOpen, setIsOpen] = useState(false);

//   useEffect(() => {
//     setIsOpen(false);
//   }, []);

//   const toggleSidebar = () => setIsOpen(!isOpen);

//   const menuItems = [
//     { name: "Dashboard", icon: LayoutDashboard, path: "/userDashboard" },
//     {
//       name: "Recommendation",
//       icon: Sparkles,
//       path: "/userDashboard/recommendation",
//     },
//     { name: "Feedback", icon: MessageSquare, path: "/userDashboard/feedback" },
//     {
//       name: "Content Search",
//       icon: Search,
//       path: "/userDashboard/contentSearch",
//     },
//     { name: "Learn Diet", icon: BookOpen, path: "/userDashboard/learn-dite" },
//     { name: "Notification", icon: Bell, path: "/userDashboard/notification" },
//     { name: "Account", icon: User, path: "/userDashboard/account" },
//   ];

//   return (
//     <>
//       {/* Menu toggle button */}
//       <div className="md:hidden fixed top-4 left-4 z-50">
//         <button
//           onClick={toggleSidebar}
//           className="p-2 rounded-lg shadow-sm"
//         >
//           {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
//         </button>
//       </div>

//       {/* Sidebar */}
//       <div
//         className={`fixed md:relative md:translate-x-0 z-40 w-60 min-h-screen border-r bg-[#D9D9D9] transition-transform duration-300 ease-in-out ${
//           isOpen ? "translate-x-0" : "-translate-x-full"
//         }`}
//       >
//         {/* Logo section */}
//         <div className="p-4 border-b">
//           <div className="flex items-center justify-between">
//             <div className="relative h-12 w-48">
//               <div className="absolute inset-0 flex items-center">
//                 <span className="text-2xl font-serif italic ml-1">Nutri</span>
//                 <span className="text-2xl font-bold ml-1 bg-gray-800 text-white px-2 rounded">
//                   Focus
//                 </span>
//               </div>
//             </div>
//             {/* Close button for mobile */}
//             <div className="md:hidden">
//               <button
//                 onClick={() => setIsOpen(false)}
//                 className="p-1 hover:bg-gray-100 rounded-full"
//               >
//                 <X className="h-6 w-6" />
//               </button>
//             </div>
//           </div>
//         </div>

//         {/* Navigation */}
//         <nav className="p-4 space-y-2">
//           {/* Menu items */}
//           {menuItems.map((item) => {
//             const isActive = pathname === item.path;
//             return (
//               <Link
//                 key={item.name}
//                 href={item.path}
//                 className={`flex items-center p-3 rounded-md transition-colors ${
//                   isActive
//                     ? "bg-[#c9c7d4] border-blue-700 border-2 font-bold"
//                     : "bg-[#c9c7d4]"
//                 }`}
//                 onClick={() => setIsOpen(false)}
//               >
//                 <item.icon className="h-5 w-5 mr-3" />
//                 <span className="md:inline">{item.name}</span>
//               </Link>
//             );
//           })}
//         </nav>
//       </div>
//     </>
//   );
// }

"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  MessageSquare,
  Search,
  BookOpen,
  Bell,
  Sparkles,
  Menu,
  X,
  User,
  LogOut,
} from "lucide-react";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { heroData } from "@/data/HomeData/data";
import Image from "next/image";

export default function Sidebar() {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  const hero = heroData[0];
  const [currentTheme, setCurrentTheme] = useState<string>("light");
  const logoSrc = currentTheme === "dark" ? hero.logo2 : hero.logo1;

  useEffect(() => {
    // Function to get current theme
    const getTheme = () => {
      if (typeof window !== "undefined") {
        const storedTheme = localStorage.getItem("ui-theme");
        return storedTheme || "light";
      }
      return "light";
    };
    // Set initial theme
    setCurrentTheme(getTheme());

    // Create a MutationObserver to watch for theme class changes on <html>
    if (typeof window !== "undefined") {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (
            mutation.type === "attributes" &&
            mutation.attributeName === "class"
          ) {
            // HTML class changed, check if dark mode is active
            const isDarkMode =
              document.documentElement.classList.contains("dark");
            setCurrentTheme(isDarkMode ? "dark" : "light");
          }
        });
      });

      // Start observing the document with the configured parameters
      observer.observe(document.documentElement, { attributes: true });

      // Also listen for storage events (for changes in other tabs)
      const handleStorageChange = () => {
        setCurrentTheme(getTheme());
      };
      window.addEventListener("storage", handleStorageChange);

      // Cleanup
      return () => {
        observer.disconnect();
        window.removeEventListener("storage", handleStorageChange);
      };
    }
  }, []);

  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  const toggleSidebar = () => setIsOpen(!isOpen);

  const menuItems = [
    { name: "Dashboard", icon: LayoutDashboard, path: "/userDashboard" },
    {
      name: "Recommendation",
      icon: Sparkles,
      path: "/userDashboard/recommendation",
    },
    {
      name: "Content Search",
      icon: Search,
      path: "/userDashboard/contentSearch",
    },
    { name: "Learn Diet", icon: BookOpen, path: "/userDashboard/learn-dite" },
    { name: "Notification", icon: Bell, path: "/userDashboard/notification" },
    { name: "Account", icon: User, path: "/userDashboard/account" },
    { name: "Feedback", icon: MessageSquare, path: "/userDashboard/feedback" },
  ];

  return (
    <>
      {/* Menu toggle button */}
      <div className="md:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={toggleSidebar}
          aria-label={isOpen ? "Close menu" : "Open menu"}
        >
          {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
        </Button>
      </div>

      {/* Sidebar */}
      <div
        className={cn(
          "fixed md:relative md:translate-x-0 z-40 w-64 h-full bg-card border-r transition-transform duration-300 ease-in-out",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        {/* Logo section */}
        <div className="p-5 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center w-full">
              <Image
                src={logoSrc || "/placeholder.svg"}
                alt="NutriFocus Logo"
                width={120}
                height={40}
                className="h-6 w-auto m-auto"
              />
            </div>
            {/* Close button for mobile */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsOpen(false)}
                aria-label="Close menu"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="p-4 space-y-2">
          {/* Menu items */}
          {menuItems.map((item) => {
            const isActive = pathname === item.path;
            return (
              <Link
                key={item.name}
                href={item.path}
                className={cn(
                  "flex items-center p-3 rounded-md transition-colors",
                  isActive
                    ? "bg-primary/10 text-primary font-medium border-l-4 border-primary"
                    : "hover:bg-muted"
                )}
              >
                <item.icon className="h-5 w-5 mr-3" />
                <span>{item.name}</span>
              </Link>
            );
          })}
        </nav>
      </div>
    </>
  );
}
