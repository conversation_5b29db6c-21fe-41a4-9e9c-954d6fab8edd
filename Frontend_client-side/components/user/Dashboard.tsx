"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
} from "recharts";
import {
  Loader2,
  TrendingDown,
  TrendingUp,
  Target,
  Award,
  Calendar,
  Activity,
  Scale,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import type { UserProfile } from "@/services/user.service";
import { userService } from "@/services/user.service";
import RecommendationHistoryDialog from "./recommendation-history";
import chartDataService from "@/services/chart.data.service";
import { toast } from "react-hot-toast";
import HealthMetricsOnboarding from "./health-metrices-onboarding";

interface UserStats {
  macroData: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  monthlyData: Array<{
    name: string;
    weight: number;
    target: number;
  }>;
  nutrientData: Array<{
    nutrient: string;
    amount: number;
    fullMark: number;
  }>;
  weeklyCalories: Array<{
    day: string;
    calories?: number;
    target: number;
    isFallback?: boolean;
  }>;
  weightHistoryData: Array<{
    name: string;
    weight: number;
    target: number;
  }>;
}

interface DailyPlanData {
  [meal: string]: Array<{
    local_name: string;
    serving_nutrition: {
      calories: number;
    };
  }>;
}

const mockUserStats: UserStats = {
  macroData: [],
  monthlyData: [],
  nutrientData: [
    { nutrient: "Sodium", amount: 50, fullMark: 100 },
    { nutrient: "Fiber", amount: 80, fullMark: 100 },
    { nutrient: "Sugar", amount: 70, fullMark: 100 },
    { nutrient: "Protein", amount: 100, fullMark: 100 },
    { nutrient: "Carbohydrate", amount: 90, fullMark: 100 },
    { nutrient: "Fat", amount: 85, fullMark: 100 },
  ],
  weeklyCalories: [],
  weightHistoryData: [],
};

const COLORS = [
  "#8884d8",
  "#82ca9d",
  "#ffc658",
  "#ff8042",
  "#0088fe",
  "#00c49f",
];

export default function Dashboard() {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [dailyPlanData, setDailyPlanData] = useState<DailyPlanData>({});
  const [todayTarget, setTodayTarget] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeIndex, setActiveIndex] = useState(0);
  const [historyDialogOpen, setHistoryDialogOpen] = useState(false);
  const [hasHealthMetrics, setHasHealthMetrics] = useState<boolean>(false);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);
        const profile = await userService.getUserProfile();
        console.log(profile)
        const hasMetrics = !!(
          profile?.healthMetrics?.age &&
          profile?.healthMetrics?.weight &&
          profile?.healthMetrics?.height &&
          profile?.healthMetrics?.gender &&
          profile?.healthMetrics?.activityLevel
        );

        setHasHealthMetrics(hasMetrics);

        if (hasMetrics) {
          const weightHistoryData = await chartDataService.getWeightHistoryData();
          const macroData = await chartDataService.getMacronutrientData();
          const nutrientData = await chartDataService.getNutrientData();
          const weeklyCalorieData = await chartDataService.getWeeklyTargetCalorieData(profile.id);
          const dailyPlan = await chartDataService.getDailyPlanData();
          const target = await chartDataService.getTodaysTargetCalorie();
          
          setTodayTarget(target.daily_calorie_target);
          setUserStats({
            ...mockUserStats,
            macroData: macroData || mockUserStats.macroData,
            nutrientData: nutrientData && nutrientData.length > 0 ? nutrientData : mockUserStats.nutrientData,
            monthlyData: weightHistoryData,
            weightHistoryData,
            weeklyCalories: weeklyCalorieData,
          });
          setDailyPlanData(dailyPlan && Object.keys(dailyPlan).length > 0 ? dailyPlan : {});
        }

        setUserProfile(profile);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching user data:", error);
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  const handleHealthMetricsComplete = async (data: any) => {
    try {
      await userService.updateHealthMetricsOnBoarding(data);
      setHasHealthMetrics(true);
      const updatedProfile = await userService.getUserProfile();
      setUserProfile(updatedProfile);
      const weightHistoryData = await chartDataService.getWeightHistoryData();
      const macroData = await chartDataService.getMacronutrientData();
      const nutrientData = await chartDataService.getNutrientData();

      const weeklyCalorieData =
        await chartDataService.getWeeklyTargetCalorieData(updatedProfile.id);
      const dailyPlan = await chartDataService.getDailyPlanData();
      setUserStats({
        ...mockUserStats,
        macroData: macroData || mockUserStats.macroData,
        nutrientData:
          nutrientData && nutrientData.length > 0
            ? nutrientData
            : mockUserStats.nutrientData,
        monthlyData: weightHistoryData,
        weightHistoryData,
        weeklyCalories: weeklyCalorieData,
      });
      setDailyPlanData(
        dailyPlan && Object.keys(dailyPlan).length > 0 ? dailyPlan : {}
      );
    } catch (error) {
      console.error("Error saving health metrics:", error);
      // toast.error("Failed to save health metrics");
    }
  };

  const CustomBarTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background p-3 border rounded-md shadow-sm">
          <p className="font-medium">{`${label}`}</p>
          <p className="text-primary">{`Weight: ${payload[0].value} kg`}</p>
          <p className="text-green-500">{`Target: ${payload[1].value} kg`}</p>
        </div>
      );
    }
    return null;
  };

  const CustomCalorieTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background p-3 border rounded-md shadow-sm">
          <p className="font-medium">{`${label}`}</p>
          <p className={data.isFallback ? "text-gray-500" : "text-primary"}>
            {`Target: ${payload[0].value} kcal`}
            {data.isFallback && (
              <span className="block text-xs text-gray-500 mt-1">
                No data recorded – using fallback value
              </span>
            )}
          </p>
        </div>
      );
    }
    return null;
  };

  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };

  if (isLoading) {
    return (
      <div className="flex flex-col text-slate-600 items-center justify-center min-h-[60vh]">
        <Loader2 className="h-12 w-12 animate-spin mb-4" />
        <p className="text-lg">Loading your dashboard...</p>
      </div>
    );
  }

  if (hasHealthMetrics === false) {
    return (
      <div className="container mx-auto p-4 animate-fade-in">
        <HealthMetricsOnboarding onComplete={handleHealthMetricsComplete} />
      </div>
    );
  }

  const startWeight = userProfile?.startingWeight;
  const currentWeight = userProfile?.healthMetrics?.weight;
  const targetWeight = userProfile?.targetWeight;
  const weightGoal = userProfile?.healthMetrics?.goals;

  if (!weightGoal) {
    return (
      <div className="flex flex-col text-slate-600 items-center justify-center min-h-[60vh]">
        <Loader2 className="h-12 w-12 animate-spin mb-4" />
        <p className="text-lg">Loading your dashboard...</p>
      </div>
    );
  }

  const weightChange = (startWeight ?? 0) - (currentWeight ?? 0);
  const totalWeightDifference = Math.abs(
    (startWeight ?? 0) - (targetWeight ?? 0)
  );
  let weightProgressValue = 0;
  let progressLabel = "";
  let progressColor = "text-primary";
  let trendIcon = <TrendingDown className="h-3 w-3 mr-1 text-green-500" />;

  if (weightGoal.includes("Lose")) {
    const weightLost = Math.max(0, weightChange);
    weightProgressValue =
      totalWeightDifference > 0
        ? Math.round((weightLost / totalWeightDifference) * 100)
        : 0;
    progressLabel = `${weightLost.toFixed(
      1
    )} kg lost of ${totalWeightDifference.toFixed(1)} kg goal`;
    progressColor = "text-green-500";
    trendIcon = <TrendingDown className="h-3 w-3 mr-1 text-green-500" />;
  } else if (weightGoal.includes("Gain")) {
    const weightGained = Math.max(0, -weightChange);
    weightProgressValue =
      totalWeightDifference > 0
        ? Math.round((weightGained / totalWeightDifference) * 100)
        : 0;
    progressLabel = `${weightGained.toFixed(
      1
    )} kg gained of ${totalWeightDifference.toFixed(1)} kg goal`;
    progressColor = "text-blue-500";
    trendIcon = <TrendingUp className="h-3 w-3 mr-1 text-blue-500" />;
  } else {
    const maintenanceRange = 2;
    const deviation = Math.abs((currentWeight ?? 0) - (targetWeight ?? 0));
    weightProgressValue = Math.max(
      0,
      100 - Math.round((deviation / maintenanceRange) * 100)
    );
    progressLabel = `Maintaining within ${deviation.toFixed(
      1
    )} kg of ${targetWeight} kg target`;
    progressColor = "text-amber-500";
    trendIcon = <Scale className="h-3 w-3 mr-1 text-amber-500" />;
  }

  weightProgressValue = Math.min(100, weightProgressValue);

  // Calculate Y-axis domain for calorie chart
  const calorieTargets = userStats?.weeklyCalories.map((d) => d.target) || [2200];
  const minTarget = 0;
  const maxTarget = Math.max(...calorieTargets);
  const yAxisDomain = [
    Math.floor(minTarget / 100) * 100,  // Round down to nearest 100
    maxTarget + 100  // Add some padding at the top
  ];

  return (
    <div className="container mx-auto p-4 space-y-6 animate-fade-in">
      <RecommendationHistoryDialog
        open={historyDialogOpen}
        onOpenChange={setHistoryDialogOpen}
      />
      <Card className="container from-primary/10 to-primary/5 border-none">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-foreground">
                Welcome, {userProfile?.name?.split(" ")[0]}
              </h1>
              <p className="text-muted-foreground mt-1">
                Track your nutrition and health progress with AI-assisted
                Ethiopian nutritional guidance
              </p>
            </div>
            <Button
              className="mt-4 md:mt-0"
              size="lg"
              onClick={() => setHistoryDialogOpen(true)}
            >
              View Recommendations
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <div className="rounded-full bg-primary/10 p-3 mb-4">
              <Activity className="h-6 w-6 text-primary" />
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Current Weight</p>
              <h3 className="text-3xl font-bold">
                {userProfile?.healthMetrics?.weight || "00"} kg
              </h3>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <div className="rounded-full bg-green-500/10 p-3 mb-4">
              <Target className="h-6 w-6 text-green-500" />
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Target Weight</p>
              <h3 className="text-3xl font-bold">
                {userProfile?.targetWeight || "00"} kg
              </h3>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <div className="rounded-full bg-blue-500/10 p-3 mb-4">
              <Calendar className="h-6 w-6 text-blue-500" />
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                Daily Calorie Target
              </p>
              <h3 className="text-3xl font-bold">
                {todayTarget ? `${todayTarget.toFixed(0)} kcal` : "No Data"}
              </h3>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 flex flex-col items-center justify-center">
            <div className="rounded-full bg-amber-500/10 p-3 mb-4">
              <Award className="h-6 w-6 text-amber-500" />
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Progress to Goal</p>
              <h3 className="text-3xl font-bold">{weightProgressValue}%</h3>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">
              {weightGoal.includes("Lose")
                ? "Weight Loss Progress"
                : weightGoal.includes("Gain")
                ? "Weight Gain Progress"
                : "Weight Maintenance"}
            </CardTitle>
            <CardDescription>{progressLabel}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Progress value={weightProgressValue} className="h-2" />
              <div className="flex justify-between text-xs text-muted-foreground">
                <div className="flex items-center">
                  {trendIcon}
                  <span>Started: {startWeight} kg</span>
                </div>
                <div className="flex items-center">
                  <Target className="h-3 w-3 mr-1 text-primary" />
                  <span>Goal: {targetWeight} kg</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Sample Daily Plan</CardTitle>
            <CardDescription>
              {/* Check if any meal has food items */}
              Total Target:{" "}
              {Object.values(dailyPlanData).some(
                (items) => Array.isArray(items) && items.length > 0
              )
                ? todayTarget?.toFixed(2) ?? "N/A"
                : "N/A"}{" "}
              kcal
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2 text-sm text-muted-foreground">
            {Object.values(dailyPlanData).some(
              (items) => Array.isArray(items) && items.length > 0
            ) ? (
              Object.entries(dailyPlanData).map(([meal, items]) => {
                if (!items || items.length === 0) {
                  return (
                    <div key={meal} className="flex justify-between">
                      <span className="capitalize">{meal}</span>
                      <span>No food available</span>
                    </div>
                  );
                }
                const food = items[0];
                const calories = food?.serving_nutrition?.calories?.toFixed(2) ?? 0;
                return (
                  <div key={meal} className="flex justify-between">
                    <span className="capitalize">{meal}</span>
                    <span>
                      {food?.local_name || "Unknown food"} - {calories} kcal
                    </span>
                  </div>
                );
              })
            ) : (
              <div>
                {" "}
                No meal plan available for today. Please check your
                recommendations.
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="weight" className="w-full">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="weight">Weight Tracking</TabsTrigger>
          <TabsTrigger value="calories">Calorie Target</TabsTrigger>
          <TabsTrigger value="macros">Macronutrients</TabsTrigger>
          {/* <TabsTrigger value="nutrients">Nutrient Balance</TabsTrigger> */}
        </TabsList>

        <TabsContent value="weight" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Weight Progress</CardTitle>
              <CardDescription>
                Track your weight changes over time compared to your target
              </CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={userStats?.weightHistoryData || []}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke="#888"
                    strokeOpacity={0.2}
                  />
                  <XAxis dataKey="name" />
                  <YAxis 
                    domain={[
                      (dataMin: number) => Math.max(30, Math.floor(dataMin - 5)), // Minimum of 30 or 5 below lowest value
                      (dataMax: number) => Math.ceil(dataMax + 5) // 5 above highest value
                    ]} 
                  />
                  <Tooltip content={<CustomBarTooltip />} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="weight"
                    name="Weight (kg)"
                    stroke="#8884d8"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="target"
                    name="Target (kg)"
                    stroke="#82ca9d"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Weekly Calorie Target</CardTitle>
              <CardDescription>
                Your recommended daily calorie targets, zoomed to show variation
              </CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={userStats?.weeklyCalories || []}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke="#888"
                    strokeOpacity={0.2}
                  />
                  <XAxis dataKey="day" />
                  <YAxis domain={yAxisDomain} />
                  <Tooltip content={<CustomCalorieTooltip />} />
                  {/* <Legend /> */}
                  <Legend 
                    content={() => (
                      <ul className="flex justify-center gap-4">
                        <li className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: "#82ca9d" }}
                          />
                          <span style={{ color: "#82ca9d" }}>Target Calories</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: "#e5e7eb" }}
                          />
                          <span style={{ color: "#e5e7eb" }}>Skipped Target Calories</span>
                        </li>
                      </ul>
                    )}
                  />
                  <Bar
                    dataKey="target"
                    name="Target Calories"
                    radius={[4, 4, 0, 0]}
                  >
                    {userStats?.weeklyCalories.map((entry, index) => (
                      <Cell 
                        key={`cell-${index}`} 
                        fill={entry.isFallback ? "#e5e7eb" : "#82ca9d"} 
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="macros" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Macronutrient Distribution</CardTitle>
              <CardDescription>
                Breakdown of your protein, carbohydrate, and fat intake
              </CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    activeIndex={activeIndex}
                    data={userStats?.macroData || []}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    onMouseEnter={onPieEnter}
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                    labelLine={false}
                  >
                    {userStats?.macroData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value: number) => [`${value}%`, "Percentage"]}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

      </Tabs>
    </div>
  );
}
