// recommendationData.ts
export const recommendationData = [
  {
    id: 1,
    foodItem: "Metatish",
    description: "Sweet potato, injinous batatas L. boiled",
    serving: 360,
    calories: 20,
    location: "Welayita",
  },
  {
    id: 2,
    foodItem: "Shimbra, kikk wet",
    description:
      "Chick pea (split) + shallot + chili + oil + garlic + salt, sauce",
    serving: 250,
    calories: 34,
    location: "Common in ETH",
  },
  {
    id: 3,
    foodItem: "Wolewol",
    description: "Maize + teff, porridge",
    serving: 231,
    calories: 45,
    location: "Gambella/Nuer",
  },
  {
    id: 4,
    foodItem: "Tehlo",
    description: "Barley flour (roasted) + salt + water",
    serving: 360,
    calories: 20,
    location: "Common in ETH",
  },
  {
    id: 5,
    foodItem: "Merqa",
    description: "Maize, Zea mays L., porridge with salt",
    serving: 400,
    calories: 20,
    location: "Borena",
  },
  {
    id: 6,
    foodItem: "Shumo",
    description: "Maize + kidney beans, boiled",
    serving: 744,
    calories: 42,
    location: "E.Hararge/Kersa",
  },
];

export const userDetails = {
  goal: "Maintain Weight",
  duration: "3-4 month",
};
