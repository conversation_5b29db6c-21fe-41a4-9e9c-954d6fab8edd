"use client";

import { useState } from "react";
import { use<PERSON>outer } from "next/navigation";
import { z } from "zod";
import { toast } from "react-hot-toast";
import { Loader2, ArrowRight, CheckCircle, Wand2, User, Activity, Apple, Salad } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { useRecommendationStore } from "@/store/recommendationStore";
import { userService } from "@/services/user.service";

// Define the schema for health metrics validation, aligned with step1
const healthMetricsSchema = z.object({
  age: z.number().int().min(18).max(79, { message: "Age must be between 18 and 79" }),
  weight: z.number().min(48).max(119, { message: "Weight must be between 48 and 119 kg" }),
  height: z.number().min(150).max(200, { message: "Height must be between 150 and 200 cm" }),
  gender: z.enum(["Male", "Female"], { message: "Gender is required" }),
  activity_level: z.enum(
    ["Sedentary", "Lightly Active", "Moderately Active", "Very Active"],
    { message: "Activity level is required" }
  ),
  dietary_preference: z.enum(["Omnivore", "Vegetarian", "Vegan"], {
    message: "Dietary preference is required",
  }),
  weight_goal: z.enum(["Lose Weight", "Maintain Weight", "Gain Weight"], {
    message: "Weight goal is required",
  }),
  health_issues: z.array(
    z.enum(["None", "Diabetes", "Hypertension", "Heart Disease", "Kidney Disease", "Acne"])
  ),
  fasting: z.enum(["Yes", "No"], { message: "Fasting preference is required" }),
  location: z.string().min(1, { message: "Location is required" }),
});

type HealthMetrics = z.infer<typeof healthMetricsSchema>;

interface HealthMetricsOnboardingProps {
  onComplete: (data: HealthMetrics) => Promise<void>;
}

export default function HealthMetricsOnboarding({ onComplete }: HealthMetricsOnboardingProps) {
  const router = useRouter();
  const { fetchPrediction, setHealthMetrics, isLoading } = useRecommendationStore();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<Partial<HealthMetrics>>({
    gender: "Male",
    activity_level: "Moderately Active",
    dietary_preference: "Omnivore",
    weight_goal: "Maintain Weight",
    health_issues: [],
    fasting: "No",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const totalSteps = 4;
  const progressPercentage = (currentStep / totalSteps) * 100;

  type HealthIssue = "None" | "Diabetes" | "Hypertension" | "Heart Disease" | "Kidney Disease" | "Acne";

  const healthIssuesOptions: { id: HealthIssue; label: string }[] = [
    { id: "None", label: "None" },
    { id: "Diabetes", label: "Diabetes" },
    { id: "Hypertension", label: "Hypertension" },
    { id: "Heart Disease", label: "Heart Disease" },
    { id: "Kidney Disease", label: "Kidney Disease" },
    { id: "Acne", label: "Acne" },
  ];

  const locationOptions = [
    "afar",
    "asossa/berta",
    "asossa/komo",
    "asossa/mao",
    "bale",
    "bench and maji zone/bench",
    "bench and maji zone/menit",
    "bench and maji zone/sheko",
    "borena",
    "dorze",
    "e. wellega",
    "e.hararge/jarso",
    "e.hararge/kersa",
    "gambella/agnuak",
    "gambella/nuer",
    "gamo",
    "gedeo",
    "gonder/negede weyto",
    "guji",
    "gurage/cheha",
    "harari",
    "keficho zone/keficho",
    "kemache zone/gumuz",
    "kembata",
    "konsso",
    "metekel/gumuz",
    "metekel/shinasha",
    "n. gonder/dembia",
    "n. omo/konta",
    "sidama",
    "tigray/central zone",
    "tigray/eastern zone",
    "tigray/kunama",
    "tigray/mekele",
    "tigray/shire",
    "tigray/southern zone",
    "urban/common",
    "w. wellega",
    "welayita",
    "addis ababa",
    "dire dawa",
    "adama",
    "gondar",
    "mekelle",
    "hawassa",
    "bahir dar",
    "jimma",
    "debre markos",
  ];

  const stepIcons = [
    <User className="h-5 w-5 text-primary" />,
    <Activity className="h-5 w-5 text-primary" />,
    <Apple className="h-5 w-5 text-primary" />,
    <Salad className="h-5 w-5 text-primary" />,
  ];

  const handleNextStep = () => {
    if (currentStep === 1) {
      const step1Fields = healthMetricsSchema.pick({ age: true, weight: true, height: true, gender: true });
      const result = step1Fields.safeParse(formData);
      if (!result.success) {
        const fieldErrors: Record<string, string> = {};
        result.error.issues.forEach((issue) => {
          fieldErrors[issue.path[0]] = issue.message;
        });
        setErrors(fieldErrors);
        toast.error("Please correct the errors in the form");
        return;
      }
    } else if (currentStep === 2) {
      const step2Fields = healthMetricsSchema.pick({ activity_level: true, health_issues: true });
      const result = step2Fields.safeParse(formData);
      if (!result.success) {
        const fieldErrors: Record<string, string> = {};
        result.error.issues.forEach((issue) => {
          fieldErrors[issue.path[0]] = issue.message;
        });
        setErrors(fieldErrors);
        toast.error("Please correct the errors in the form");
        return;
      }
    } else if (currentStep === 3) {
      const step3Fields = healthMetricsSchema.pick({
        dietary_preference: true,
        weight_goal: true,
        fasting: true,
        location: true,
      });
      const result = step3Fields.safeParse(formData);
      if (!result.success) {
        const fieldErrors: Record<string, string> = {};
        result.error.issues.forEach((issue) => {
          fieldErrors[issue.path[0]] = issue.message;
        });
        setErrors(fieldErrors);
        toast.error("Please correct the errors in the form");
        return;
      }
    }

    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
      setErrors({});
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setErrors({});
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: Number(value) });
    setErrors({ ...errors, [name]: "" });
  };

  const handleSelectChange = (name: keyof HealthMetrics, value: string) => {
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" });
  };

  const handleHealthIssueChange = (id: HealthIssue, checked: boolean) => {
    const currentIssues = formData.health_issues || [];
    const newIssues = checked
      ? [...currentIssues, id]
      : currentIssues.filter((issue) => issue !== id);
    setFormData({ ...formData, health_issues: newIssues });
    setErrors({ ...errors, health_issues: "" });
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      const validationResult = healthMetricsSchema.safeParse(formData);

      if (!validationResult.success) {
        const fieldErrors: Record<string, string> = {};
        validationResult.error.issues.forEach((issue) => {
          fieldErrors[issue.path[0]] = issue.message;
        });
        setErrors(fieldErrors);
        toast.error("Please correct the errors in the form");
        return;
      }

      // Format the data to match backend expectations
      const formattedData = {
        age: Number(validationResult.data.age),
        gender: validationResult.data.gender,
        weight: Number(validationResult.data.weight),
        height: Number(validationResult.data.height),
        health_issues: validationResult.data.health_issues,
        activity_level: validationResult.data.activity_level,
        dietary_preference: validationResult.data.dietary_preference,
        weight_goal: validationResult.data.weight_goal,
        fasting: validationResult.data.fasting,
        location: validationResult.data.location || ""
      };
      console.log("Sending data to backend:", formattedData);
      await userService.updateHealthMetricsOnBoarding(formattedData);
      
      setHealthMetrics(validationResult.data);
      toast.success("Profile completed successfully!");
      setCurrentStep(currentStep + 1);  // Move to step 4 first
    } catch (error: any) {
      console.error("Error saving health metrics:", error);
      const errorMessage = error.response?.data?.message || "Failed to save your health metrics. Please try again.";
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGenerateFirstPlan = async () => {
    try {
      setIsSubmitting(true);
      const validationResult = healthMetricsSchema.safeParse(formData);
      if (!validationResult.success) {
        toast.error("Invalid form data");
        return;
      }
      await onComplete(validationResult.data);
      await fetchPrediction();
      toast.success("Your first meal plan has been generated!");
      router.push("/userDashboard/recommendation/step3");
    } catch (error) {
      console.error("Error generating the first meal plan:", error);
      toast.error("Failed to generate your first meal plan. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center bg-gradient-to-b bg-red-300 from-gray-50 to-gray-100 min-h-screen">
      <Card className="w-full shadow-xl rounded-xl overflow-hidden">
        <CardHeader className="bg-primary/5">
          <CardTitle className="text-3xl font-bold text-center text-primary">
            Welcome to NutriFocus!
          </CardTitle>
          <CardDescription className="text-center text-lg text-muted-foreground">
            Let's set up your profile for personalized nutrition recommendations
          </CardDescription>
          <div className="mt-6">
            <Progress value={progressPercentage} className="h-3 bg-gray-200" />
            <div className="flex justify-between mt-3 text-sm font-medium text-muted-foreground">
              <span className="flex items-center gap-1">
                {stepIcons[0]} Basic Info
              </span>
              <span className="flex items-center gap-1">
                {stepIcons[1]} Health Details
              </span>
              <span className="flex items-center gap-1">
                {stepIcons[2]} Preferences
              </span>
              <span className="flex items-center gap-1">
                {stepIcons[3]} Generate Plan
              </span>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {currentStep === 1 && (
            <div className="space-y-6 animate-fade-in">
              <h3 className="text-xl font-semibold flex items-center gap-2 text-primary">
                {stepIcons[0]} Basic Information
              </h3>
              <Separator />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="age">Age</Label>
                  <Input
                    id="age"
                    name="age"
                    type="number"
                    placeholder="Enter your age (18-79)"
                    value={formData.age ?? ""}
                    onChange={handleInputChange}
                    className={errors.age ? "border-destructive" : ""}
                  />
                  {errors.age && <p className="text-sm text-destructive">{errors.age}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gender">Gender</Label>
                  <Select
                    value={formData.gender}
                    onValueChange={(value) => handleSelectChange("gender", value)}
                  >
                    <SelectTrigger id="gender">
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Male">Male</SelectItem>
                      <SelectItem value="Female">Female</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.gender && <p className="text-sm text-destructive">{errors.gender}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="weight">Weight (kg)</Label>
                  <Input
                    id="weight"
                    name="weight"
                    type="number"
                    placeholder="Enter your weight (48-119 kg)"
                    value={formData.weight ?? ""}
                    onChange={handleInputChange}
                    className={errors.weight ? "border-destructive" : ""}
                  />
                  {errors.weight && <p className="text-sm text-destructive">{errors.weight}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="height">Height (cm)</Label>
                  <Input
                    id="height"
                    name="height"
                    type="number"
                    placeholder="Enter your height (150-200 cm)"
                    value={formData.height ?? ""}
                    onChange={handleInputChange}
                    className={errors.height ? "border-destructive" : ""}
                  />
                  {errors.height && <p className="text-sm text-destructive">{errors.height}</p>}
                </div>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6 animate-fade-in">
              <h3 className="text-xl font-semibold flex items-center gap-2 text-primary">
                {stepIcons[1]} Health Details
              </h3>
              <Separator />
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="activity_level">Activity Level</Label>
                  <Select
                    value={formData.activity_level}
                    onValueChange={(value) => handleSelectChange("activity_level", value)}
                  >
                    <SelectTrigger id="activity_level">
                      <SelectValue placeholder="Select activity level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Sedentary">Sedentary (little or no exercise)</SelectItem>
                      <SelectItem value="Lightly Active">
                        Lightly Active (light exercise 1-3 days/week)
                      </SelectItem>
                      <SelectItem value="Moderately Active">
                        Moderately Active (moderate exercise 3-5 days/week)
                      </SelectItem>
                      <SelectItem value="Very Active">
                        Very Active (hard exercise 6-7 days/week)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.activity_level && (
                    <p className="text-sm text-destructive">{errors.activity_level}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Health Issues (if any)</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                    {healthIssuesOptions.map((option) => (
                      <div key={option.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={option.id}
                          checked={(formData.health_issues || []).includes(option.id)}
                          onCheckedChange={(checked) =>
                            handleHealthIssueChange(option.id as HealthIssue, checked === true)
                          }
                        />
                        <Label htmlFor={option.id} className="cursor-pointer">
                          {option.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                  {errors.health_issues && (
                    <p className="text-sm text-destructive">{errors.health_issues}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6 animate-fade-in">
              <h3 className="text-xl font-semibold flex items-center gap-2 text-primary">
                {stepIcons[2]} Preferences
              </h3>
              <Separator />
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="dietary_preference">Dietary Preference</Label>
                  <Select
                    value={formData.dietary_preference}
                    onValueChange={(value) => handleSelectChange("dietary_preference", value)}
                  >
                    <SelectTrigger id="dietary_preference">
                      <SelectValue placeholder="Select dietary preference" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Omnivore">Omnivore</SelectItem>
                      <SelectItem value="Vegetarian">Vegetarian</SelectItem>
                      <SelectItem value="Vegan">Vegan</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.dietary_preference && (
                    <p className="text-sm text-destructive">{errors.dietary_preference}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="weight_goal">Weight Goal</Label>
                  <Select
                    value={formData.weight_goal}
                    onValueChange={(value) => handleSelectChange("weight_goal", value)}
                  >
                    <SelectTrigger id="weight_goal">
                      <SelectValue placeholder="Select weight goal" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Maintain Weight">Maintain Weight</SelectItem>
                      <SelectItem value="Lose Weight">Lose Weight</SelectItem>
                      <SelectItem value="Gain Weight">Gain Weight</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.weight_goal && (
                    <p className="text-sm text-destructive">{errors.weight_goal}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fasting">Do you fast?</Label>
                  <Select
                    value={formData.fasting}
                    onValueChange={(value) => handleSelectChange("fasting", value)}
                  >
                    <SelectTrigger id="fasting">
                      <SelectValue placeholder="Select fasting preference" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Yes">Yes</SelectItem>
                      <SelectItem value="No">No</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.fasting && <p className="text-sm text-destructive">{errors.fasting}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Select
                    value={formData.location}
                    onValueChange={(value) => handleSelectChange("location", value)}
                  >
                    <SelectTrigger id="location">
                      <SelectValue placeholder="Select your location" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        {locationOptions.map((location) => (
                          <SelectItem key={location} value={location}>
                            {location}
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  {errors.location && <p className="text-sm text-destructive">{errors.location}</p>}
                </div>

                <Alert className="mt-6 bg-primary/10 border-primary/20">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <AlertDescription>
                    You're almost done! Review your information and click "Complete Profile" to
                    finish.
                  </AlertDescription>
                </Alert>
              </div>
            </div>
          )}

          {currentStep === 4 && (
            <div className="space-y-6 animate-fade-in">
              <h3 className="text-xl font-semibold flex items-center gap-2 text-primary">
                {stepIcons[3]} Generate Your First Plan
              </h3>
              <Separator />
              <p className="text-center text-muted-foreground">
                Click below to generate your personalized meal plan based on your profile.
              </p>
              <div className="flex justify-center mt-6">
                <Button
                  className="w-full max-w-sm bg-primary hover:bg-primary/90"
                  onClick={handleGenerateFirstPlan}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      Get Your First Plan
                      <Wand2 className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="flex justify-between bg-primary/5 p-6">
          {currentStep > 1 ? (
            <Button
              variant="outline"
              onClick={handlePrevStep}
              disabled={isSubmitting}
              className="hover:bg-gray-100"
            >
              Back
            </Button>
          ) : (
            <div></div>
          )}

          {currentStep < totalSteps - 1 ? (
            <Button
              onClick={handleNextStep}
              className="bg-primary hover:bg-primary/90"
              disabled={isSubmitting}
            >
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : currentStep === totalSteps - 1 ? (
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="bg-primary hover:bg-primary/90"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  Complete Profile
                  <CheckCircle className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          ) : null}
        </CardFooter>
      </Card>
    </div>
  );
}