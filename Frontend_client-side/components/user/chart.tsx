import React from "react";
import { Bar, Bar<PERSON><PERSON> } from "recharts";
import { ChartConfig, ChartContainer } from "@/components/ui/chart";

const chartData = [
  { month: "January", desktop: 186, mobile: 80 },
  { month: "February", desktop: 305, mobile: 200 },
  { month: "March", desktop: 237, mobile: 120 },
  { month: "April", desktop: 73, mobile: 190 },
  { month: "May", desktop: 209, mobile: 130 },
  { month: "June", desktop: 214, mobile: 140 },
];

const chartConfig = {
  desktop: {
    label: "Desktop",
    color: "#2563eb",
  },
  mobile: {
    label: "Mobile",
    color: "#60a5fa",
  },
} satisfies ChartConfig;

const Chart = () => {
  return (
    <div>
      <ChartContainer config={chartConfig} className="h-[150px] md:h-[200px] md:w-full h- ">  
        <BarChart
          accessibilityLayer
          data={chartData}
          height={150} 
          margin={{ top:5, right: 0, left: 20, bottom: 10 }} 
        >
          <Bar dataKey="desktop" fill="var(--color-desktop)" radius={4} />
          <Bar dataKey="mobile" fill="var(--color-mobile)" radius={4}  />
        </BarChart>
      </ChartContainer>
    </div>
  );
};

export default Chart;
