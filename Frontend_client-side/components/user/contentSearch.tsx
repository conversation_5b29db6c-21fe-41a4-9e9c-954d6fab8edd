"use client";
import Image from "next/image";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Search, X, MapPin, Info, ArrowRight } from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import Injera from "@/public/Injera.jpg";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { api } from "@/services/api";
import { format } from "date-fns";
import { toast } from "react-hot-toast";

interface FoodItem {
  foodId: string;
  name: string;
  region: string;
  description: string;
  calories: number;
  category: string;
  image?: string;
  nutrients?: {
    protein: number;
    carbohydrates: number;
    fat: number;
  };
}

export default function ContentSearch() {
  const [allItems, setAllItems] = useState<FoodItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [loading, setLoading] = useState(true);
  const [categories, setCategories] = useState<string[]>(["all"]);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const router = useRouter();
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 9,
    total: 0,
    totalPages: 0,
  });
  const [error, setError] = useState<string | null>(null);

  const fetchFoods = async (searchParams = {}) => {
    try {
      setLoading(true);
      setError(null); // Add error state
      
      // Ensure all parameters are properly typed
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        ...(searchTerm ? { name: searchTerm } : {}),
        ...(selectedCategory !== "all" ? { category: selectedCategory } : {}),
        ...searchParams
      };
      
      console.log("Fetching with params:", params);
      
      const response = await api.get('/nutritional-database', { params });
      
      console.log("Response data:", response.data);
      setAllItems(response.data.data);
      setPagination({
        page: response.data.meta.page,
        limit: response.data.meta.limit,
        total: response.data.meta.total,
        totalPages: response.data.meta.totalPages,
      });
    } catch (error) {
      console.error('Error fetching foods:', error);
      setError("Failed to load food database. Please try again."); // Set error message
      toast.error("Failed to load food database");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFoods();
  }, [page, limit]);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await api.get('/nutritional-database/categories');
        setCategories(["all", ...response.data]);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);
 
  const filteredItems = allItems.filter((item) => {
    const matchesSearch =
      (item?.name?.toLowerCase() ?? "").includes(searchTerm.toLowerCase()) ||
      ((item?.description?.toLowerCase() ?? "").includes(searchTerm.toLowerCase()));

    const matchesCategory =
      selectedCategory === "all" || item?.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const handleClearSearch = () => {
    setSearchTerm("");
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchFoods({ page: 1 });
  };

  const handleNextPage = () => {
    if (page < totalPages) {
      setPage(page + 1);
      fetchFoods({ page: page + 1 });
    }
  };

  const handlePrevPage = () => {
    if (page > 1) {
      setPage(page - 1);
      fetchFoods({ page: page - 1 });
    }
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchFoods({ page: 1 });
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMMM d, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <div className="max-w-3xl mx-auto mb-10">
        <h1 className="text-3xl font-bold text-center mb-3">
          Ethiopian Food Nutrition Database
        </h1>
        <p className=" text-center mb-8">
          Search our comprehensive database of traditional Ethiopian foods and
          their nutritional information
        </p>

        <div className="flex flex-col sm:flex-row gap-3 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search by food name or ingredients..."
              className="pl-10 h-11"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <button
                className="absolute right-3 top-1/2 transform -translate-y-1/2 hover:text-primary"
                onClick={handleClearSearch}
                aria-label="Clear search"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>

          <Select value={selectedCategory} onValueChange={handleCategoryChange}>
            <SelectTrigger className="w-full sm:w-[180px] h-11">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category === "all" ? "All Categories" : category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {error && (
        <div className="text-center py-12 max-w-md mx-auto">
          <Info className="h-12 w-12 mx-auto text-red-500 mb-4" />
          <h3 className="text-xl font-medium mb-2">Error Loading Data</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => fetchFoods()}
          >
            Try Again
          </Button>
        </div>
      )}

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="space-y-3 w-2/3">
                      <Skeleton className="h-5 w-3/4" />
                      <div className="flex items-center">
                        <Skeleton className="h-4 w-4 mr-2 rounded-full" />
                        <Skeleton className="h-4 w-1/2" />
                      </div>
                    </div>
                    <Skeleton className="h-16 w-16 rounded-full" />
                  </div>
                  <Skeleton className="h-4 w-full mb-1" />
                  <Skeleton className="h-4 w-5/6 mb-1" />
                  <Skeleton className="h-4 w-4/6 mb-4" />
                  <div className="flex justify-between items-center">
                    <Skeleton className="h-5 w-1/3" />
                    <Skeleton className="h-5 w-1/4" />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t p-4">
                <Skeleton className="h-9 w-full" />
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : filteredItems.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {filteredItems.map((item) => (
              <Card
                key={item.foodId}
                className="overflow-hidden transition-all duration-200 hover:shadow-md"
              >
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h2 className="text-xl font-semibold mb-2">
                        {item.name}
                      </h2>
                      <div className="flex items-center text-sm mb-1">
                        <MapPin className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
                        <span>{item.region}</span>
                      </div>
                      {item.category && (
                        <Badge variant="outline" className="mt-1">
                          {item.category}
                        </Badge>
                      )}
                    </div>
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 rounded-full overflow-hidden bg-slate-100 border border-slate-200">
                        <Image
                          src={item.image || "/food-placeholder.png"}
                          alt={item.name}
                          width={10}
                          height={20}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  </div>

                  <p className="text-sm  mb-4 line-clamp-3">
                    {item.description}
                  </p>

                  <div className="flex justify-between items-center text-sm">
                    <span className="font-medium">Calories:</span>
                    <span className="font-bold">
                      {item.calories} cal
                    </span>
                  </div>
                </CardContent>
                <CardFooter className="border-t p-4">
                  <Button
                    variant="outline"
                    className="w-full justify-between"
                    onClick={() =>
                      router.push(`/userDashboard/contentSearch/${item.foodId}`)
                    }
                  >
                    View Nutritional Details
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
          
          {/* Move pagination controls here, outside the conditional rendering */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center items-center gap-2 mt-10">
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  setPagination(prev => ({ ...prev, page: 1 }));
                  fetchFoods({ page: 1 });
                }}
                disabled={pagination.page === 1 || loading}
              >
                First
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  const prevPage = pagination.page - 1;
                  setPagination(prev => ({ ...prev, page: prevPage }));
                  fetchFoods({ page: prevPage });
                }}
                disabled={pagination.page === 1 || loading}
              >
                Previous
              </Button>
              
              <span className="mx-2 text-sm ">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  const nextPage = pagination.page + 1;
                  setPagination(prev => ({ ...prev, page: nextPage }));
                  fetchFoods({ page: nextPage });
                }}
                disabled={pagination.page === pagination.totalPages || loading}
              >
                Next
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => {
                  setPagination(prev => ({ ...prev, page: pagination.totalPages }));
                  fetchFoods({ page: pagination.totalPages });
                }}
                disabled={pagination.page === pagination.totalPages || loading}
              >
                Last
              </Button>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12 max-w-md mx-auto">
          <Info className="h-12 w-12 mx-auto text-slate-300 mb-4" />
          <h3 className="text-xl font-medium mb-2">
            No foods found
          </h3>
          <p className="text-muted-foreground">
            No foods match your search criteria. Try adjusting your search term
            or category filter.
          </p>
          {searchTerm && (
            <Button
              variant="outline"
              className="mt-4"
              onClick={handleClearSearch}
            >
              Clear Search
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
