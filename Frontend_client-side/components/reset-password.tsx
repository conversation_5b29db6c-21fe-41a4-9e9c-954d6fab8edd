"use client"

import type React from "react"

import { useEffect, useState, type FormEvent } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { ArrowLeft, Lock, Eye, EyeOff, AlertCircle, Loader2, CheckCircle2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardDescription, CardTitle } from "@/components/ui/card"
import { heroData } from "@/data/HomeData/data"
import { authService } from "@/services/auth.service"

export default function ResetPassword() {
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formError, setFormError] = useState<string | null>(null)
  const [passwordStrength, setPasswordStrength] = useState(0)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get("token")
  const hero = heroData[0]
  
    const [currentTheme, setCurrentTheme] = useState<string>("light")
    
    useEffect(() => {
      // Function to get current theme
      const getTheme = () => {
        if (typeof window !== "undefined") {
          const storedTheme = localStorage.getItem("ui-theme")
          return storedTheme || "light"
        }
        return "light"
      }
      // Set initial theme
      setCurrentTheme(getTheme())
      
      // Create a MutationObserver to watch for theme class changes on <html>
      if (typeof window !== "undefined") {
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (
              mutation.type === "attributes" && 
              mutation.attributeName === "class"
            ) {
              // HTML class changed, check if dark mode is active
              const isDarkMode = document.documentElement.classList.contains("dark")
              setCurrentTheme(isDarkMode ? "dark" : "light")
            }
          })
        })
        
        // Start observing the document with the configured parameters
        observer.observe(document.documentElement, { attributes: true })
        
        // Also listen for storage events (for changes in other tabs)
        const handleStorageChange = () => {
          setCurrentTheme(getTheme())
        }
        window.addEventListener("storage", handleStorageChange)
        
        // Cleanup
        return () => {
          observer.disconnect()
          window.removeEventListener("storage", handleStorageChange)
        }
      }
    }, [])
    
    // Determine which logo to use based on theme
    const logoSrc = currentTheme === "dark" ? hero.logo2 : hero.logo1

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setPassword(value)

    // Simple password strength calculation
    let strength = 0
    if (value.length >= 8) strength += 1
    if (/[A-Z]/.test(value)) strength += 1
    if (/[0-9]/.test(value)) strength += 1
    if (/[^A-Za-z0-9]/.test(value)) strength += 1
    setPasswordStrength(strength)

    if (formError) setFormError(null)
  }

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setFormError(null)

    // Validate passwords match
    if (password !== confirmPassword) {
      setFormError("Passwords do not match")
      return
    }

    // Validate password strength
    if (passwordStrength < 3) {
      setFormError("Please create a stronger password")
      return
    }

    setIsLoading(true)

    try {
      // Here you would call your API to reset the password
      // For example: await authService.resetPassword(token, password)

      // Simulate API call
      if (!token) {
        setFormError("Invalid or expired reset link. Please request a new password reset.")
        setIsLoading(false)
        return
      }
      await authService.resetPassword({ token, newPassword: password })
      
      await new Promise((resolve) => setTimeout(resolve, 1500))

      setIsSubmitted(true)
    } catch (error: any) {
      console.error("Password reset error:", error)
      setFormError(error.response?.data?.message || "Failed to reset password. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-b from-background to-muted/30">
      <div className="w-full max-w-md animate-fade-in">
        <Card className="w-full shadow-lg overflow-hidden border-muted/20">
          <div className="p-6 md:p-8 space-y-6">
            <div className="flex justify-between items-center">
              <Link href="/" className="flex items-center space-x-2">
                <Image
                  src={logoSrc}
                  alt="NutriFocus Logo"
                  width={120}
                  height={40}
                  className="h-6 w-auto"
                />
              </Link>
              <Link
                href="/login"
                className="flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                <ArrowLeft className="mr-1 h-4 w-4" />
                Back to login
              </Link>
            </div>

            {!isSubmitted ? (
              <div className="space-y-6 animate-fade-in">
                <div className="space-y-2 text-center">
                  <CardTitle className="text-2xl font-bold">Reset your password</CardTitle>
                  <CardDescription>Create a new password for your account</CardDescription>
                </div>

                {!token && (
                  <div className="bg-amber-500/10 text-amber-700 dark:text-amber-400 text-sm p-3 rounded-md flex items-start">
                    <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                    <p>Invalid or expired reset link. Please request a new password reset.</p>
                  </div>
                )}

                {formError && (
                  <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md flex items-start">
                    <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                    <p>{formError}</p>
                  </div>
                )}

                <form className="space-y-4" onSubmit={handleSubmit}>
                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-sm font-medium">
                      New Password
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        value={password}
                        onChange={handlePasswordChange}
                        className="pl-10 pr-10"
                        placeholder="••••••••"
                        required
                        minLength={8}
                        disabled={!token}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="lg"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={!token}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <Eye className="h-4 w-4 text-muted-foreground" />
                        )}
                        <span className="sr-only">{showPassword ? "Hide password" : "Show password"}</span>
                      </Button>
                    </div>

                    {/* Password strength indicator */}
                    {password && (
                      <div className="mt-2">
                        <div className="flex gap-1 h-1">
                          {[...Array(4)].map((_, i) => (
                            <div
                              key={i}
                              className={`h-full flex-1 rounded-full transition-colors ${
                                i < passwordStrength
                                  ? passwordStrength === 1
                                    ? "bg-destructive/80"
                                    : passwordStrength === 2
                                      ? "bg-orange-500"
                                      : passwordStrength === 3
                                        ? "bg-yellow-500"
                                        : "bg-green-500"
                                  : "bg-muted"
                              }`}
                            />
                          ))}
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {passwordStrength === 0 && "Use 8+ characters with letters, numbers & symbols"}
                          {passwordStrength === 1 && "Weak - Add uppercase letters, numbers or symbols"}
                          {passwordStrength === 2 && "Fair - Add more variety"}
                          {passwordStrength === 3 && "Good - Almost there"}
                          {passwordStrength === 4 && "Strong password"}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword" className="text-sm font-medium">
                      Confirm Password
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                      <Input
                        id="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        value={confirmPassword}
                        onChange={(e) => {
                          setConfirmPassword(e.target.value)
                          if (formError) setFormError(null)
                        }}
                        className="pl-10 pr-10"
                        placeholder="••••••••"
                        required
                        disabled={!token}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="lg"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        disabled={!token}
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <Eye className="h-4 w-4 text-muted-foreground" />
                        )}
                        <span className="sr-only">{showConfirmPassword ? "Hide password" : "Show password"}</span>
                      </Button>
                    </div>
                    {password && confirmPassword && password !== confirmPassword && (
                      <p className="text-xs text-destructive mt-1">Passwords do not match</p>
                    )}
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    size="lg"
                    disabled={isLoading || !token || password !== confirmPassword}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Resetting password...
                      </>
                    ) : (
                      "Reset password"
                    )}
                  </Button>
                </form>
              </div>
            ) : (
              <div className="space-y-6 animate-fade-in text-center py-8">
                <div className="flex justify-center">
                  <div className="rounded-full bg-primary/10 p-3">
                    <CheckCircle2 className="h-9 w-9 text-green-500" />
                  </div>
                </div>
                <div className="space-y-2">
                  <CardTitle className="text-2xl font-bold">Password reset successful</CardTitle>
                  <CardDescription className="max-w-sm mx-auto">
                    Your password has been reset successfully. You can now log in with your new password.
                  </CardDescription>
                </div>
                <div className="pt-4">
                  <Button onClick={() => router.push("/login")} className="w-full sm:w-auto">
                    Go to login
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  )
}
