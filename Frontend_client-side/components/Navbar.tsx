'use client'

import { useEffect, useState } from 'react'
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Menu, X, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { cn } from "@/lib/utils";
import { heroData } from "@/data/HomeData/data";

// Updated navLinks with scroll targets for About and Features
const navLinks = [
  { href: "#home", label: "Home", isScroll: true },
  { href: "#about", label: "About Us", isScroll: true },
  { href: "#features", label: "Features", isScroll: true },
  { href: "#faq", label: "FAQ", isScroll: true },
  { href: "/Blog", label: "Blog", isScroll: false },

];

const excludedPaths = ["/login", "/signup", "/adminLogin", "/adminSignup"];

export default function Navbar() {
  const [menuOpen, setMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const pathname = usePathname();
  const [hideNavbar, setHideNavbar] = useState(false);
  const hero = heroData[0];
  const [currentTheme, setCurrentTheme] = useState<string>("light");
  const [activeSection, setActiveSection] = useState<string>("/");

  useEffect(() => {
    const shouldHideNavbar = excludedPaths.includes(pathname) || 
      pathname.startsWith("/userDashboard") || 
      pathname.startsWith("/admin") || 
      pathname.startsWith("/superAdmin") ||
      pathname.startsWith("/reset-password") ||
      pathname.startsWith("/forgot-password");
      
    setHideNavbar(shouldHideNavbar);
  }, [pathname]);

  // Function to handle smooth scrolling
  const handleNavClick = (e: React.MouseEvent<HTMLAnchorElement>, href: string, isScroll: boolean) => {
    // If we're on the homepage and it's a scroll link
    if (isScroll && pathname === "/") {
      e.preventDefault();
      const targetId = href.replace("#", "");
      const element = document.getElementById(targetId);
      
      if (element) {
        // Close mobile menu if open
        setMenuOpen(false);
        
        // Scroll to the element
        element.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
        
        // Update active section
        setActiveSection(href);
        
        // Update URL without page reload
        window.history.pushState({}, "", href);
      }
    } 
    // If we're NOT on the homepage but it's a scroll link
    else if (isScroll && pathname !== "/") {
      e.preventDefault();
      
      // Close mobile menu if open
      setMenuOpen(false);
      
      // Store the target section in sessionStorage to use after navigation
      const targetSection = href;
      sessionStorage.setItem("scrollTarget", targetSection);
      
      // Navigate to homepage
      window.location.href = "/";
    }
    // For non-scroll links, use normal navigation (no changes needed)
  };

  // Function to get current theme
  useEffect(() => {
    const getTheme = () => {
      if (typeof window !== "undefined") {
        const storedTheme = localStorage.getItem("ui-theme");
        return storedTheme || "light";
      }
      return "light";
    };
    
    // Set initial theme
    setCurrentTheme(getTheme());

    // Create a MutationObserver to watch for theme class changes on <html>
    if (typeof window !== "undefined") {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (
            mutation.type === "attributes" &&
            mutation.attributeName === "class"
          ) {
            // HTML class changed, check if dark mode is active
            const isDarkMode =
              document.documentElement.classList.contains("dark");
            setCurrentTheme(isDarkMode ? "dark" : "light");
          }
        });
      });

      // Start observing the document with the configured parameters
      observer.observe(document.documentElement, { attributes: true });

      // Also listen for storage events (for changes in other tabs)
      const handleStorageChange = () => {
        setCurrentTheme(getTheme());
      };
      window.addEventListener("storage", handleStorageChange);

      // Cleanup
      return () => {
        observer.disconnect();
        window.removeEventListener("storage", handleStorageChange);
      };
    }
  }, []);

  // Intersection Observer to detect which section is in view
  useEffect(() => {
    if (typeof window !== "undefined" && pathname === "/") {
      const observerOptions = {
        root: null,
        rootMargin: "-80px 0px -80% 0px", // Adjust based on your navbar height
        threshold: 0.1,
      };

      const observerCallback = (entries: IntersectionObserverEntry[]) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveSection(`#${entry.target.id}`);
            window.history.replaceState({}, "", `#${entry.target.id}`);
          }
        });
      };

      const observer = new IntersectionObserver(observerCallback, observerOptions);
      
      // Observe sections
      const sections = ["home", "about", "faq", "features"];
      sections.forEach((id) => {
        const element = document.getElementById(id);
        if (element) {
          observer.observe(element);
        }
      });

      return () => {
        sections.forEach((id) => {
          const element = document.getElementById(id);
          if (element) {
            observer.unobserve(element);
          }
        });
      };
    }
  }, [pathname]);

  const logoSrc = currentTheme === "dark" ? hero.logo2 : hero.logo1;

  // Handle scroll effect
  useEffect(() => {
    if (hideNavbar) return;

    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [hideNavbar]);

  // Close menu when clicking outside
  useEffect(() => {
    if (hideNavbar) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (
        menuOpen &&
        !target.closest(".mobile-menu") &&
        !target.closest(".menu-button")
      ) {
        setMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [menuOpen, hideNavbar]);

  // Close menu when route changes
  useEffect(() => {
    setMenuOpen(false);
  }, [pathname]);

  // Add a new useEffect to handle scrolling after navigation from another page
  useEffect(() => {
    if (pathname === "/") {
      // Check if we have a stored scroll target
      const scrollTarget = sessionStorage.getItem("scrollTarget");
      if (scrollTarget) {
        // Clear the stored target
        sessionStorage.removeItem("scrollTarget");
        
        // Wait for the page to fully load
        setTimeout(() => {
          const targetId = scrollTarget.replace("#", "");
          const element = document.getElementById(targetId);
          
          if (element) {
            element.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
            
            // Update active section
            setActiveSection(scrollTarget);
            
            // Update URL without page reload
            window.history.pushState({}, "", scrollTarget);
          }
        }, 100);
      }
    }
  }, [pathname]);

  if (hideNavbar) {
    return null;
  }

  // Check if we're on the homepage
  const isHomePage = pathname === "/";

  return (
    <header
      className={cn(
        "sticky top-0 z-40 w-full transition-all duration-300",
        scrolled ? "py-2 bg-background/80 backdrop-blur-md shadow-sm" : "py-4"
      )}
    >
      <div className="container mx-auto px-4">
        <nav className="flex items-center justify-between">
          {/* Logo */}
          <div className="relative w-[150px] h-[40px] lg:w-[160px] lg:h-[50px]">
            <Link href="/" className="flex items-center space-x-2">
              <Image
                src={logoSrc || "/placeholder.svg"}
                alt="NutriFocus Logo"
                width={100}
                height={30}
                className="h-10 w-auto"
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-12 md:space-x-7">
            {navLinks.map((link) => {
              // For scroll links on homepage, use the activeSection state
              // For other links, use the pathname
              const isActive = link.isScroll && isHomePage
                ? activeSection === link.href
                : pathname === link.href;
                
              return (
                <Link
                  key={link.href}
                  href={link.href}
                  className={cn(
                    "text-base transition-colors hover:text-foreground relative group",
                    isActive
                      ? "text-foreground font-medium"
                      : "text-muted-foreground"
                  )}
                  onClick={(e) => handleNavClick(e, link.href, link.isScroll)}
                >
                  {link.label}
                  <span
                    className={cn(
                      "absolute -bottom-1 left-0 h-0.5 bg-primary transition-all duration-300",
                      isActive ? "w-full" : "w-0 group-hover:w-full"
                    )}
                  />
                </Link>
              );
            })}
          </div>

          {/* Right Side Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <ThemeToggle />
            <Button asChild>
              <Link href="/signup">Get Started</Link>
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="flex items-center space-x-4 md:hidden">
            <ThemeToggle />
            <Button
              variant="ghost"
              size="icon"
              className="menu-button"
              onClick={() => setMenuOpen(true)}
              aria-label="Open menu"
            >
              <Menu className="h-6 w-6" />
            </Button>
          </div>
        </nav>
      </div>

      {/* Mobile Menu Overlay */}
      <div
        className={cn(
          "fixed inset-0 bg-background/80 backdrop-blur-sm z-50 transition-opacity duration-300 md:hidden",
          menuOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
      >
        {/* Mobile Menu Panel */}
        <div
          className={cn(
            "fixed right-0 top-0 h-full w-3/4 max-w-xs bg-background border-l shadow-xl p-6 transition-transform duration-300 ease-in-out mobile-menu",
            menuOpen ? "translate-x-0" : "translate-x-full"
          )}
        >
          <div className="flex items-center justify-between mb-8">
            <p className="font-semibold text-lg">Menu</p>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setMenuOpen(false)}
              aria-label="Close menu"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          <div className="space-y-6">
            {/* Mobile Navigation Links */}
            <div className="space-y-2">
              {navLinks.map((link) => {
                const isActive = link.isScroll && isHomePage
                  ? activeSection === link.href
                  : pathname === link.href;
                  
                return (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={cn(
                      "flex items-center justify-between py-2 px-3 rounded-md transition-colors",
                      isActive
                        ? "bg-muted font-medium text-foreground"
                        : "text-muted-foreground hover:bg-muted/50 hover:text-foreground"
                    )}
                    onClick={(e) => handleNavClick(e, link.href, link.isScroll)}
                  >
                    <span>{link.label}</span>
                    <ChevronRight className="h-4 w-4 opacity-70" />
                  </Link>
                );
              })}
            </div>

            <div className="pt-6 border-t">
              <Button className="w-full" asChild>
                <Link href="/signup">Get Started</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
