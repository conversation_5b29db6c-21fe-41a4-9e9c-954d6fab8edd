@import url('https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100..900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Base */
    --background: 0 0% 100%;
    --foreground: 0 0% 20%;
    --color-text: 0 0% 20%; /* Text for light mode */

    /* Custom Colors */
    --color-field: 0 0% 85%;
    --color-input: 0 0% 98%;
    --color-header: 260 33% 84%;
    --color-input-border: 0 0% 85%;
    --color-button: 0 0% 40%;
    --color-off-white: 0 0% 92%;
    --color-white-button: 0 0% 100%;
    --color-form-bg: 210 36% 76%;

    /* shadcn/ui colors */
    --card: 0 0% 100%;
    --card-foreground: 0 0% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 20%;
    --primary: 0 0% 40%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 20%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 20%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 0% 40%;

    /* Chart colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --radius: 0.5rem;
  }

  .dark {
    /* Base Dark Mode */
    --background: 220 13% 30%;
    --foreground: 0 0% 98%;
    --color-text: 0 0% 95%; /* Brighter text for dark mode */

    /* Custom Colors in Dark Mode */
    --color-field: 220 13% 25%;
    --color-input: 220 13% 25%;
    --color-header: 220 13% 35%;
    --color-input-border: 220 13% 35%;
    --color-button: 220 13% 60%; /* Brighter button color */
    --color-off-white: 220 13% 25%;
    --color-white-button: 220 13% 40%; /* Brighter button background */
    --color-form-bg: 220 13% 25%;

    /* shadcn/ui dark colors */
    --card: 220 13% 25%;
    --card-foreground: 0 0% 98%;
    --popover: 220 13% 25%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 80%; /* Brighter primary color */
    --primary-foreground: 220 13% 15%;
    --secondary: 220 13% 30%;
    --secondary-foreground: 0 0% 98%;
    --muted: 220 13% 25%;
    --muted-foreground: 0 0% 75%; /* Brighter muted text */
    --accent: 220 13% 35%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 70% 60%; /* Brighter destructive color */
    --destructive-foreground: 0 0% 98%;
    --border: 220 13% 40%;
    --input: 220 13% 30%;
    --ring: 0 0% 80%; /* Brighter ring color */

    /* Chart Colors Dark */
    --chart-1: 220 70% 60%; /* Brighter chart colors */
    --chart-2: 160 60% 55%;
    --chart-3: 30 80% 65%;
    --chart-4: 280 65% 70%;
    --chart-5: 340 75% 65%;
  }

  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-[hsl(var(--background))] text-[hsl(var(--color-text))];
  }
}

/* Add these utility classes to ensure text visibility in dark mode */
@layer utilities {
  .dark-text-fix {
    @apply dark:text-white text-black;
  }
  
  .dark-bg-contrast {
    @apply dark:bg-slate-800 bg-white;
  }
  
  /* For inputs that might have dark text on dark background */
  .dark-input-fix {
    @apply dark:text-white dark:bg-slate-700 dark:border-slate-600;
  }
}
