"use client";

import { Loader2 } from "lucide-react";
import React from "react";


interface LoadingPageProps {
  loading: boolean; // Prop to indicate loading state
}

const LoadingPage: React.FC<LoadingPageProps> = ({ loading }) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
            <p className="text-lg text-muted-foreground">
              Loading...
            </p>
          </div>
  );
};

export default LoadingPage;
