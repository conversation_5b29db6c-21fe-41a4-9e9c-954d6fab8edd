"use client";

import { useState, useEffect } from "react";
import { MessageSquare, CheckCircle2, Star, ChevronLeft, ChevronRight } from "lucide-react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { api } from "@/services/api";
import { toast } from "react-hot-toast";
import { Skeleton } from "@/components/ui/skeleton";

interface Feedback {
  feedbackId: string;
  userId: string;
  message: string;
  feedbackType: string;
  rating: number;
  adminResponse: string | null;
  createdAt: string;
  userEmail: string;
  status: string;
}

interface FeedbackResponse {
  data: Feedback[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
  };
}

export default function AdminFeedback() {
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [responses, setResponses] = useState<{ [key: string]: string }>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [status, setStatus] = useState<"all" | "Submitted" | "Reviewed" | "Resolved">("all");
  const itemsPerPage = 5;

  // Fetch feedback
  useEffect(() => {
    const fetchFeedback = async () => {
      try {
        setLoading(true);
        console.log(currentPage, itemsPerPage, status);
        const response = await api.get<FeedbackResponse>(
          `/feedback/all?page=${currentPage}&limit=${itemsPerPage}&status=${status}`,
        );
        console.log('Feedback response:', response);
        setFeedbacks(response.data.data);
        setTotalPages(response.data.meta.totalPages);
      } catch (err) {
        toast.error("Failed to load feedback.");
      } finally {
        setLoading(false);
      }
    };
    fetchFeedback();
  }, [currentPage, status]);

  const handleResponseChange = (feedbackId: string, value: string) => {
    setResponses((prev) => ({ ...prev, [feedbackId]: value }));
  };

  const handleSubmitResponse = async (feedbackId: string) => {
    try {
      const responseText = responses[feedbackId];
      if (!responseText?.trim()) {
        toast.error("Response cannot be empty.");
        return;
      }

      await api.post(`/feedback/${feedbackId}/respond`, {
        response: responseText,
      });

      setFeedbacks((prev) =>
        prev.map((f) =>
          f.feedbackId === feedbackId ? { ...f, adminResponse: responseText, status: "Reviewed" } : f,
        ),
      );
      setResponses((prev) => ({ ...prev, [feedbackId]: "" }));
      toast.success("Response submitted successfully.");
    } catch (err) {
      toast.error("Failed to submit response.");
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handleStatusChange = (value: "all" | "Submitted" | "Reviewed" | "Resolved") => {
    setStatus(value);
    setCurrentPage(1); // Reset to page 1 on status change
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 md:px-6">
        <div className="space-y-6">
          {[...Array(3)].map((_, idx) => (
            <Skeleton key={idx}>
              <div className="p-6 rounded-lg border bg-white space-y-4">
                <div className="flex justify-between items-center">
                  <div className="h-6 w-1/4 bg-gray-300 rounded"></div>
                  <div className="h-6 w-20 bg-gray-200 rounded"></div>
                </div>
                <div className="flex items-center space-x-2">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-4 w-4 bg-gray-200 rounded-full"></div>
                  ))}
                  <div className="h-4 w-12 bg-gray-200 rounded"></div>
                </div>
                <div className="h-4 w-5/6 bg-gray-300 rounded"></div>
                <div className="h-4 w-4/6 bg-gray-200 rounded"></div>
                <div className="h-10 w-32 bg-gray-300 rounded-md"></div>
              </div>
            </Skeleton>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <h1 className="text-3xl font-bold mb-6 flex items-center">
        <MessageSquare className="h-6 w-6 mr-2" />
        Feedback Management
      </h1>
      <div className="mb-6">
        <Label htmlFor="status-filter" className="mb-2 block">
          Filter by Status
        </Label>
        <Select value={status} onValueChange={handleStatusChange}>
          <SelectTrigger id="status-filter" className="w-[200px]">
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="Submitted">Submitted</SelectItem>
            <SelectItem value="Reviewed">Reviewed</SelectItem>
            <SelectItem value="Resolved">Resolved</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-6">
        {feedbacks.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <p>No feedback available.</p>
            </CardContent>
          </Card>
        ) : (
          feedbacks.map((feedback) => (
            <Card key={feedback.feedbackId}>
              <CardHeader>
                <CardTitle className="flex justify-between items-center">
                  <span className="capitalize">
                    {feedback.feedbackType} - {feedback.userEmail}
                  </span>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={feedback.status === "Resolved" ? "default" : "secondary"}
                      className={
                        feedback.status === "Resolved" 
                          ? "bg-green-500" 
                          : feedback.status === "Reviewed"
                          ? "bg-blue-500"
                          : "bg-yellow-500"
                      }
                    >
                      {feedback.status.charAt(0).toUpperCase() + feedback.status.slice(1)}
                    </Badge>
                    <Badge variant="outline">
                      {new Date(feedback.createdAt).toLocaleDateString()}
                    </Badge>
                  </div>
                </CardTitle>
                <CardDescription>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`inline h-4 w-4 mr-0.5 ${
                        feedback.rating >= star
                          ? "text-amber-500 fill-amber-500"
                          : "text-slate-300"
                      }`}
                    />
                  ))}
                  <span className="ml-2 text-sm text-slate-500">{feedback.rating}/5</span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="mb-4">{feedback.message}</p>
                {feedback.adminResponse ? (
                  <div className="p-4 bg-green-100 rounded-md">
                    <p className="font-semibold flex items-center">
                      <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                      Admin Response
                    </p>
                    <p>{feedback.adminResponse}</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Textarea
                      placeholder="Enter your response"
                      value={responses[feedback.feedbackId] || ""}
                      onChange={(e) => handleResponseChange(feedback.feedbackId, e.target.value)}
                      className="min-h-[100px]"
                    />
                    <Button onClick={() => handleSubmitResponse(feedback.feedbackId)}>
                      Submit Response
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
        {/* Pagination Controls */}
        {feedbacks.length > 0 && (
          <div className="flex justify-between items-center mt-4">
            <Button
              variant="outline"
              onClick={handlePreviousPage}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            <span className="text-sm">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}