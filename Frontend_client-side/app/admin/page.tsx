"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON>r<PERSON>heck, UserPlus, ShieldCheck, ArrowUpRight, CheckCircleIcon, ClockIcon, TrashIcon, BanIcon, XCircleIcon } from "lucide-react"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts"
import { api } from "@/services/api"
import { toast } from "react-hot-toast"
import { LoadingSpinner } from "@/components/loader/LoadingSpinner"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { useRouter } from "next/navigation"
import { UserStatus } from "@/services/user.service"

interface User {
  name: string
  email: string
  createdAt: string
  date: string
  status: UserStatus
}

interface DashboardStats {
  totalUsers: number
  activeUsers: number
  newUsersThisMonth: number
  totalAdmins: number
}

interface MonthlyData {
  name: string
  users: number
}

export default function AdminDashboard() {
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState([
    {
      title: "Total Users",
      value: "0",
      icon: Users,
      color: "bg-blue-500",
      trend: "+12%",
      trendUp: true,
    },
    {
      title: "Active Users",
      value: "0",
      icon: UserCheck,
      color: "bg-green-500",
      trend: "+8%",
      trendUp: true,
    },
    {
      title: "New Users This Month",
      value: "0",
      icon: UserPlus,
      color: "bg-purple-500",
      trend: "+24%",
      trendUp: true,
    },
    {
      title: "Total Admins",
      value: "0",
      icon: ShieldCheck,
      color: "bg-amber-500",
      trend: "+2%",
      trendUp: true,
    },
  ])
  const [users, setUsers] = useState<User[]>([])
  const [monthlyEngagementData, setMonthlyEngagementData] = useState<MonthlyData[]>([])
  const [activeEngagementData, setActiveEngagementData] = useState([
    { name: "Active Users", value: 0, color: "#3b82f6" },
    { name: "Inactive Users", value: 0, color: "#93c5fd" },
  ])
  const router = useRouter()
          const getStatusBadgeColor = (status: string) => {
            switch(status) {
              case UserStatus.ACTIVE:
                return "bg-green-100 text-green-800 border-green-200";
              case UserStatus.INACTIVE:
                return "bg-red-100 text-red-800 border-red-200";
              case UserStatus.SUSPENDED:
                return "bg-yellow-100 text-yellow-800 border-yellow-200";
              case UserStatus.PENDING:
                return "bg-blue-100 text-blue-800 border-blue-200";
              case UserStatus.DELETED:
                return "bg-gray-100 text-gray-800 border-gray-200";
              default:
                return "bg-slate-100 text-slate-800 border-slate-200";
            }
          };
        
          const getStatusIcon = (status: string) => {
            switch(status) {
              case UserStatus.ACTIVE:
                return <CheckCircleIcon className="h-4 w-4 mr-1" />;
              case UserStatus.INACTIVE:
                return <XCircleIcon className="h-4 w-4 mr-1" />;
              case UserStatus.SUSPENDED:
                return <BanIcon className="h-4 w-4 mr-1" />;
              case UserStatus.DELETED:
                return <TrashIcon className="h-4 w-4 mr-1" />;
              case UserStatus.PENDING:
                return <ClockIcon className="h-4 w-4 mr-1" />;
              case UserStatus.DELETED:
                return <TrashIcon className="h-4 w-4 mr-1" />;
              default:
                return null;
            }
          };
  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true)
      try {
        // Fetch dashboard stats
        const statsResponse = await api.get("/admin/dashboard/stats")
        const dashboardStats: DashboardStats = statsResponse.data

        // Update stats with real data
        setStats([
          {
            title: "Total Users",
            value: dashboardStats.totalUsers.toString(),
            icon: Users,
            color: "bg-blue-500",
            trend: "+12%",
            trendUp: true,
          },
          {
            title: "Active Users",
            value: dashboardStats.activeUsers.toString(),
            icon: UserCheck,
            color: "bg-green-500",
            trend: "+8%",
            trendUp: true,
          },
          {
            title: "New Users This Month",
            value: dashboardStats.newUsersThisMonth.toString(),
            icon: UserPlus,
            color: "bg-purple-500",
            trend: "+24%",
            trendUp: true,
          },
          {
            title: "Total Admins",
            value: dashboardStats.totalAdmins.toString(),
            icon: ShieldCheck,
            color: "bg-amber-500",
            trend: "+2%",
            trendUp: true,
          },
        ])

        // Fetch monthly user engagement data
        const monthlyDataResponse = await api.get("/admin/dashboard/monthly-users")
        setMonthlyEngagementData(monthlyDataResponse.data)

        // Fetch active vs inactive users data
        const activeUsersPercent = Math.round((dashboardStats.activeUsers / dashboardStats.totalUsers) * 100) || 0
        const inactiveUsersPercent = 100 - activeUsersPercent

        setActiveEngagementData([
          { name: "Active Users", value: activeUsersPercent, color: "#3b82f6" },
          { name: "Inactive Users", value: inactiveUsersPercent, color: "#93c5fd" },
        ])

        // Fetch recent users
        const usersResponse = await api.get("/users/recent")
        console.log(usersResponse);
        setUsers(
          usersResponse.data.map((user: User) => ({
            name: user.name,
            email: user.email,
            date: new Date(user.createdAt).toLocaleDateString(),
            status: user.status,
          })),
        )
      } catch (error) {
        console.error("Error fetching dashboard data:", error)
        toast.error("Failed to load dashboard data")
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  // Custom tooltip for bar chart
  const CustomBarTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-card p-2 border rounded-md shadow-sm text-xs">
          <p className="font-medium">{`${label}`}</p>
          <p className="text-blue-600">{`Users: ${payload[0].value}`}</p>
        </div>
      )
    }
    return null
  }

  if (isLoading) {
    return <LoadingSpinner />
  }

  return (
    <div className="space-y-6 p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Admin Dashboard</h1>
        <Button variant="outline" size="lg">
          Download Report
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <Card key={index} className="overflow-hidden border-none shadow-md">
            <CardContent className="p-6">
              <div className="flex justify-between items-center">
                <div className={`${stat.color} text-white p-3 rounded-lg`}>
                  <stat.icon className="h-5 w-5" />
                </div>
                <div
                  className={`text-xs font-medium flex items-center gap-1 ${stat.trendUp ? "text-green-600" : "text-red-600"}`}
                >
                  {stat.trend}
                  <ArrowUpRight className={`h-3 w-3 ${!stat.trendUp && "rotate-180"}`} />
                </div>
              </div>
              <div className="mt-3">
                <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                <h3 className="text-2xl font-bold mt-1">{stat.value}</h3>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-none shadow-md">
          <CardHeader className="pb-2">
            <CardTitle>Monthly User Growth</CardTitle>
            <CardDescription>New user registrations per month</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[fit]">
              <ChartContainer
                config={{
                  users: {
                    label: "Users",
                    color: "hsl(var(--chart-1))",
                  },
                }}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={monthlyEngagementData} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
                    <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="hsl(var(--border))" />
                    <XAxis dataKey="name" axisLine={false} tickLine={false} tick={{ fontSize: 12 }} dy={10} />
                    <YAxis axisLine={false} tickLine={false} tick={{ fontSize: 12 }} dx={-10} />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar dataKey="users" fill="hsl(var(--chart-1))" radius={[4, 4, 0, 0]} maxBarSize={50} />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="border-none shadow-md">
          <CardHeader className="pb-2">
            <CardTitle>User Status Distribution</CardTitle>
            <CardDescription>Active vs. inactive user accounts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={activeEngagementData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={90}
                    paddingAngle={5}
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    labelLine={false}
                  >
                    {activeEngagementData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} stroke="transparent" />
                    ))}
                  </Pie>
                  <RechartsTooltip
                    formatter={(value: any) => [`${value}%`, "Percentage"]}
                    contentStyle={{
                      backgroundColor: "hsl(var(--card))",
                      border: "1px solid hsl(var(--border))",
                      borderRadius: "0.375rem",
                      boxShadow: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* User Table */}
      <Card className="border-none shadow-md">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recently Registered Users</CardTitle>
              <CardDescription>New users who joined the platform</CardDescription>
            </div>
            <Button onClick={() => router.push('/admin/users')} variant="outline" size="lg">
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.length > 0 ? (
                users.map((user, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{user.name}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>{user.date}</TableCell>
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={`flex items-center w-fit ${getStatusBadgeColor(user.status)}`}
                      >
                        {getStatusIcon(user.status)}
                        {(user.status ? user.status.charAt(0).toUpperCase() + user.status.slice(1) : "")}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center">
                    No users found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
