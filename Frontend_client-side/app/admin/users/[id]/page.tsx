// File: app/edit-user/[id]/page.tsx
"use client";
import { useParams, useRouter } from "next/navigation";

export default function EditUserPage() {
  const { id } = useParams();
  const router = useRouter();

  const handleUpdate = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Replace with real API call to update user
    alert(`User ${id} updated successfully!`);
    router.push("/manage-users");
  };

  return (
    <div className="max-w-lg mx-auto p-6 bg-white rounded shadow">
      <h1 className="text-2xl font-semibold mb-4">Edit User ID: {id}</h1>
      <form onSubmit={handleUpdate} className="space-y-4">
        <input className="w-full p-2 border rounded" defaultValue="User Name" />
        <input className="w-full p-2 border rounded" type="date" />
        <select className="w-full p-2 border rounded">
          <option value="Active">Active</option>
          <option value="Inactive">Inactive</option>
        </select>
        <div className="flex justify-between">
          <button
            type="submit"
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Save
          </button>
          <button
            type="button"
            className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400"
            onClick={() => router.back()}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}
