"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, ImageIcon, Save, Trash2 } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { blogService, BlogPost } from "@/services/blog.service";

// Blog categories
const BLOG_CATEGORIES = ["Health", "Lifestyle", "Fitness", "Nutrition"];

export default function EditBlog() {
  const params = useParams();
  const router = useRouter();
  const blogId = params.id as string;

  const [blog, setBlog] = useState<BlogPost | null>(null);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [category, setCategory] = useState("");
  const [image, setImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [errors, setErrors] = useState<{ title?: string; content?: string; category?: string }>({});

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        setIsLoading(true);
        const blogData = await blogService.getBlogById(blogId);
        setBlog(blogData);
        setTitle(blogData.title);
        setContent(blogData.content || "");
        setCategory(blogData.category || "");
        setImagePreview(blogData.featuredImage || null);
      } catch (error) {
        console.error("Error fetching blog:", error);
        toast.error("Failed to load blog data");
      } finally {
        setIsLoading(false);
      }
    };

    if (blogId) {
      fetchBlog();
    }
  }, [blogId]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImage(file);
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageReset = () => {
    setImage(null);
    setImagePreview(null);
  };

  const validateForm = () => {
    const newErrors: { title?: string; content?: string; category?: string } = {};
    if (!title.trim()) newErrors.title = "Title is required";
    if (!content.trim()) newErrors.content = "Content is required";
    if (!category) newErrors.category = "Category is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      setIsUpdating(true);
      const formData = new FormData();
      formData.append("title", title);
      formData.append("content", content);
      formData.append("category", category);
      if (image) {
        formData.append("featuredImage", image);
      }
      await blogService.updateBlog(blogId, formData);
      toast.success("Blog post updated successfully");
      router.push("/admin/blogs");
    } catch (error) {
      console.error("Error updating blog:", error);
      toast.error("Failed to update blog post");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDelete = async () => {
    try {
      setIsUpdating(true);
      await blogService.deleteBlog(blogId);
      toast.success("Blog post deleted successfully");
      router.push("/admin/blogs");
    } catch (error) {
      console.error("Error deleting blog:", error);
      toast.error("Failed to delete blog post");
    } finally {
      setIsUpdating(false);
      setDeleteDialogOpen(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4 md:px-6 bg-background">
        <div className="max-w-6xl mx-auto space-y-6">
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-96 w-full rounded-md" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 bg-background">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link
              href="/admin/blogs"
              className="mr-4 text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-2xl font-semibold text-foreground">
              Edit Blog Post
            </h1>
          </div>
          <div className="flex space-x-3">
            <Button
              type="button"
              onClick={() => setDeleteDialogOpen(true)}
              disabled={isLoading || isUpdating}
              className="bg-destructive hover:bg-destructive/90 text-destructive-foreground flex items-center"
            >
              <Trash2 className="h-5 w-5 mr-2" />
              Delete
            </Button>
            <Button
              type="submit"
              form="edit-blog-form"
              disabled={isLoading || isUpdating}
              className="bg-primary hover:bg-primary/90 text-primary-foreground flex items-center"
            >
              <Save className="h-5 w-5 mr-2" />
              {isUpdating ? "Updating..." : "Update Post"}
            </Button>
          </div>
        </div>

        <form id="edit-blog-form" onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-background rounded-md shadow-sm p-6 border border-border">
            <div className="mb-6">
              <Label
                htmlFor="title"
                className="block text-sm font-medium text-foreground mb-1"
              >
                Post Title
              </Label>
              <Input
                type="text"
                id="title"
                value={title}
                onChange={(e) => {
                  setTitle(e.target.value);
                  setErrors((prev) => ({ ...prev, title: undefined }));
                }}
                className={`w-full border-border text-foreground placeholder-muted-foreground ${errors.title ? "border-destructive" : ""}`}
                placeholder="Enter post title"
                required
                disabled={isLoading || isUpdating}
              />
              {errors.title && (
                <p className="text-sm text-destructive mt-1">{errors.title}</p>
              )}
            </div>

            <div className="mb-6">
              <Label
                htmlFor="category"
                className="block text-sm font-medium text-foreground mb-1"
              >
                Category
              </Label>
              <Select
                value={category}
                onValueChange={(value) => {
                  setCategory(value);
                  setErrors((prev) => ({ ...prev, category: undefined }));
                }}
                required
                disabled={isLoading || isUpdating}
              >
                <SelectTrigger
                  className={`w-full border-border text-foreground ${errors.category ? "border-destructive" : ""}`}
                >
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {BLOG_CATEGORIES.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-sm text-destructive mt-1">{errors.category}</p>
              )}
            </div>

            <div className="mb-6">
              <Label
                htmlFor="image"
                className="block text-sm font-medium text-foreground mb-1"
              >
                Featured Image
              </Label>
              <div className="flex items-center space-x-4">
                <label className="flex items-center justify-center w-40 h-40 border-2 border-border border-dashed rounded-md cursor-pointer hover:bg-muted/50">
                  {imagePreview ? (
                    <Image
                      src={imagePreview}
                      alt="Preview"
                      width={160}
                      height={160}
                      className="w-full h-full object-cover rounded-md"
                    />
                  ) : (
                    <div className="space-y-1 text-center">
                      <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground" />
                      <div className="text-sm text-muted-foreground">
                        <span className="font-medium text-primary hover:text-primary/80">
                          Upload an image
                        </span>
                      </div>
                    </div>
                  )}
                  <input
                    id="image"
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleImageChange}
                    disabled={isLoading || isUpdating}
                  />
                </label>
                <div className="flex flex-col space-y-2">
                  <div className="text-sm text-muted-foreground">
                    Recommended size: 1200 x 630 pixels
                  </div>
                  {imagePreview && (
                    <Button
                      type="button"
                      variant="outline"
                      size="lg"
                      onClick={handleImageReset}
                      className="border-border text-foreground hover:bg-muted/50"
                      disabled={isLoading || isUpdating}
                    >
                      Remove Image
                    </Button>
                  )}
                </div>
              </div>
            </div>

            <div>
              <Label
                htmlFor="content"
                className="block text-sm font-medium text-foreground mb-1"
              >
                Content
              </Label>
              <Textarea
                id="content"
                value={content}
                onChange={(e) => {
                  setContent(e.target.value);
                  setErrors((prev) => ({ ...prev, content: undefined }));
                }}
                rows={12}
                className={`w-full border-border text-foreground placeholder-muted-foreground ${errors.content ? "border-destructive" : ""}`}
                placeholder="Write your blog post content here..."
                required
                disabled={isLoading || isUpdating}
              />
              {errors.content && (
                <p className="text-sm text-destructive mt-1">{errors.content}</p>
              )}
            </div>
          </div>
        </form>

        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Blog Post</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this blog post? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isLoading || isUpdating}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                className="bg-destructive hover:bg-destructive/90 text-destructive-foreground"
                onClick={handleDelete}
                disabled={isLoading || isUpdating}
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}