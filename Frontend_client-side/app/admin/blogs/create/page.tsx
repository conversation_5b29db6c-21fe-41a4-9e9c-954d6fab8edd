"use client";

import { useState } from "react";
import { <PERSON><PERSON>ef<PERSON>, ImageIcon, Save } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { blogService } from "@/services/blog.service";

// Blog categories
const BLOG_CATEGORIES = ["Health", "Lifestyle", "Fitness", "Nutrition"];

export default function CreateBlog() {
  const router = useRouter();
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [category, setCategory] = useState("");
  const [image, setImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ title?: string; content?: string; category?: string }>({});

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImage(file);
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageReset = () => {
    setImage(null);
    setImagePreview(null);
  };

  const validateForm = () => {
    const newErrors: { title?: string; content?: string; category?: string } = {};
    if (!title.trim()) newErrors.title = "Title is required";
    if (!content.trim()) newErrors.content = "Content is required";
    if (!category) newErrors.category = "Category is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      setIsLoading(true);

      await blogService.createBlog({
        title,
        content,
        category,
        featuredImage: image || undefined,
      });
      toast.success("Blog post created successfully");
      router.push("/admin/blogs");
    } catch (error: any) {
      console.error("Error creating blog:", error);
      toast.error(error.message?.[0] || "Failed to create blog post");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 bg-background dark:bg-background">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link
              href="/admin/blogs"
              className="mr-4 text-muted-foreground dark:text-muted-foreground hover:text-foreground dark:hover:text-foreground"
            >
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-2xl font-semibold text-foreground dark:text-foreground">
              Create New Blog Post
            </h1>
          </div>
          <Button
            type="submit"
            form="create-blog-form"
            disabled={isLoading}
            className="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white flex items-center"
          >
            <Save className="h-5 w-5 mr-2" />
            {isLoading ? "Creating..." : "Publish Post"}
          </Button>
        </div>

        <form id="create-blog-form" onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-background dark:bg-background rounded-md shadow-sm p-6 border border-border dark:border-border">
            <div className="mb-6">
              <Label
                htmlFor="title"
                className="block text-sm font-medium text-foreground dark:text-foreground mb-1"
              >
                Post Title
              </Label>
              <Input
                type="text"
                id="title"
                value={title}
                onChange={(e) => {
                  setTitle(e.target.value);
                  setErrors((prev) => ({ ...prev, title: undefined }));
                }}
                className={`w-full border-border dark:border-border text-foreground dark:text-foreground placeholder-muted-foreground dark:placeholder-muted-foreground ${errors.title ? "border-destructive" : ""}`}
                placeholder="Enter post title"
                required
                disabled={isLoading}
              />
              {errors.title && (
                <p className="text-sm text-destructive dark:text-destructive mt-1">{errors.title}</p>
              )}
            </div>

            <div className="mb-6">
              <Label
                htmlFor="category"
                className="block text-sm font-medium text-foreground dark:text-foreground mb-1"
              >
                Category
              </Label>
              <Select
                value={category}
                onValueChange={(value) => {
                  setCategory(value);
                  setErrors((prev) => ({ ...prev, category: undefined }));
                }}
                required
                disabled={isLoading}
              >
                <SelectTrigger
                  className={`w-full border-border dark:border-border text-foreground dark:text-foreground ${errors.category ? "border-destructive" : ""}`}
                >
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {BLOG_CATEGORIES.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && (
                <p className="text-sm text-destructive dark:text-destructive mt-1">{errors.category}</p>
              )}
            </div>

            <div className="mb-6">
              <Label
                htmlFor="image"
                className="block text-sm font-medium text-foreground dark:text-foreground mb-1"
              >
                Featured Image
              </Label>
              <div className="flex items-center space-x-4">
                <label className="flex items-center justify-center w-40 h-40 border-2 border-border dark:border-border border-dashed rounded-md cursor-pointer hover:bg-muted/50 dark:hover:bg-muted/50">
                  {imagePreview ? (
                    <Image
                      src={imagePreview}
                      alt="Preview"
                      width={160}
                      height={160}
                      className="w-full h-full object-cover rounded-md"
                    />
                  ) : (
                    <div className="space-y-1 text-center">
                      <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground dark:text-muted-foreground" />
                      <div className="text-sm text-muted-foreground dark:text-muted-foreground">
                        <span className="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                          Upload an image
                        </span>
                      </div>
                    </div>
                  )}
                  <input
                    id="image"
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleImageChange}
                    disabled={isLoading}
                  />
                </label>
                <div className="flex flex-col space-y-2">
                  <div className="text-sm text-muted-foreground dark:text-muted-foreground">
                    Recommended size: 1200 x 630 pixels
                  </div>
                  {imagePreview && (
                    <Button
                      type="button"
                      variant="outline"
                      size="lg"
                      onClick={handleImageReset}
                      className="border-border dark:border-border text-foreground dark:text-foreground hover:bg-muted/50 dark:hover:bg-muted/50"
                      disabled={isLoading}
                    >
                      Remove Image
                    </Button>
                  )}
                </div>
              </div>
            </div>

            <div>
              <Label
                htmlFor="content"
                className="block text-sm font-medium text-foreground dark:text-foreground mb-1"
              >
                Content
              </Label>
              <Textarea
                id="content"
                value={content}
                onChange={(e) => {
                  setContent(e.target.value);
                  setErrors((prev) => ({ ...prev, content: undefined }));
                }}
                rows={12}
                className={`w-full border-border dark:border-border text-foreground dark:text-foreground placeholder-muted-foreground dark:placeholder-muted-foreground ${errors.content ? "border-destructive" : ""}`}
                placeholder="Write your blog post content here..."
                required
                disabled={isLoading}
              />
              {errors.content && (
                <p className="text-sm text-destructive dark:text-destructive mt-1">{errors.content}</p>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}