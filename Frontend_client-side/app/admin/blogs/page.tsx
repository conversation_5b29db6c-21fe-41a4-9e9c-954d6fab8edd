"use client";

import { <PERSON>cilIcon, TrashIcon, PlusCircleIcon, ChevronLeftIcon, ChevronRightIcon, SearchIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { blogService, BlogPost, BlogQueryParams } from "@/services/blog.service";
import { format } from "date-fns";

// Blog categories
const BLOG_CATEGORIES = [
  "All",
  "Health",
  "Lifestyle",
  "Fitness",
  "Nutrition",
];

export default function ManageBlogs() {
  const router = useRouter();
  const [blogs, setBlogs] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("All");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  // Dialog states
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedBlog, setSelectedBlog] = useState<BlogPost | null>(null);

  // Fetch blogs
  const fetchBlogs = async (params: BlogQueryParams = {}) => {
    try {
      setLoading(true);
      const queryParams: BlogQueryParams = {
        page: pagination.page,
        limit: pagination.limit,
        search: searchQuery || undefined,
        category: selectedCategory === "All" ? undefined : selectedCategory,
        ...params,
      };

      const response = await blogService.getMyBlogs(queryParams);
      setBlogs(Array.isArray(response.data) ? response.data : []);
      setPagination({
        page: response.meta?.page || 1,
        limit: response.meta?.limit || pagination.limit,
        total: response.meta?.total || 0,
        totalPages: response.meta?.totalPages || 0,
      });

      setError(null);
    } catch (err) {
      console.error("Error fetching blogs:", err);
      setError("Failed to load blogs. Please try again later.");
      toast.error("Failed to load blogs");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBlogs();
  }, []);

  // Handle search
  const handleSearch = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchBlogs({ page: 1 });
  };

  // Handle category change
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchBlogs({
      page: 1,
      category: category === "All" ? undefined : category,
    });
  };

  // Navigate to edit page
  const handleEdit = (blog: BlogPost) => {
    router.push(`/admin/blogs/edit/${blog.blogId}`);
  };

  // Delete blog
  const handleDelete = (blog: BlogPost) => {
    setSelectedBlog(blog);
    setDeleteDialogOpen(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedBlog) return;

    try {
      await blogService.deleteBlog(selectedBlog.blogId);
      toast.success("Blog deleted successfully");
      setDeleteDialogOpen(false);
      fetchBlogs();
    } catch (error) {
      console.error("Error deleting blog:", error);
      toast.error("Failed to delete blog");
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  // Pagination
  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= pagination.totalPages) {
      setPagination((prev) => ({ ...prev, page: newPage }));
      fetchBlogs({ page: newPage });
    }
  };

  if (loading && blogs.length === 0) {
    return (
      <div className="max-w-6xl mx-auto space-y-6">
        <Skeleton className="h-12 w-full bg-muted dark:bg-muted/50" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-64 w-full bg-muted dark:bg-muted/50" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 bg-background dark:bg-background">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-semibold text-foreground dark:text-foreground">
            Manage Blog Posts
          </h1>
          <Link href="/admin/blogs/create">
            <Button className="h-11 bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800">
              <PlusCircleIcon className="h-5 w-5 mr-2" />
              Create New Post
            </Button>
          </Link>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Input
              type="text"
              placeholder="Search blogs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border-border dark:border-border text-foreground dark:text-foreground placeholder-muted-foreground dark:placeholder-muted-foreground"
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
            />
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground dark:text-muted-foreground" />
          </div>
          <div className="w-full sm:w-48">
            <Select value={selectedCategory} onValueChange={handleCategoryChange}>
              <SelectTrigger className="border-border dark:border-border text-foreground dark:text-foreground">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                {BLOG_CATEGORIES.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button
            onClick={handleSearch}
            className="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800"
          >
            Search
          </Button>
        </div>

        {error && blogs.length === 0 ? (
          <div className="text-center py-10 text-destructive dark:text-destructive">
            {error}
          </div>
        ) : blogs.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-muted-foreground dark:text-muted-foreground">
              No blog posts found.
            </p>
            <Link href="/admin/blogs/create">
              <Button className="mt-4 bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800">
                Create your first blog post
              </Button>
            </Link>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {blogs.map((blog) => (
                <div
                  key={blog.blogId}
                  className="bg-background dark:bg-background rounded-lg overflow-hidden shadow-sm border border-border dark:border-border hover:shadow-md transition-shadow"
                >
                  <div className="relative h-48">
                    <Image
                      src={blog.featuredImage || "/blog-placeholder.png"}
                      alt={blog.title}
                      fill
                      className="object-cover"
                    />
                    {blog.category && (
                      <span className="absolute top-2 right-2 bg-blue-600 dark:bg-blue-700 text-white dark:text-white text-xs px-2 py-1 rounded">
                        {blog.category}
                      </span>
                    )}
                  </div>
                  <div className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h2 className="text-lg font-semibold text-foreground dark:text-foreground line-clamp-2">
                        {blog.title}
                      </h2>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="lg"
                          className="text-blue-600 dark:text-blue-400 border-border dark:border-border hover:bg-blue-50 dark:hover:bg-blue-900/50"
                          onClick={() => handleEdit(blog)}
                        >
                          <PencilIcon className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="lg"
                          className="text-red-600 dark:text-red-400 border-border dark:border-border hover:bg-red-50 dark:hover:bg-red-900/50"
                          onClick={() => handleDelete(blog)}
                        >
                          <TrashIcon className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground dark:text-muted-foreground mb-2">
                      {formatDate(blog.createdAt)}
                    </p>
                    <p className="text-muted-foreground dark:text-muted-foreground line-clamp-3">
                      {blog.content?.substring(0, 150) || "No content"}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex justify-center items-center gap-2 mt-8">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => handlePageChange(1)}
                  disabled={pagination.page === 1 || loading}
                  className="border-border dark:border-border text-foreground dark:text-foreground hover:bg-muted/50 dark:hover:bg-muted/50"
                >
                  First
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1 || loading}
                  className="border-border dark:border-border text-foreground dark:text-foreground hover:bg-muted/50 dark:hover:bg-muted/50"
                >
                  <ChevronLeftIcon className="h-4 w-4 mr-1" />
                  Previous
                </Button>
                <span className="mx-2 text-sm text-muted-foreground dark:text-muted-foreground">
                  Page {pagination.page} of {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.totalPages || loading}
                  className="border-border dark:border-border text-foreground dark:text-foreground hover:bg-muted/50 dark:hover:bg-muted/50"
                >
                  Next
                  <ChevronRightIcon className="h-4 w-4 ml-1" />
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => handlePageChange(pagination.totalPages)}
                  disabled={pagination.page === pagination.totalPages || loading}
                  className="border-border dark:border-border text-foreground dark:text-foreground hover:bg-muted/50 dark:hover:bg-muted/50"
                >
                  Last
                </Button>
              </div>
            )}
          </>
        )}

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent className="bg-background dark:bg-background border-border dark:border-border">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-foreground dark:text-foreground">
                Are you absolutely sure?
              </AlertDialogTitle>
              <AlertDialogDescription className="text-muted-foreground dark:text-muted-foreground">
                This action will permanently delete this blog post. This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            {selectedBlog && (
              <div className="mt-2 p-3 bg-muted dark:bg-muted/50 rounded-md">
                <div className="font-medium mb-1 text-foreground dark:text-foreground">
                  {selectedBlog.title}
                </div>
                <div className="text-sm text-muted-foreground dark:text-muted-foreground line-clamp-2">
                  {selectedBlog.content?.substring(0, 150)}...
                </div>
              </div>
            )}
            <AlertDialogFooter>
              <AlertDialogCancel className="border-border dark:border-border text-foreground dark:text-foreground hover:bg-muted/50 dark:hover:bg-muted/50">
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                className="bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}