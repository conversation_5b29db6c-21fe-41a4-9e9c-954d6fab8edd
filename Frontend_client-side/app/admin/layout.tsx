import type React from "react"
import type { Metadata } from "next"
import AdminSidebar from "@/components/admin/admin-sidebar"
import AdminHeader from "@/components/admin/admin-header"
import AdminFooter from "@/components/admin/admin-footer"

export const metadata: Metadata = {
  title: "NutriFocus Admin",
  description: "Admin Dashboard for NutriFocus",
}

export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="min-h-screen bg-[#CCC9DA]">
      <div className="flex h-screen overflow-hidden">
        <div className="z-50">
          <AdminSidebar />
        </div>

        <div className="flex-1 flex flex-col h-screen">
          <div className="fixed top-0 left-0 md:left-60 right-0 h-16 z-40 bg-white shadow-md">
            <AdminHeader />
          </div>
          <main className="flex-1 overflow-y-auto p-4 pt-40 md:pt-24">
            {children}
          </main>
          <div className="bg-white shadow-inner">
            <AdminFooter />
          </div>
        </div>
      </div>
    </div>
  );
}
