"use client";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { getAdminById, updateUser } from "@/services/superAdmin.service";

export default function EditUserPage() {
  const params = useParams();
  const id = params.id as string; // Type assertion to string
  const router = useRouter();
  const [user, setUser] = useState({
    name: "",
    email: "",
    status: "Active",
  });

  useEffect(() => {
    const fetchUser = async () => {
      if (!id) return; // Guard clause to prevent calling with undefined
      
      try {
        const data = await getAdminById(id);
        setUser({
          name: data.name,
          email: data.email,
          status: data.role, // Assuming role represents status
        });
      } catch (error) {
        console.error("Error fetching user:", error);
        alert("Failed to fetch user data.");
      }
    };

    fetchUser();
  }, [id]);

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id) return; // Guard clause to prevent calling with undefined
    
    try {
      await updateUser(id, user);
      alert("User updated successfully!");
      router.push("/superAdmin/admins"); // Fixed: redirect to admins page instead of users
    } catch (error) {
      console.error("Error updating user:", error);
      alert("Failed to update user. Please try again.");
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setUser((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div className="max-w-lg mx-auto p-6 bg-white rounded shadow">
      <h1 className="text-2xl font-semibold mb-4">Edit Admin ID: {id}</h1>
      <form onSubmit={handleUpdate} className="space-y-4">
        <input
          name="name"
          className="w-full p-2 border rounded"
          value={user.name}
          onChange={handleChange}
          placeholder="Admin Name"
          required
        />
        <input
          name="email"
          className="w-full p-2 border rounded"
          type="email"
          value={user.email}
          onChange={handleChange}
          placeholder="Email"
          required
        />
        <select
          name="status"
          className="w-full p-2 border rounded"
          value={user.status}
          onChange={handleChange}
          required
        >
          <option value="Active">Active</option>
          <option value="Inactive">Inactive</option>
          <option value="Suspended">Suspended</option>
          <option value="Pending">Pending</option>
          <option value="Deleted">Deleted</option>
        </select>
        <div className="flex justify-between">
          <button
            type="submit"
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Save
          </button>
          <button
            type="button"
            className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400"
            onClick={() => router.back()}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}
