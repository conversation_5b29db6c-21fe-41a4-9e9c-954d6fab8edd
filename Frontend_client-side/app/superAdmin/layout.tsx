import type React from "react"
import type { Metadata } from "next"
import DashboardHeader from "@/components/supperAdmin/sAdmin-header"
import AdminFooter from "@/components/admin/admin-footer"
import SuperAdminSideBar from "@/components/supperAdmin/sAdmin-sidebar"

export const metadata: Metadata = {
  title: "NutriFocus Admin",
  description: "SuperAdmin Dashboard for NutriFocus",
}

export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="min-h-screen bg-[#CCC9DA]">
      <div className="flex h-screen overflow-hidden">
        <div className="z-50">
          <SuperAdminSideBar />
        </div>

        <div className="flex-1 flex flex-col h-screen">
          <div className="fixed top-0 left-0 md:left-60 right-0 h-16 z-40 bg-white shadow-md">
            <DashboardHeader />
          </div>
          <main className="flex-1 overflow-y-auto p-4 pt-40 md:pt-24">
            {children}
          </main>
          <div className="bg-white shadow-inner">
            <AdminFooter />
          </div>
        </div>
      </div>
    </div>
  );
}
