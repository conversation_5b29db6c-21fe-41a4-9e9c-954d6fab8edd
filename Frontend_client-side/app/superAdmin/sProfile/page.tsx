"use client";
import type React from "react";
import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { Camera, ArrowLeft, User } from "lucide-react";
import Link from "next/link";
import { toast } from "react-hot-toast";
import { superAdminService, SuperAdminProfile } from "@/services/superAdmin.service";
import { useTheme } from "@/components/theme-provider";

export default function ProfilePage() {
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [userData, setUserData] = useState<Partial<SuperAdminProfile>>({
    name: "",
    email: "",
  });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { theme } = useTheme();

  // Fetch user data from API
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        setIsPageLoading(true);
        const response = await superAdminService.getSuperAdminProfile();
        // Update state with user data
        setUserData({
          name: response.name,
          email: response.email,
        });
        
        // Set profile image if available
        if (response.profilePicture) {
          setProfileImage(response.profilePicture);
        }
      } catch (error) {
        console.error("Failed to fetch superAdmin profile:", error);
        toast.error("Failed to load profile data");
      } finally {
        setIsPageLoading(false);
      }
    };

    fetchUserProfile();
  }, []);

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setIsLoading(true);
      const response = await superAdminService.uploadProfileImage(file);
      setProfileImage(response.profilePicture);
      toast.success('Profile image updated successfully');
    } catch (error) {
      console.error('Failed to upload profile image:', error);
      toast.error('Failed to upload profile image');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUserData({
      ...userData,
      [name]: value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      // Send updated profile data to server
      await superAdminService.updateProfile({
        name: userData.name,
        location: userData.location,
        phone: userData.phone,
        status: userData.status,
        email: userData.email,
        profilePicture: profileImage,
        
      });
      
      toast.success("Profile updated successfully!");
    } catch (error) {
      console.error("Failed to update profile:", error);
      toast.error("Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  if (isPageLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <>
      <main className={`pt-[80px] pb-16 ${theme === 'dark' ? 'bg-[hsl(var(--background))]' : 'bg-gray-50'}`}>
        <div className="max-w-3xl mx-auto px-4 sm:px-6">
          <div className="flex items-center mb-6">
            <Link
              href="/admin"
              className={`mr-4 ${theme === 'dark' ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`}
            >
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className={`text-2xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
              Your Profile
            </h1>
          </div>

          <div className={`${theme === 'dark' ? 'bg-[hsl(var(--card))] border border-[hsl(var(--border))]' : 'bg-white'} rounded-lg shadow-sm overflow-hidden`}>
            <div className="h-32 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
            <div className="px-6 pb-6">
              <div className="flex justify-center -mt-16 mb-6">
                <div
                  className="relative w-32 h-32 rounded-full overflow-hidden border-4 border-white cursor-pointer"
                  onClick={handleImageClick}
                >
                  {profileImage ? (
                    <Image
                      src={profileImage}
                      alt="Profile"
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className={`w-full h-full flex items-center justify-center ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
                      <User className={`h-16 w-16 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-400'}`} />
                    </div>
                  )}
                  <div className="absolute bottom-0 right-0 bg-gray-800 p-1.5 rounded-full">
                    <Camera className="h-4 w-4 text-white" />
                  </div>
                </div>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleImageChange}
                  accept="image/*"
                  className="hidden"
                />
              </div>

              <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label
                      htmlFor="name"
                      className={`block text-sm font-medium ${theme === 'dark' ? 'text-green-400' : 'text-green-500'} mb-1`}
                    >
                      Full Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={userData.name}
                      onChange={handleInputChange}
                      className={`w-full p-2 border ${theme === 'dark' ? 'bg-[hsl(var(--input))] border-[hsl(var(--border))] text-white' : 'border-gray-300'} rounded-md focus:ring-blue-500 focus:border-blue-500`}
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="email"
                      className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-900'} mb-1`}
                    >
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={userData.email}
                      disabled
                      className={`w-full p-2 border ${theme === 'dark' ? 'bg-[hsl(var(--muted))] border-[hsl(var(--border))] text-gray-400' : 'border-gray-300 bg-gray-50'} rounded-md`}
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? "Saving..." : "Save Changes"}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
