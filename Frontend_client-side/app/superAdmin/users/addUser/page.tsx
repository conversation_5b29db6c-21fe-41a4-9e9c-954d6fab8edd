"use client";
import { userService } from "@/services/user.service";
import { useRouter } from "next/navigation";

export default function AddUserPage() {
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const userData = {
      name: formData.get("name") as string,
      email: formData.get("email") as string,
      password: formData.get("password") as string,
      status: formData.get("status") as string,
    };

    try {
      await userService.createUser(userData);
      alert("User added successfully!");
      router.push("/superAdmin/users");
    } catch (error) {
      console.error("Error adding user:", error);
      alert("Failed to add user. Please try again.");
    }
  };

  return (
    <div className="max-w-lg mx-auto p-6 bg-white rounded shadow">
      <h1 className="text-2xl font-semibold mb-4">Add New User</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <input
          name="name"
          className="w-full p-2 border rounded"
          placeholder="User name"
          required
        />
        <input
          name="email"
          className="w-full p-2 border rounded"
          type="email"
          placeholder="Email"
          required
        />
        <input
          name="password"
          className="w-full p-2 border rounded"
          type="password"
          placeholder="Password"
          required
        />
        <select name="status" className="w-full p-2 border rounded" required>
          <option value="">Status</option>
          <option value="Active">Active</option>
          <option value="Inactive">Inactive</option>
          <option value="Suspended">Suspended</option>
          <option value="Pending">Pending</option>
          <option value="Deleted">Deleted</option>
        </select>
        <div className="flex justify-between">
          <button
            type="submit"
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Submit
          </button>
          <button
            type="button"
            className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400"
            onClick={() => router.back()}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}