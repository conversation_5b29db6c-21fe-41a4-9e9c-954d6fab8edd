"use client"

import Link from "next/link"
import { Info, ArrowRight} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function Recommendation() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl md:text-2xl font-bold uppercase tracking-wider">
            Personalized Dietary Recommendations
          </CardTitle>
        </CardHeader>
      </Card>

      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row items-start gap-4">
            <div className="flex-shrink-0 text-primary mx-auto md:mx-0">
              <Info className="w-8 h-8 md:w-12 md:h-12" />
            </div>
            <div className="text-center md:text-left">
              <h2 className="text-lg md:text-xl font-semibold">
                Enhance Your Recommendations: Personalized Setup Guide
              </h2>
              <p className="text-muted-foreground mt-1 text-sm md:text-base">
                To receive tailored dietary recommendations that align with{" "}
                <span className="font-semibold">your health goals</span> and{" "}
                <span className="font-semibold">preferences</span>, follow these{" "}
                <span className="font-semibold">steps</span> to fill in your
                personal details accurately:
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mt-6 md:mt-8">
            <Card className="relative">
              <Badge 
                className="absolute top-2 right-2 md:top-4 md:right-4 h-6 w-6 md:h-8 md:w-8 rounded-full flex items-center justify-center text-sm md:text-base font-bold p-0"
                variant="default"
              > 1
              </Badge>
              <CardHeader className="pb-2">
                <CardTitle className="text-base md:text-lg">Provide Your Details</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm md:text-base list-disc list-inside text-muted-foreground">
                  <li>
                    <span className="font-medium">Input Fields:</span> Fill in all
                    required fields with precision. The fields include:
                    <ul className="pl-4 md:pl-5 mt-1 space-y-1 list-disc list-inside">
                      <li>Age</li>
                      <li>Weight & Height</li>
                      <li>Health Issues: diabetes, allergies, blood pressure</li>
                      <li>
                        Health Goals: Maintain current weight, Build muscle, Reduce
                        fat, Slim down
                      </li>
                      <li>Dietary Preferences: vegetarian, low carb</li>
                    </ul>
                  </li>
                  <li className="mt-2 md:mt-4">
                    <span className="font-medium">Interaction:</span> After
                    completing all fields, click the Next button to proceed.
                  </li>
                </ul>
              </CardContent>
            </Card>
            
            <Card className="relative">
              <Badge 
                className="absolute top-2 right-2 md:top-4 md:right-4 h-6 w-6 md:h-8 md:w-8 rounded-full flex items-center justify-center text-sm md:text-base font-bold p-0"
                variant="default"
              >
                2
              </Badge>
              <CardHeader className="pb-2">
                <CardTitle className="text-base md:text-lg">Verify Your Information</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm md:text-base list-disc list-inside text-muted-foreground">
                  <li>
                    <span className="font-medium">Review Your Input:</span>
                    <ul className="pl-4 md:pl-5 mt-1 space-y-1 list-disc list-inside">
                      <li>
                        A summary of your entered data is displayed in a clean,
                        easy-to-read format.
                      </li>
                    </ul>
                  </li>
                  <li className="mt-1 md:mt-2">
                    <span className="font-medium">User Action:</span>
                    <ul className="pl-4 md:pl-5 mt-1 space-y-1 list-disc list-inside">
                      <li>
                        Confirm that all details are correct by clicking the Confirm
                        button.
                      </li>
                      <li>
                        If corrections are needed, click Edit to modify specific
                        fields.
                      </li>
                    </ul>
                  </li>
                  <li className="mt-1 md:mt-2">
                    <span className="font-medium">Visual Feedback:</span>
                    <ul className="pl-4 md:pl-5 mt-1 space-y-1 list-disc list-inside">
                      <li>
                        Use checkmarks or progress indicators to show which sections
                        are complete.
                      </li>
                    </ul>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="relative col-span-1 md:col-span-2">
              <Badge 
                className="absolute top-2 right-2 md:top-4 md:right-4 h-6 w-6 md:h-8 md:w-8 rounded-full flex items-center justify-center text-sm md:text-base font-bold p-0"
                variant="default"
              >
                3
              </Badge>
              <CardHeader className="pb-2">
                <CardTitle className="text-base md:text-lg">Generate Your Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 list-disc list-inside text-sm md:text-base text-muted-foreground">
                  <li className="text-start">
                    <span className="font-medium">Final Action:</span>
                    <ul className="pl-4 md:pl-5 mt-1 space-y-1 list-disc list-inside text-start">
                      <li>
                        Once all inputs are verified, press the Generate
                        Recommendations button.
                      </li>
                      <li>
                        The system uses your data to generate a list of tailored
                        dietary suggestions and insights.
                      </li>
                    </ul>
                  </li>
                  <li className="text-start mt-2 md:mt-0">
                    <span className="font-medium">Processing Feedback:</span>
                    <ul className="pl-4 md:pl-5 mt-1 space-y-1 list-disc list-inside text-start">
                      <li>
                        A loading animation is displayed while the AI processes your
                        data.
                      </li>
                      <li>
                        Success message: "Your personalized recommendations are ready!"
                      </li>
                    </ul>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6 md:mt-8 flex justify-start">
            <Button asChild size="lg">
              <Link href="/userDashboard/recommendation/step1">
                Get Started
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
