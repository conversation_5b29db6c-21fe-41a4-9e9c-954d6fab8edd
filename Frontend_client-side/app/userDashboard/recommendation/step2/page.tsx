"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { Loader2, <PERSON><PERSON><PERSON>ircle, Edit, Wand2 } from "lucide-react";
import ProgressSteps from "@/components/user/progress-steps";
import { useRecommendationStore } from "@/store/recommendationStore";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { LoadingSpinner } from "@/components/loader/LoadingSpinner";

export default function RecommendationStep2() {
  const router = useRouter();
  const { healthMetrics, fetchPrediction, isLoading, error } = useRecommendationStore();

  useEffect(() => {
    // Try to load from localStorage if healthMetrics is null
    if (!healthMetrics && typeof window !== "undefined") {
      const savedMetrics = localStorage.getItem("healthMetrics");
      if (savedMetrics) {
        try {
          const parsedMetrics = JSON.parse(savedMetrics);
          // Ensure health_issues is an array
          if (!parsedMetrics.health_issues || !Array.isArray(parsedMetrics.health_issues)) {
            parsedMetrics.health_issues = [];
          }
          useRecommendationStore.getState().setHealthMetrics(parsedMetrics);
        } catch (e) {
          console.error("Failed to parse stored health metrics:", e);
          toast.error("No data provided. Please start over.");
          router.push("/userDashboard/recommendation/step1");
        }
      } else {
        toast.error("No data provided. Please start over.");
        router.push("/userDashboard/recommendation/step1");
      }
    }
  }, [healthMetrics, router]);

  const handleGenerate = async () => {
    try {
      await fetchPrediction();
      toast.success("Recommendation generated!");
      router.push("/userDashboard/recommendation/step3");
    } catch (error) {
      console.error("Error generating recommendation:", error);
      toast.error("Failed to generate recommendation");
    }
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-6 space-y-6">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <Button onClick={() => router.push("/userDashboard/recommendation/step1")}>
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!healthMetrics) return null;

  return (
    <div className="flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl shadow-lg">
        <CardHeader>
          <ProgressSteps currentStep={2} showBackButton backUrl="/userDashboard/recommendation/step1" />
          <CardTitle className="text-3xl font-bold text-center mt-6">Verify Your Information</CardTitle>
          <CardDescription className="text-center">
            Please review your details before generating your meal plan.
          </CardDescription>
          <Separator className="mt-4" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold border-b pb-2">Personal Information</h3>
                <div className="mt-4 space-y-3">
                  <p className="text-sm">
                    <span className="font-medium">Age:</span> {healthMetrics.age}
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">Weight:</span> {healthMetrics.weight} kg
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">Height:</span> {healthMetrics.height} cm
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">Gender:</span> {healthMetrics.gender}
                  </p>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold border-b pb-2">Activity Level</h3>
                <p className="mt-4 text-sm">{healthMetrics.activity_level}</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold border-b pb-2">Location</h3>
                <p className="mt-4 text-sm">{healthMetrics.location}</p>
              </div>
            </div>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold border-b pb-2">Dietary Preference</h3>
                <p className="mt-4 text-sm">{healthMetrics.dietary_preference}</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold border-b pb-2">Weight Goal</h3>
                <p className="mt-4 text-sm">{healthMetrics.weight_goal}</p>
              </div>
              <div>
                <h3 className="text-lg font-semibold border-b pb-2">Health Issues</h3>
                <p className="mt-4 text-sm">
                  {healthMetrics.health_issues?.length
                    ? healthMetrics.health_issues.join(", ")
                    : "None"}
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold border-b pb-2">Fasting</h3>
                <p className="mt-4 text-sm">{healthMetrics.fasting}</p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex gap-4">
          <Button
            variant="outline"
            className="flex-1"
            onClick={() => {
              router.push("/userDashboard/recommendation/step1");
            }}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button className="flex-1" onClick={handleGenerate} disabled={isLoading}>
            {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Wand2 className="mr-2 h-4 w-4" />}
            Generate
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}