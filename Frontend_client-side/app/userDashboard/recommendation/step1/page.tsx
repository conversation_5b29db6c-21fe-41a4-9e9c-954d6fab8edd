"use client";

import type React from "react";

import { useEffect, useState, useRef, useMemo } from "react";
import { useRouter } from "next/navigation";
import { ChevronDown, Loader2 } from "lucide-react";
import { toast, Toaster } from "react-hot-toast";
import ProgressSteps from "@/components/user/progress-steps";
import { useRecommendationStore } from "@/store/recommendationStore";
import { HealthMetricsSchema } from "@/utils/validation";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { LoadingSpinner } from "@/components/loader/LoadingSpinner";
import { userService } from "@/services/user.service";

// Define the ValidInputs type to match FALLBACK_VALID_INPUTS
interface ValidInputs {
  height: any;
  age: any;
  weight: any;
  genders: string[];
  activity_levels: string[];
  dietary_preferences: string[];
  weight_goals: string[];
  health_issues: string[];
  fasting_statuses: string[];
  supported_locations: string[];
}

const FALLBACK_VALID_INPUTS: ValidInputs = {
  height: { min: 150, max: 200 },
  age: { min: 18, max: 79 },
  weight: { min: 48, max: 119 },
  genders: ["Male", "Female"],
  activity_levels: ["Sedentary", "Lightly Active", "Moderately Active", "Very Active"],
  dietary_preferences: ["Omnivore", "Vegetarian", "Vegan"],
  weight_goals: ["Lose Weight", "Maintain Weight", "Gain Weight"],
  health_issues: ["None", "Diabetes", "Hypertension", "Heart Disease", "Kidney Disease", "Acne"],
  fasting_statuses: ["Yes", "No"],
  supported_locations: [
    "afar",
    "asossa/berta",
    "asossa/komo",
    "asossa/mao",
    "bale",
    "bench and maji zone/bench",
    "bench and maji zone/menit",
    "bench and maji zone/sheko",
    "borena",
    "dorze",
    "e. wellega",
    "e.hararge/jarso",
    "e.hararge/kersa",
    "gambella/agnuak",
    "gambella/nuer",
    "gamo",
    "gedeo",
    "gonder/negede weyto",
    "guji",
    "gurage/cheha",
    "harari",
    "keficho zone/keficho",
    "kemache zone/gumuz",
    "kembata",
    "konsso",
    "metekel/gumuz",
    "metekel/shinasha",
    "n. gonder/dembia",
    "n. omo/konta",
    "sidama",
    "tigray/central zone",
    "tigray/eastern zone",
    "tigray/kunama",
    "tigray/mekele",
    "tigray/shire",
    "tigray/southern zone",
    "urban/common",
    "w. wellega",
    "welayita",
    "addis ababa",
    "dire dawa",
    "adama",
    "gondar",
    "mekelle",
    "hawassa",
    "bahir dar",
    "jimma",
    "debre markos"
  ],
};

// Custom MultiSelect component using shadcn/ui components
const MultiSelect = ({
  options,
  selected,
  onChange,
  placeholder,
}: {
  options: { value: string; label: string }[];
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleOption = (value: string) => {
    if (!value) return;
    
    if (value === "None") {
      if (selected.includes("None")) {
        // If "None" is already selected, deselect it
        onChange([]);
      } else {
        // If "None" is being selected, deselect all other options and select only "None"
        onChange(["None"]);
      }
      return;
    }
    
    // Handle other options
    if (selected.includes("None")) {
      // If "None" is currently selected and we're selecting another option,
      // remove "None" and add the new option
      const newSelected = selected.filter((v) => v !== "None");
      if (newSelected.includes(value)) {
        // If the option is already selected, remove it
        onChange(newSelected.filter((v) => v !== value));
      } else {
        // If the option is not selected, add it
        onChange([...newSelected, value]);
      }
    } else {
      // Normal toggle behavior when "None" is not selected
      const newSelected = selected.includes(value)
        ? selected.filter((v) => v !== value)
        : [...selected, value];
      onChange(newSelected);
    }
  };

  const handleButtonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  const handleOptionClick = (e: React.MouseEvent, value: string) => {
    e.preventDefault();
    e.stopPropagation();
    toggleOption(value);
  };

  const handleCheckboxChange = (e: React.MouseEvent, value: string) => {
    e.preventDefault();
    e.stopPropagation();
    toggleOption(value);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        variant="outline"
        role="combobox"
        aria-expanded={isOpen}
        className="w-full justify-between"
        onClick={handleButtonClick}
        type="button"
      >
        {selected.length > 0
          ? selected
              .map(
                (value) =>
                  options.find((option) => option.value === value)?.label
              )
              .filter(Boolean)
              .join(", ")
          : placeholder}
        <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </Button>
      {isOpen && (
        <Card className="absolute z-50 w-full mt-1">
          <CardContent className="p-2 max-h-60 overflow-auto">
            {options.map((option) => (
              <div
                key={option.value}
                className={`flex items-center space-x-2 p-2`}
                onClick={(e) => e.stopPropagation()}
              >
                <Checkbox
                  id={`option-${option.value.toLowerCase()}`}
                  checked={selected.includes(option.value)}
                  onCheckedChange={() => toggleOption(option.value)}
                  onClick={(e) => handleCheckboxChange(e, option.value)}
                />
                <Label
                  htmlFor={`option-${option.value.toLowerCase()}`}
                  className="flex-grow cursor-pointer"
                  onClick={(e) => handleOptionClick(e, option.value)}
                >
                  {option.label}
                  {option.value === "None"}
                </Label>
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default function RecommendationStep1() {
  const router = useRouter();
  const { setHealthMetrics, healthMetrics: storeHealthMetrics, fetchValidInputs, validInputs, isLoading, error } = useRecommendationStore();
  
  const [formData, setFormData] = useState({
    age: "",
    weight: "",
    height: "",
    gender: "",
    activity_level: "",
    dietary_preference: "",
    weight_goal: "",
    health_issues: [] as string[],
    fasting: "No",
    location: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const hasAttemptedFetch = useRef(false);
  const hasInitialized = useRef(false);

  // Initialize form with data from store if available - only run once
  useEffect(() => {
    if (hasInitialized.current) return;
    hasInitialized.current = true;
    
    console.log("Initializing form with storeHealthMetrics:", storeHealthMetrics);
    
    const fetchUserHealthMetrics = async () => {
      try {
        const userProfile:any = await userService.getUserProfile();
        console.log("User profile fetched:", userProfile);
        
        if (userProfile?.healthMetrics) {
          console.log("Setting form data from profile healthMetrics:", userProfile.healthMetrics);
          const metrics = userProfile.healthMetrics;
          const formDataToSet = {
            age: metrics.age?.toString() || "",
            gender: metrics.gender || "",
            height: metrics.height?.toString() || "",
            weight: metrics.weight?.toString() || "",
            activity_level: metrics.activityLevel || "",
            dietary_preference: metrics.dietaryPreference || "",
            weight_goal: metrics.goals || "",
            health_issues: Array.isArray(metrics.healthIssues)
              ? metrics.healthIssues
              : metrics.healthIssues
              ? [metrics.healthIssues]
              : [],
            fasting: metrics.fasting || "No",
            location: metrics.location || "",
          };
          
          setFormData(formDataToSet);
          
          // Update store with the fetched health metrics
          setHealthMetrics({
            age: Number(metrics.age),
            weight: Number(metrics.weight),
            height: Number(metrics.height),
            gender: metrics.gender,
            activity_level: metrics.activityLevel,
            dietary_preference: metrics.dietaryPreference,
            weight_goal: metrics.goals,
            health_issues: Array.isArray(metrics.healthIssues)
              ? metrics.healthIssues
              : metrics.healthIssues
              ? [metrics.healthIssues]
              : [],
            fasting: metrics.fasting || "No",
            location: metrics.location,
          });
        } else if (storeHealthMetrics && Object.values(storeHealthMetrics).some(value => value !== "" && value !== 0)) {
          // Only use store values if they're not empty
          console.log("Using non-empty store values:", storeHealthMetrics);
          setFormData({
            age: storeHealthMetrics.age?.toString() || "",
            gender: storeHealthMetrics.gender || "",
            height: storeHealthMetrics.height?.toString() || "",
            weight: storeHealthMetrics.weight?.toString() || "",
            activity_level: storeHealthMetrics.activity_level || "",
            dietary_preference: storeHealthMetrics.dietary_preference || "",
            weight_goal: storeHealthMetrics.weight_goal || "",
            health_issues: Array.isArray(storeHealthMetrics.health_issues)
              ? storeHealthMetrics.health_issues
              : storeHealthMetrics.health_issues
              ? [storeHealthMetrics.health_issues]
              : [],
            fasting: storeHealthMetrics.fasting || "No",
            location: storeHealthMetrics.location || "",
          });
        } else {
          console.log("No health metrics found, using empty form");
          setFormData({
            age: "",
            gender: "",
            height: "",
            weight: "",
            activity_level: "",
            dietary_preference: "",
            weight_goal: "",
            health_issues: [],
            fasting: "No",
            location: "",
          });
        }
      } catch (error) {
        console.error("Error fetching user health metrics:", error);
        // Only use store values if there's an error and store has non-empty values
        if (storeHealthMetrics && Object.values(storeHealthMetrics).some(value => value !== "" && value !== 0)) {
          setFormData({
            age: storeHealthMetrics.age?.toString() || "",
            gender: storeHealthMetrics.gender || "",
            height: storeHealthMetrics.height?.toString() || "",
            weight: storeHealthMetrics.weight?.toString() || "",
            activity_level: storeHealthMetrics.activity_level || "",
            dietary_preference: storeHealthMetrics.dietary_preference || "",
            weight_goal: storeHealthMetrics.weight_goal || "",
            health_issues: Array.isArray(storeHealthMetrics.health_issues)
              ? storeHealthMetrics.health_issues
              : storeHealthMetrics.health_issues
              ? [storeHealthMetrics.health_issues]
              : [],
            fasting: storeHealthMetrics.fasting || "No",
            location: storeHealthMetrics.location || "",
          });
        } else {
          setFormData({
            age: "",
            gender: "",
            height: "",
            weight: "",
            activity_level: "",
            dietary_preference: "",
            weight_goal: "",
            health_issues: [],
            fasting: "No",
            location: "",
          });
        }
      }
    };

    fetchUserHealthMetrics();
  }, []); // Remove storeHealthMetrics from dependencies to prevent infinite loop

  // Fetch valid inputs when component mounts
  useEffect(() => {
    const getValidInputs = async () => {
      if (!validInputs && !isLoading && !hasAttemptedFetch.current) {
        hasAttemptedFetch.current = true;
        try {
          console.log('Attempting to fetch valid inputs...');
          await fetchValidInputs();
          console.log('Valid inputs fetched successfully:', validInputs);
        } catch (err) {
          console.error('Failed to fetch valid inputs:', err);
        }
      }
    };
    
    getValidInputs();
  }, [validInputs, isLoading, fetchValidInputs]);

  // Log whenever validInputs changes
  useEffect(() => {
    console.log('Current validInputs state:', validInputs);
  }, [validInputs]);

  // Use fallback values if validInputs is not available yet
  const effectiveValidInputs = useMemo(() => {
    console.log('Computing effectiveValidInputs with:', validInputs);
    return validInputs || FALLBACK_VALID_INPUTS;
  }, [validInputs]);

  // Helper function to safely get array values
  const getArrayValues = (source: any, key: string, fallback: string[]) => {
    console.log(`Getting array values for ${key}:`, { source, key, fallback });
    if (!source) return fallback;
    const values = source[key];
    console.log(`Values for ${key}:`, values);
    if (Array.isArray(values)) return values;
    return fallback;
  };

  // Debug activity level specifically
  console.log('Activity Level Options:', {
    validInputs: validInputs?.activity_levels,
    fallback: FALLBACK_VALID_INPUTS.activity_levels,
    effective: effectiveValidInputs.activity_levels
  });

  const validateNumericField = (name: keyof ValidInputs, value: string) => {
    if (!value) return `${name} is required`;
    const num = Number.parseFloat(value);
    if (isNaN(num)) return `${name} must be a number`;
    const constraints = Array.isArray(effectiveValidInputs[name]) 
      ? undefined 
      : effectiveValidInputs[name] as { min: number; max: number } | undefined;
    if (constraints && constraints.min !== undefined && num < constraints.min) {
      return `${name} must be at least ${constraints.min}`;
    }
    if (constraints && constraints.max !== undefined && num > constraints.max) {
      return `${name} must be at most ${constraints.max}`;
    }
    return "";
  };

  const handleNumericChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    const error = validateNumericField(name as keyof ValidInputs, value);
    setErrors((prev) => ({ ...prev, [name]: error }));
  };

const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  setErrors({});
  setIsSubmitting(true);

  console.log("Submitting formData:", formData);

  try {
    const data = {
      age: Number.parseInt(formData.age),
      weight: Number.parseFloat(formData.weight),
      height: Number.parseFloat(formData.height),
      gender: formData.gender,
      activity_level: formData.activity_level, // Changed from activityLevel
      dietary_preference: formData.dietary_preference, // Changed from dietaryPreference
      weight_goal: formData.weight_goal, // Changed from weightGoal
      health_issues: formData.health_issues, // Changed from healthIssues
      fasting: formData.fasting || "No",
      location: formData.location,
    };

    const parsed = HealthMetricsSchema.safeParse(data);
    if (!parsed.success) {
      console.log(parsed.error.issues);
      const fieldErrors: Record<string, string> = {};
      parsed.error.issues.forEach((issue) => {
        fieldErrors[issue.path[0]] = issue.message;
      });
      setErrors(fieldErrors);
      toast.error("Please correct the form errors");
      return;
    }

    console.log("Parsed data:", parsed.data);
    
    // Save to local store and localStorage
    setHealthMetrics(parsed.data);
    localStorage.setItem('healthMetrics', JSON.stringify(parsed.data));

    // Save to backend database
    try {
      await userService.updateHealthMetricsOnBoarding({
        age: parsed.data.age,
        weight: parsed.data.weight,
        height: parsed.data.height,
        gender: parsed.data.gender,
        activity_level: parsed.data.activity_level,
        dietary_preference: parsed.data.dietary_preference,
        weight_goal: parsed.data.weight_goal,
        health_issues: parsed.data.health_issues,
        fasting: parsed.data.fasting,
        location: parsed.data.location,
      });
      console.log("Health metrics saved to backend successfully");
    } catch (backendError) {
      console.error("Failed to save health metrics to backend:", backendError);
      // Don't fail the entire submission if backend save fails
      // The data is already saved locally
    }

    toast.success("Data saved successfully!");
    router.push("/userDashboard/recommendation/step2");
  } catch (error) {
    toast.error("Failed to save data");
    setErrors({ submit: "Failed to save data" });
  } finally {
    setIsSubmitting(false);
  }
};

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error && !validInputs) {
    return (
      <div className="flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-6 space-y-6">
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <Button
              onClick={() => {
                fetchValidInputs();
              }}
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const healthIssuesOptions = effectiveValidInputs.health_issues
    .filter((v) => v)
    .map((v) => ({ value: v, label: v }));

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl shadow-lg">
        <CardHeader>
          <ProgressSteps currentStep={1} />
          <CardTitle className="text-3xl font-bold text-center mt-6">
            Tell Us About You
          </CardTitle>
          <CardDescription className="text-center">
            Help us personalize your meal plan by sharing a few details.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="age">Age</Label>
                  <Input
                    id="age"
                    type="number"
                    name="age"
                    value={formData.age}
                    onChange={handleNumericChange}
                    placeholder="Enter age (18-79)"
                    min={effectiveValidInputs?.age?.min || 18}
                    max={effectiveValidInputs?.age?.max || 79}
                    aria-describedby={errors.age ? "age-error" : undefined}
                  />
                  {errors.age && (
                    <p id="age-error" className="text-sm text-destructive">
                      {errors.age}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="weight">Weight (kg)</Label>
                  <Input
                    id="weight"
                    type="number"
                    name="weight"
                    value={formData.weight}
                    onChange={handleNumericChange}
                    placeholder="Enter weight (48-119)"
                    min={effectiveValidInputs?.weight?.min || 48}
                    max={effectiveValidInputs?.weight?.max || 119}
                    step="0.1"
                    aria-describedby={
                      errors.weight ? "weight-error" : undefined
                    }
                  />
                  {errors.weight && (
                    <p id="weight-error" className="text-sm text-destructive">
                      {errors.weight}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="height">Height (cm)</Label>
                  <Input
                    id="height"
                    type="number"
                    name="height"
                    value={formData.height}
                    onChange={handleNumericChange}
                    placeholder="Enter height (150-200)"
                    min={effectiveValidInputs?.height?.min || 150}
                    max={effectiveValidInputs?.height?.max || 200}
                    step="0.1"
                    aria-describedby={
                      errors.height ? "height-error" : undefined
                    }
                  />
                  {errors.height && (
                    <p id="height-error" className="text-sm text-destructive">
                      {errors.height}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gender">Gender</Label>
                  <Select
                    value={formData.gender}
                    onValueChange={(value) => {
                      setFormData({ ...formData, gender: value });
                      setErrors({ ...errors, gender: "" });
                    }}
                  >
                    <SelectTrigger id="gender">
                      <SelectValue placeholder="Select Gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
  {getArrayValues(effectiveValidInputs, 'genders', FALLBACK_VALID_INPUTS.genders).map((gender) => (
    <SelectItem key={gender} value={gender}>
      {gender}
    </SelectItem>
  ))}
</SelectGroup>
                    </SelectContent>
                  </Select>
                  {errors.gender && (
                    <p className="text-sm text-destructive">{errors.gender}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="activity_level">Activity Level</Label>
                  <Select
                    value={formData.activity_level}
                    onValueChange={(value) => {
                      console.log('Activity level changed to:', value);
                      setFormData({ ...formData, activity_level: value });
                      setErrors({ ...errors, activity_level: "" });
                    }}
                  >
                    <SelectTrigger id="activity_level">
                      <SelectValue placeholder="Select Activity Level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
  {getArrayValues(effectiveValidInputs, 'activity_levels', FALLBACK_VALID_INPUTS.activity_levels).map((level) => (
    <SelectItem key={level} value={level}>
      {level}
    </SelectItem>
  ))}
</SelectGroup>
                    </SelectContent>
                  </Select>
                  {errors.activity_level && (
                    <p className="text-sm text-destructive">
                      {errors.activity_level}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="dietary_preference">Dietary Preference</Label>
                  <Select
                    value={formData.dietary_preference}
                    onValueChange={(value) => {
                      setFormData({ ...formData, dietary_preference: value });
                      setErrors({ ...errors, dietary_preference: "" });
                    }}
                  >
                    <SelectTrigger id="dietary_preference">
                      <SelectValue placeholder="Select Dietary Preference" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
  {getArrayValues(effectiveValidInputs, 'dietary_preferences', FALLBACK_VALID_INPUTS.dietary_preferences).map((pref) => (
    <SelectItem key={pref} value={pref}>
      {pref}
    </SelectItem>
  ))}
</SelectGroup>
                    </SelectContent>
                  </Select>
                  {errors.dietary_preference && (
                    <p className="text-sm text-destructive">
                      {errors.dietary_preference}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="weight_goal">Weight Goal</Label>
                  <Select
                    value={formData.weight_goal}
                    onValueChange={(value) => {
                      setFormData({ ...formData, weight_goal: value });
                      setErrors({ ...errors, weight_goal: "" });
                    }}
                  >
                    <SelectTrigger id="weight_goal">
                      <SelectValue placeholder="Select Weight Goal" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
  {getArrayValues(effectiveValidInputs, 'weight_goals', FALLBACK_VALID_INPUTS.weight_goals).map((goal) => (
    <SelectItem key={goal} value={goal}>
      {goal}
    </SelectItem>
  ))}
</SelectGroup>
                    </SelectContent>
                  </Select>
                  {errors.weight_goal && (
                    <p className="text-sm text-destructive">
                      {errors.weight_goal}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="health_issues">Health Issues</Label>
                  <MultiSelect
                    options={healthIssuesOptions}
                    selected={formData.health_issues}
                    onChange={(selected) => {
                      setFormData({ ...formData, health_issues: selected });
                      setErrors({ ...errors, health_issues: "" });
                    }}
                    placeholder="Select Health Issues"
                  />
                  {errors.health_issues && (
                    <p className="text-sm text-destructive">
                      {errors.health_issues}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fasting">Fasting</Label>
                  <Select
                    value={formData.fasting}
                    onValueChange={(value) => {
                      setFormData({ ...formData, fasting: value });
                      setErrors({ ...errors, fasting: "" });
                    }}
                  >
                    <SelectTrigger id="fasting">
                      <SelectValue placeholder="Are you fasting?" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
  {getArrayValues(effectiveValidInputs, 'fasting_statuses', FALLBACK_VALID_INPUTS.fasting_statuses).map((option) => (
    <SelectItem key={option} value={option}>
      {option}
    </SelectItem>
  ))}
</SelectGroup>
                    </SelectContent>
                  </Select>
                  {errors.fasting && (
                    <p className="text-sm text-destructive">{errors.fasting}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Select
                    value={formData.location}
                    onValueChange={(value) => {
                      setFormData({ ...formData, location: value });
                      setErrors({ ...errors, location: "" });
                    }}
                  >
                    <SelectTrigger id="location">
                      <SelectValue placeholder="Select Location" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
  {getArrayValues(effectiveValidInputs, 'supported_locations', FALLBACK_VALID_INPUTS.supported_locations).map((location) => (
    <SelectItem key={location} value={location}>
      {location}
    </SelectItem>
  ))}
</SelectGroup>
                    </SelectContent>
                  </Select>
                  {errors.location && (
                    <p className="text-sm text-destructive">
                      {errors.location}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {errors.submit && (
              <p className="text-center text-sm text-destructive mt-4">
                {errors.submit}
              </p>
            )}
          </form>
        </CardContent>
        <CardFooter>
          <Button
            className="w-full"
            size="lg"
            onClick={handleSubmit}
            disabled={isSubmitting || Object.values(errors).some((err) => err)}
          >
            {isSubmitting ? (
              <Loader2 className="h-5 w-5 mr-2 animate-spin" />
            ) : null}
            Next
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
