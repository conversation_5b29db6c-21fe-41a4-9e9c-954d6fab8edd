"use client";

import { JS<PERSON>, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { AlertCircle, RefreshCw, ChevronDown, ChevronUp, Utensils, Apple, Salad, Soup } from "lucide-react";
import ProgressSteps from "@/components/user/progress-steps";
import { useRecommendationStore } from "@/store/recommendationStore";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { LoadingSpinner } from "@/components/loader/LoadingSpinner";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export default function RecommendationStep3() {
  const router = useRouter();
  const { healthMetrics, predictionResponse, isLoading, error, reset } = useRecommendationStore();

  // State to track expanded meal items
  const [expandedItems, setExpandedItems] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    if (!healthMetrics || !predictionResponse) {
      toast.error("No recommendation data. Please start over.");
      router.push("/userDashboard/recommendation/step1");
    }
  }, [healthMetrics, predictionResponse, router]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-6 space-y-6">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <Button onClick={() => router.push("/userDashboard/recommendation/step1")}>Go Back</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!healthMetrics || !predictionResponse) return null;

  const toggleItem = (mealType: string, index: number) => {
    setExpandedItems((prev) => ({
      ...prev,
      [`${mealType}-${index}`]: !prev[`${mealType}-${index}`],
    }));
  };

  const mealIcons: { [key: string]: JSX.Element } = {
    breakfast: <Utensils className="h-5 w-5 text-yellow-500 dark:text-yellow-400" />,
    lunch: <Salad className="h-5 w-5 text-green-500 dark:text-green-400" />,
    snack: <Apple className="h-5 w-5 text-red-500 dark:text-red-400" />,
    dinner: <Soup className="h-5 w-5 text-blue-500 dark:text-blue-400" />,
  };

  return (
    <div className="flex items-center justify-center p-4 bg-gradient-to-b from-background to-muted/20 min-h-screen">
      <Card className="w-full max-w-5xl shadow-xl rounded-xl overflow-hidden border-border">
        <CardHeader className="bg-primary/5 dark:bg-primary/10">
          <ProgressSteps currentStep={3} showBackButton backUrl="/userDashboard/recommendation/step2" />
          <CardTitle className="text-4xl font-bold text-center mt-6 text-primary">Your Personalized Meal Plan</CardTitle>
          <CardDescription className="text-center text-lg text-muted-foreground">
            Discover your tailored nutrition plan crafted for your goals.
          </CardDescription>
          <Separator className="mt-4" />
        </CardHeader>
        <CardContent className="p-6 space-y-8">
          {/* Profile Summary */}
          <Card className="border-border shadow-md bg-card">
            <CardHeader className="pb-2">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <Utensils className="h-5 w-5 text-primary" />
                Your Profile
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-sm">
                  <span className="font-medium text-primary">Goal:</span>{" "}
                  <span className="text-foreground">{healthMetrics.weight_goal}</span>
                </div>
                <div className="text-sm">
                  <span className="font-medium text-primary">Age:</span>{" "}
                  <span className="text-foreground">{healthMetrics.age}</span>
                </div>
                <div className="text-sm">
                  <span className="font-medium text-primary">Gender:</span>{" "}
                  <span className="text-foreground">{healthMetrics.gender}</span>
                </div>
                <div className="text-sm">
                  <span className="font-medium text-primary">Location:</span>{" "}
                  <span className="text-foreground">{healthMetrics.location}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Nutritional Targets */}
          <Card className="border-border shadow-md bg-card">
            <CardHeader className="pb-2">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <Apple className="h-5 w-5 text-primary" />
                Daily Nutritional Targets
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(predictionResponse.nutritional_detail).map(([key, value]) => (
                  <TooltipProvider key={key}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Card className="bg-gradient-to-br from-primary/10 to-primary/5 dark:from-primary/20 dark:to-primary/10 hover:shadow-lg transition-shadow duration-300 border-border">
                          <CardContent className="p-4 text-center">
                            <p className="text-sm font-medium capitalize text-muted-foreground">
                              {key.replace(/_/g, " ")}
                            </p>
                            <p className="text-lg font-semibold text-primary">
                              {key === "sodium"
                                ? `${(Number(value) / 1000).toFixed(1)} mg`
                                : `${Number(value).toFixed(1)} ${key.includes("calorie") ? "kcal" : "g"}`}
                            </p>
                          </CardContent>
                        </Card>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>
                          {key === "daily_calorie_target"
                            ? "Recommended daily calorie intake"
                            : `Daily ${key.replace(/_/g, " ")} target`}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Meal Plan */}
          <Card className="border-border shadow-md bg-card">
            <CardHeader className="pb-2">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <Salad className="h-5 w-5 text-primary" />
                Recommended Meal Plan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(predictionResponse.recommendations).map(([mealType, foods]) => (
                  <div key={mealType} className="space-y-3">
                    <h4 className="text-lg font-medium text-primary capitalize flex items-center gap-2">
                      {mealIcons[mealType]} {mealType}
                    </h4>
                    <div className="space-y-2">
                      {foods.map((food: any, index: number) => {
                        const isExpanded = expandedItems[`${mealType}-${index}`];
                        return (
                          <Card
                            key={index}
                            className="bg-card hover:bg-muted/50 transition-all duration-300 rounded-lg shadow-sm border-border"
                          >
                            <CardContent className="p-4">
                              <div
                                className="flex justify-between items-center cursor-pointer"
                                onClick={() => toggleItem(mealType, index)}
                              >
                                <div className="flex items-center gap-3">
                                  <span className="text-sm font-medium text-foreground">{food.local_name}</span>
                                  <Badge variant="secondary" className="text-xs">
                                    {food.category}
                                  </Badge>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline">{food.serving_size_grams.toFixed(1)}g</Badge>
                                  {isExpanded ? (
                                    <ChevronUp className="h-4 w-4 text-muted-foreground" />
                                  ) : (
                                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                                  )}
                                </div>
                              </div>
                              {isExpanded && (
                                <div className="mt-4 space-y-2 animate-fade-in">
                                  <p className="text-xs text-muted-foreground">
                                    <span className="font-medium text-foreground">Ingredient:</span> {food.ingredient}
                                  </p>
                                  <p className="text-xs text-muted-foreground">
                                    <span className="font-medium text-foreground">Location:</span> {food.location}
                                  </p>
                                  <div className="grid grid-cols-2 gap-2">
                                    <div>
                                      <p className="text-xs font-medium text-foreground">Serving Nutrition:</p>
                                      <ul className="text-xs text-muted-foreground">
                                        <li>Calories: {food.serving_nutrition.calories.toFixed(1)} kcal</li>
                                        <li>Protein: {food.serving_nutrition.protein.toFixed(1)} g</li>
                                        <li>Fat: {food.serving_nutrition.fat.toFixed(1)} g</li>
                                        <li>Carbs: {food.serving_nutrition.carbohydrates.toFixed(1)} g</li>
                                        <li>Fiber: {food.serving_nutrition.fiber.toFixed(1)} g</li>
                                        <li>Sodium: {(food.serving_nutrition.sodium / 1000).toFixed(2)} mg</li>
                                      </ul>
                                    </div>
                                    <div>
                                      <p className="text-xs font-medium text-foreground">Base Nutrition (per 100g):</p>
                                      <ul className="text-xs text-muted-foreground">
                                        <li>Calories: {food.nutrition.calories.toFixed(1)} kcal</li>
                                        <li>Protein: {food.nutrition.protein.toFixed(1)} g</li>
                                        <li>Fat: {food.nutrition.fat.toFixed(1)} g</li>
                                        <li>Carbs: {food.nutrition.carbohydrates.toFixed(1)} g</li>
                                        <li>Fiber: {food.nutrition.fiber.toFixed(1)} g</li>
                                        <li>Sodium: {(food.nutrition.sodium / 1000).toFixed(2)} mg</li>
                                      </ul>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </CardContent>
        <CardFooter className="flex gap-4 bg-primary/5 dark:bg-primary/10 p-6">
          <Button
            variant="outline"
            className="flex-1"
            onClick={() => router.push("/userDashboard/recommendation/step2")}
          >
            Back to Verify
          </Button>
          <Button
            className="flex-1"
            onClick={() => {
              reset();
              router.push("/userDashboard/recommendation/step1");
            }}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Start Over
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
