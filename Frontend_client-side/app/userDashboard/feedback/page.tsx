"use client";

import React from "react";
import { useState, useEffect } from "react";
import { Send, Star, CheckCircle2, MessageSquare, ChevronLeft, ChevronRight } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON>Footer,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { toast } from "react-hot-toast";
import { createFeedback, getUserFeedback } from "@/services/feedback.service";

interface FeedbackItem {
  feedbackId: string;
  message: string;
  feedbackType: string;
  rating: number;
  adminResponse: string | null;
  createdAt: string;
  status: string;
}

interface FeedbackResponse {
  data: FeedbackItem[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
  };
}

export default function Feedback() {
  const [feedbackText, setFeedbackText] = useState("");
  const [feedbackType, setFeedbackType] = useState("general");
  const [rating, setRating] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [feedbacks, setFeedbacks] = useState<FeedbackItem[]>([]);
  const [loadingFeedbacks, setLoadingFeedbacks] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 6;

  // Fetch user feedback
  useEffect(() => {
    const fetchFeedback = async () => {
      try {
        setLoadingFeedbacks(true);
        const response = await getUserFeedback(currentPage, itemsPerPage);

        if (response?.data) {
          setFeedbacks(response.data);
          setTotalPages(response.meta.totalPages);
        } else {
          setFeedbacks([]);
          setTotalPages(1);
        }
      } catch (err) {
        toast.error("Failed to load your feedback.");
        setFeedbacks([]);
        setTotalPages(1);
      } finally {
        setLoadingFeedbacks(false);
      }
    };
    fetchFeedback();
  }, [currentPage]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!feedbackText.trim()) {
      toast.error("Please enter your feedback before submitting.");
      return;
    }

    if (!rating) {
      toast.error("Please select a rating before submitting.");
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await createFeedback({
        message: feedbackText,
        feedbackType: feedbackType,
        rating: parseInt(rating),
      });

      if (response?.status === 201) {
        setIsSubmitted(true);
        // Refresh feedback list
        const feedbackResponse = await getUserFeedback(1, itemsPerPage);
        if (feedbackResponse?.data) {
          setFeedbacks(feedbackResponse.data);
          setTotalPages(feedbackResponse.meta.totalPages);
        }
        setCurrentPage(1); // Reset to first page
      }
    } catch (error: any) {
      setError(error.response?.data?.message || "Failed to submit feedback");
      toast.error(error.response?.data?.message || "There was an error submitting your feedback. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    setFeedbackText("");
    setFeedbackType("general");
    setRating(null);
    setIsSubmitted(false);
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <div className="container mx-auto py-12 px-4 md:px-6">
      <div className="max-w-2xl mx-auto">
        {isSubmitted ? (
          <Card className="border-green-100 bg-green-50">
            <CardContent className="pt-6 pb-6 flex flex-col items-center text-center">
              <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
              <CardTitle className="text-2xl mb-2">
                Thank You for Your Feedback!
              </CardTitle>
              <CardDescription className="text-base mb-6">
                Your insights help us improve our Ethiopian Nutrition platform for everyone.
              </CardDescription>
              <Button onClick={handleReset}>Submit Another Response</Button>
            </CardContent>
          </Card>
        ) : (
          <form onSubmit={handleSubmit}>
            <Card>
              <CardHeader className="space-y-1">
                <CardTitle className="text-2xl text-center">
                  Share Your Feedback
                </CardTitle>
                <CardDescription className="text-center">
                  Your insights help us improve our Ethiopian Nutrition platform
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="feedback-type">Feedback Category</Label>
                  <Select value={feedbackType} onValueChange={setFeedbackType}>
                    <SelectTrigger id="feedback-type">
                      <SelectValue placeholder="Select feedback category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General Feedback</SelectItem>
                      <SelectItem value="content">Nutritional Content</SelectItem>
                      <SelectItem value="usability">Usability & Design</SelectItem>
                      <SelectItem value="feature">Feature Request</SelectItem>
                      <SelectItem value="bug">Bug Report</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="rating">How would you rate your experience?</Label>
                  <div className="flex justify-between items-center">
                    <RadioGroup
                      className="flex space-x-1 sm:space-x-2"
                      value={rating || ""}
                      onValueChange={setRating}
                    >
                      {[1, 2, 3, 4, 5].map((value) => (
                        <div
                          key={value}
                          className="flex flex-col items-center space-y-1"
                        >
                          <RadioGroupItem
                            value={value.toString()}
                            id={`rating-${value}`}
                            className="sr-only peer"
                          />
                          <Label
                            htmlFor={`rating-${value}`}
                            className="cursor-pointer p-2 rounded-full hover:bg-slate-100 peer-data-[state=checked]:text-amber-50"
                          >
                            <Star
                              className={`h-9 w-9 ${
                                rating && Number.parseInt(rating) >= value
                                  ? "fill-amber-500 text-amber-500"
                                  : "text-slate-300"
                              }`}
                            />
                          </Label>
                          <span className="text-xl">{value}</span>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="feedback">Your Feedback</Label>
                  <Textarea
                    id="feedback"
                    placeholder="Please share your thoughts, suggestions, or report any issues you've encountered..."
                    className="min-h-[150px] resize-none"
                    value={feedbackText}
                    onChange={(e) => setFeedbackText(e.target.value)}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <span className="flex items-center">
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Submitting...
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <Send className="mr-2 h-4 w-4" /> Submit Feedback
                    </span>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </form>
        )}

        {/* Feedback History Section */}
        <div className="mt-8">
          <h2 className="text-2xl font-bold mb-4 flex items-center">
            <MessageSquare className="h-6 w-6 mr-2" />
            Your Feedback History
          </h2>
          {loadingFeedbacks ? (
            <Card>
              <CardContent className="pt-6">
                <p>Loading feedback...</p>
              </CardContent>
            </Card>
          ) : feedbacks.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <p>No feedback submitted yet.</p>
              </CardContent>
            </Card>
          ) : (
            <React.Fragment>
              {feedbacks.map((feedback) => (
                <div key={feedback.feedbackId}>
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex justify-between items-center">
                        <span className="capitalize">{feedback.feedbackType}</span>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={feedback.status === "responded" ? "default" : "secondary"}
                            className={feedback.status === "responded" ? "bg-green-500" : "bg-yellow-500"}
                          >
                            {feedback.status.charAt(0).toUpperCase() + feedback.status.slice(1)}
                          </Badge>
                          <Badge variant="outline">
                            {new Date(feedback.createdAt).toLocaleDateString()}
                          </Badge>
                        </div>
                      </CardTitle>
                      <CardDescription>
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={`${feedback.feedbackId}-star-${star}`}
                            className={`inline h-4 w-4 mr-0.5 ${
                              feedback.rating >= star
                                ? "text-amber-500 fill-amber-500"
                                : "text-slate-300"
                            }`}
                          />
                        ))}
                        <span className="ml-2 text-sm text-slate-500">{feedback.rating}/5</span>
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="mb-4">{feedback.message}</p>
                      {feedback.adminResponse && (
                        <div className="p-4 bg-green-100 rounded-md">
                          <p className="font-semibold flex items-center">
                            <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                            Admin Response
                          </p>
                          <p>{feedback.adminResponse}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              ))}
              {/* Pagination Controls */}
              <div className="flex justify-between items-center mt-4" key="pagination-controls">
                <Button
                  variant="outline"
                  onClick={handlePreviousPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>
                <span className="text-sm">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  onClick={handleNextPage}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </React.Fragment>
          )}
        </div>
      </div>
    </div>
  );
}