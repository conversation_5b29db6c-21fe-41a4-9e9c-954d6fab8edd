"use client";
import { useEffect, useState, use } from "react";
import { useRouter } from "next/navigation";
import { Arrow<PERSON>ef<PERSON>, MapPin, Utensils } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import Image from "next/image";
import { api } from "@/services/api";
interface FoodItem {
  foodId: string;
  name: string;
  localName: string;
  region: string;
  description: string;
  calories: string;
  servingSize: string;
  image: string;
  category: string;
  baseIngredient: string;
  preparationMethod: string;
  nutrients: {
    protein: number;
    carbohydrates: number;
    fat: number;
    fiber: number;
    calcium: number;
    sodium: number;
  };
  culturalSignificance: string;
  healthBenefits: string;
  nutritionalContext: string;
  highlights: string;
}

export default function FoodItemDetail({ params }: { params: Promise<{ id: string }> }) {
  const [foodItem, setFoodItem] = useState<FoodItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  
  const { id } = use(params);

  useEffect(() => {
    const fetchFoodItem = async () => {
      try {
        setLoading(true);
        console.log("Fetching food item with ID:", id);
        const response = await api.get(`/nutritional-database/${id}`);
        setFoodItem(response.data);
        setError(null);
      } catch (err) {
        console.error("Error fetching food item:", err);
        setError("Failed to load food item");
        setFoodItem(null);
      } finally {
        setLoading(false);
      }
    };

    fetchFoodItem();
  }, [id]);

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 md:px-6">
        <div className="h-10 w-32 mb-6">
          <Skeleton className="h-full w-full" />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-1">
            <Skeleton className="h-[300px] w-full rounded-lg mb-4" />
            <Skeleton className="h-10 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2 mb-6" />
            <Skeleton className="h-8 w-full mb-2" />
            <Skeleton className="h-24 w-full rounded-md" />
          </div>

          <div className="lg:col-span-2">
            <Skeleton className="h-10 w-full mb-4" />
            <div className="space-y-2 mb-6">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
            <Skeleton className="h-8 w-48 mb-4" />
            <div className="grid grid-cols-2 gap-4 mb-6">
              <Skeleton className="h-20 w-full rounded-md" />
              <Skeleton className="h-20 w-full rounded-md" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !foodItem) {
    return (
      <div className="container mx-auto py-16 px-4 md:px-6 text-center">
        <h2 className="text-2xl font-bold mb-4">
          {error ? "Error Loading Food Item" : "Food Item Not Found"}
        </h2>
        <p className="mb-8">
          {error || "The food item you're looking for doesn't exist or has been removed."}
        </p>
        <Button onClick={() => router.push("/userDashboard/contentSearch")}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Food Database
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <Button
        variant="ghost"
        className="mb-6"
        onClick={() => router.push("/userDashboard/contentSearch")}
      >
        <ArrowLeft className="mr-2 h-4 w-4" /> Back to Food Database
      </Button>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1">
          <div className="rounded-lg overflow-hidden border border-slate-200 mb-6">
            <Image
              src={foodItem.image || "/food-placeholder.png"}
              alt={foodItem.name}
              width={400}
              height={300}
              className="w-full h-[300px] object-cover"
            />
          </div>

          <h1 className="text-3xl font-bold mb-1">
            {foodItem.name} ({foodItem.localName})
          </h1>

          <div className="flex items-center mb-6">
            <MapPin className="h-4 w-4 mr-1" />
            <span>{foodItem.region}</span>
            {foodItem.category && (
              <>
                <span className="mx-2">•</span>
                <Badge variant="outline">{foodItem.category}</Badge>
              </>
            )}
          </div>

          <div className="flex items-start mb-6 text-sm">
            <Utensils className="h-4 w-4 mr-2 mt-0.5" />
            <span>Base Ingredient: {foodItem.baseIngredient}</span>
          </div>

          <Card className="mb-6">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Nutritional Information</CardTitle>
              <p className="text-sm">Per {foodItem.servingSize}</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Calories</span>
                  <span className="font-bold">{foodItem.calories} kcal</span>
                </div>
                <Separator />
                <div className="flex justify-between items-center">
                  <span>Protein</span>
                  <span>{foodItem.nutrients.protein} g</span>
                </div>
                <Separator />
                <div className="flex justify-between items-center">
                  <span>Carbohydrates</span>
                  <span>{foodItem.nutrients.carbohydrates} g</span>
                </div>
                <Separator />
                <div className="flex justify-between items-center">
                  <span>Fat</span>
                  <span>{foodItem.nutrients.fat} g</span>
                </div>
                <Separator />
                <div className="flex justify-between items-center">
                  <span>Fiber</span>
                  <span>{foodItem.nutrients.fiber} g</span>
                </div>
                <Separator />
                <div className="flex justify-between items-center">
                  <span>Calcium</span>
                  <span>{foodItem.nutrients.calcium} mg</span>
                </div>
                <Separator />
                <div className="flex justify-between items-center">
                  <span>Sodium</span>
                  <span>{foodItem.nutrients.sodium} mg</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-2">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="nutrition">Nutrition</TabsTrigger>
              <TabsTrigger value="health">Health & Culture</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold mb-3">Description</h2>
                <Card>
                  <CardContent className="p-4">
                    <p>{foodItem.description}</p>
                  </CardContent>
                </Card>
              </div>

              <div>
                <h2 className="text-xl font-semibold mb-3">Preparation Method</h2>
                <Card>
                  <CardContent className="p-4">
                    <p>{foodItem.preparationMethod}</p>
                  </CardContent>
                </Card>
              </div>

              <div>
                <h2 className="text-xl font-semibold mb-3">Cultural Significance</h2>
                <Card>
                  <CardContent className="p-4">
                    <p>{foodItem.culturalSignificance}</p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="nutrition" className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold mb-3">Macronutrients</h2>
                <Card>
                  <CardContent className="p-4">
                    <div className="grid grid-cols-1 gap-4">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center p-3 border border-green-100 rounded-md">
                          <div className="text-sm mb-1">Protein</div>
                          <div className="text-xl font-bold">{foodItem.nutrients.protein} g</div>
                          <div className="text-xs mt-1">
                            {Math.round(((foodItem.nutrients.protein * 4) / Number(foodItem.calories)) * 100)}% of calories
                          </div>
                        </div>
                        <div className="text-center p-3 border border-green-100 rounded-md">
                          <div className="text-sm mb-1">Carbohydrates</div>
                          <div className="text-xl font-bold">{foodItem.nutrients.carbohydrates} g</div>
                          <div className="text-xs mt-1">
                            {Math.round(((foodItem.nutrients.carbohydrates * 4) / Number(foodItem.calories)) * 100)}% of calories
                          </div>
                        </div>
                        <div className="text-center p-3 border border-green-100 rounded-md">
                          <div className="text-sm mb-1">Fat</div>
                          <div className="text-xl font-bold">{foodItem.nutrients.fat} g</div>
                          <div className="text-xs mt-1">
                            {Math.round(((foodItem.nutrients.fat * 9) / Number(foodItem.calories)) * 100)}% of calories
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <h2 className="text-xl font-semibold mb-3">Nutritional Context</h2>
                <Card>
                  <CardContent className="p-4">
                    <p>{foodItem.nutritionalContext}</p>
                    <p className="mt-2 text-sm">
                      Nutritional values are for {foodItem.servingSize}. Content may vary based on preparation and regional differences.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="health" className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold mb-3">Health Benefits</h2>
                <Card>
                  <CardContent className="p-4">
                    <p>{foodItem.healthBenefits}</p>
                  </CardContent>
                </Card>
              </div>

              <div>
                <h2 className="text-xl font-semibold mb-3">Highlights</h2>
                <Card>
                  <CardContent className="p-4">
                    <p>{foodItem.highlights}</p>
                  </CardContent>
                </Card>
              </div>

              <div>
                <h2 className="text-xl font-semibold mb-3">Nutritional Highlights</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {foodItem.nutrients.protein > 10 && (
                    <Card>
                      <CardContent className="p-4">
                        <h3 className="font-medium mb-2">High in Protein</h3>
                        <p className="text-sm">
                          With {foodItem.nutrients.protein} g of protein per serving, this food supports muscle building and repair.
                        </p>
                      </CardContent>
                    </Card>
                  )}
                  {foodItem.nutrients.fiber > 3 && (
                    <Card>
                      <CardContent className="p-4">
                        <h3 className="font-medium mb-2">Good Source of Fiber</h3>
                        <p className="text-sm">
                          Contains {foodItem.nutrients.fiber} g of fiber, aiding digestive health and satiety.
                        </p>
                      </CardContent>
                    </Card>
                  )}
                  {foodItem.nutrients.calcium > 100 && (
                    <Card>
                      <CardContent className="p-4">
                        <h3 className="font-medium mb-2">Rich in Calcium</h3>
                        <p className="text-sm">
                          Provides {foodItem.nutrients.calcium} mg of calcium, supporting bone health.
                        </p>
                      </CardContent>
                    </Card>
                  )}
                  {foodItem.nutrients.fat < 5 && (
                    <Card>
                      <CardContent className="p-4">
                        <h3 className="font-medium mb-2">Low in Fat</h3>
                        <p className="text-sm">
                          With {foodItem.nutrients.fat} g of fat per serving, it’s suitable for low-fat diets.
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}