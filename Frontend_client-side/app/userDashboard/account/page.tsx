"use client";
import type React from "react";
import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { Camera, ArrowLeft, User, Mail, Phone, MapPin, Shield, Heart, Activity, Weight, Ruler } from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "react-hot-toast";
import { api } from "@/services/api";
import { userService } from "@/services/user.service";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { <PERSON>, <PERSON>, EyeOff } from "lucide-react";
import { authService } from "@/services/auth.service";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";


interface UserProfile {
  userId: string;
  name: string;
  email: string;
  phone?: string;
  location?: string;
  profileImage?: string;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  healthMetrics?: {
    age?: number;
    weight?: number;
    height?: number;
    gender?: string;
    activityLevel?: string;
    dietaryPreference?: string;
    goals?: string;
    healthIssues?: string[];
    fasting?: string;
    location?: string;
  };
  bmi?: number;
  targetWeight?: number;
  dailyCalorieTarget?: number;
}


interface HealthMetricsForm {
  age: string;
  weight: string;
  height: string;
  gender: string;
  activityLevel: string;
  dietaryPreference: string;
  weightGoal: string;
  healthIssues: string[];
  fasting: string;
  location: string;
}


const genderOptions = [
  { value: "Male", label: "Male" },
  { value: "Female", label: "Female" },
];


const activityLevelOptions = [
  { value: "Sedentary", label: "Sedentary (little or no exercise)" },
  { value: "Lightly Active", label: "Light (exercise 1-3 times/week)" },
  { value: "Moderately Active", label: "Moderate (exercise 3-5 times/week)" },
  { value: "Very Active", label: "Active (exercise 6-7 times/week)" },
];


const dietaryPreferenceOptions = [
  { value: "Omnivore", label: "Omnivore" },
  { value: "Vegetarian", label: "Vegetarian" },
  { value: "Vegan", label: "Vegan" },
];


const weightGoalOptions = [
  { value: "Lose Weight", label: "Lose Weight" },
  { value: "Maintain Weight", label: "Maintain Weight" },
  { value: "Gain Weight", label: "Gain Weight" },
];


const healthIssueOptions = [
  "None",
  "Diabetes",
  "Hypertension",
  "Heart Disease",
  "Kidney Disease",
  "Acne",
];


const locationOptions = [
  "afar",
  "asossa/berta",
  "asossa/komo",
  "asossa/mao",
  "bale",
  "bench and maji zone/bench",
  "bench and maji zone/menit",
  "bench and maji zone/sheko",
  "borena",
  "dorze",
  "e. wellega",
  "e.hararge/jarso",
  "e.hararge/kersa",
  "gambella/agnuak",
  "gambella/nuer",
  "gamo",
  "gedeo",
  "gonder/negede weyto",
  "guji",
  "gurage/cheha",
  "harari",
  "keficho zone/keficho",
  "kemache zone/gumuz",
  "kembata",
  "konsso",
  "metekel/gumuz",
  "metekel/shinasha",
  "n. gonder/dembia",
  "n. omo/konta",
  "sidama",
  "tigray/central zone",
  "tigray/eastern zone",
  "tigray/kunama",
  "tigray/mekele",
  "tigray/shire",
  "tigray/southern zone",
  "urban/common",
  "w. wellega",
  "welayita",
  "addis ababa",
  "dire dawa",
  "adama",
  "gondar",
  "mekelle",
  "hawassa",
  "bahir dar",
  "jimma",
  "debre markos"
];


export default function ProfilePage() {
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [userData, setUserData] = useState<UserProfile>({
    userId: "",
    name: "",
    email: "",
    phone: "",
    location: "",
    emailVerified: false,
    createdAt: "",
    updatedAt: "",
    healthMetrics: {
      age: undefined,
      weight: undefined,
      height: undefined,
      gender: undefined,
      activityLevel: undefined,
      dietaryPreference: undefined,
      goals: undefined,
      healthIssues: undefined,
      fasting: undefined,
      location: undefined
    },
  });
 
  const [healthMetricsForm, setHealthMetricsForm] = useState<HealthMetricsForm>({
    age: "",
    weight: "",
    height: "",
    gender: "Male",
    activityLevel: "Moderately Active",
    dietaryPreference: "Omnivore",
    weightGoal: "Maintain Weight",
    healthIssues: [],
    fasting: "No",
    location: "",
  });
 
  const [isLoading, setIsLoading] = useState(false);
  const [isHealthMetricsLoading, setIsHealthMetricsLoading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);


  // Add these state variables
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  });
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [passwordError, setPasswordError] = useState<string | null>(null);


  // Fetch user data from API
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        setIsPageLoading(true);
        const response = await userService.getUserProfile();
        // Update state with user data
        setUserData({
          userId: response.id,
          name: response.name,
          email: response.email,
          phone: response.phone || "",
          location: response.healthMetrics?.location || "",
          profileImage: response.profilePicture,
          emailVerified: response.emailVerified,
          createdAt: response.createdAt,
          updatedAt: response.updatedAt,
          healthMetrics: response.healthMetrics ? {
            ...response.healthMetrics,
            healthIssues: Array.isArray(response.healthMetrics.healthIssues) ? 
              response.healthMetrics.healthIssues : 
              response.healthMetrics.healthIssues ? 
                [response.healthMetrics.healthIssues] : 
                [],
            fasting: response.healthMetrics.fasting || "No",
            location: response.healthMetrics.location || "",
          } : {
            age: undefined,
            weight: undefined,
            height: undefined,
            gender: undefined,
            activityLevel: undefined,
            dietaryPreference: undefined,
            goals: undefined,
            healthIssues: [],
            fasting: undefined,
            location: undefined
          },
          bmi: response.bmi,
          targetWeight: response.targetWeight,
          dailyCalorieTarget: response.dailyCalorieTarget,
        });
       
        // Initialize health metrics form with user data
        if (response.healthMetrics) {
          setHealthMetricsForm({
            age: response.healthMetrics.age?.toString() || "",
            weight: response.healthMetrics.weight?.toString() || "",
            height: response.healthMetrics.height?.toString() || "",
            gender: response.healthMetrics.gender || "Male",
            activityLevel: response.healthMetrics.activityLevel || "Moderately Active",
            dietaryPreference: response.healthMetrics.dietaryPreference || "Omnivore",
            weightGoal: response.healthMetrics.goals || "Maintain Weight",
            healthIssues: Array.isArray(response.healthMetrics.healthIssues) ? 
              response.healthMetrics.healthIssues : 
              response.healthMetrics.healthIssues ? 
                [response.healthMetrics.healthIssues] : 
                [],
            fasting: response.healthMetrics.fasting || "No",
            location: response.healthMetrics.location || "",
          });
        }
       
        // Set profile image if available
        if (response.profilePicture) {
          setProfileImage(response.profilePicture);
        }
      } catch (error) {
        console.error("Failed to fetch user profile:", error);
        toast.error("Failed to load profile data");
      } finally {
        setIsPageLoading(false);
      }
    };


    fetchUserProfile();
  }, []);


  const handleImageClick = () => {
    fileInputRef.current?.click();
  };


  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = async (event) => {
        const result = event.target?.result as string;
        setProfileImage(result);
        
        // Upload profile image to server
        try {
          const formData = new FormData();
          formData.append('profileImage', file);
          
          await api.post('/users/profile-image', formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });
          
          toast.success("Profile image updated");
        } catch (error) {
          console.error("Failed to upload profile image:", error);
          toast.error("Failed to update profile image");
        }
      };
      reader.readAsDataURL(file);
    }
  };


  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUserData({
      ...userData,
      [name]: value,
    });
  };


  const handleHealthMetricsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setHealthMetricsForm({
      ...healthMetricsForm,
      [name]: value,
    });
  };


  const handleSelectChange = (name: string, value: string) => {
    setHealthMetricsForm({
      ...healthMetricsForm,
      [name]: value,
    });
  };


  const handleHealthIssueChange = (issue: string, checked: boolean) => {
    let updatedIssues = [...healthMetricsForm.healthIssues];
   
    if (checked) {
      if (issue === "None") {
        updatedIssues = ["None"];
      } else {
        updatedIssues = updatedIssues.filter(i => i !== "None");
        updatedIssues.push(issue);
      }
    } else {
      updatedIssues = updatedIssues.filter(i => i !== issue);
    }
   
    setHealthMetricsForm({
      ...healthMetricsForm,
      healthIssues: updatedIssues
    });
  };


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    const user = await authService.checkUser();
   
    try {
      // Send updated profile data to server
      await api.put(`/users/${user.id}`, {
        name: userData.name,
        phone: userData.phone,
        location: userData.location
      });
     
      toast.success("Profile updated successfully!");
    } catch (error) {
      console.error("Failed to update profile:", error);
      toast.error("Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };


  const handleHealthMetricsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsHealthMetricsLoading(true);
   
    try {
      // Convert string values to appropriate types
      const payload = {
        age: parseInt(healthMetricsForm.age) || 0,
        weight: parseFloat(healthMetricsForm.weight) || 0,
        height: parseFloat(healthMetricsForm.height) || 0,
        gender: healthMetricsForm.gender,
        activity_level: healthMetricsForm.activityLevel,
        dietary_preference: healthMetricsForm.dietaryPreference,
        weight_goal: healthMetricsForm.weightGoal,
        health_issues: healthMetricsForm.healthIssues.length > 0 ? healthMetricsForm.healthIssues : ["None"],
        fasting: healthMetricsForm.fasting,
        location: healthMetricsForm.location,
      };
     
      // Send updated health metrics to server
      await api.patch('/health-metrics', payload);
     
      // Update local state with new values
      setUserData(prev => ({
        ...prev,
        healthMetrics: {
          ...prev.healthMetrics,
          ...payload,
        }
      }));
     
      toast.success("Health metrics updated successfully!");
     
      // Refresh user profile to get updated BMI and target weight
      const updatedProfile = await userService.getUserProfile();
      setUserData(prev => ({
        ...prev,
        bmi: updatedProfile.bmi,
        targetWeight: updatedProfile.targetWeight,
        dailyCalorieTarget: updatedProfile.dailyCalorieTarget,
      }));
     
    } catch (error) {
      console.error("Failed to update health metrics:", error);
      toast.error("Failed to update health metrics");
    } finally {
      setIsHealthMetricsLoading(false);
    }
  };


  const handlePasswordChange = () => {
    setPasswordForm({
      currentPassword: "",
      newPassword: "",
      confirmPassword: ""
    });
    setPasswordError(null);
    setPasswordStrength(0);
    setIsPasswordDialogOpen(true);
  };


  const handlePasswordInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordForm({
      ...passwordForm,
      [name]: value
    });


    if (name === "newPassword") {
      // Calculate password strength
      let strength = 0;
      if (value.length >= 8) strength += 1;
      if (/[A-Z]/.test(value)) strength += 1;
      if (/[0-9]/.test(value)) strength += 1;
      if (/[^A-Za-z0-9]/.test(value)) strength += 1;
      setPasswordStrength(strength);
    }


    // Clear error when typing
    if (passwordError) setPasswordError(null);
  };


  const handleChangePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
   
    // Validate passwords
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setPasswordError("New passwords do not match");
      return;
    }


    if (passwordStrength < 3) {
      setPasswordError("Please create a stronger password");
      return;
    }


    setIsChangingPassword(true);
    setPasswordError(null);


    try {
      await authService.changePassword({
        email: userData.email,
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword
      });
     
      toast.success("Password changed successfully");
      setIsPasswordDialogOpen(false);
    } catch (error: any) {
      console.error("Failed to change password:", error);
      setPasswordError(error.response?.data?.message || "Failed to change password");
    } finally {
      setIsChangingPassword(false);
    }
  };


  if (isPageLoading) {
    return (
      <div className="container mx-auto py-8 px-4 md:px-6">
        <div className="flex items-center mb-6">
          <div className="h-5 w-5 rounded-full animate-pulse mr-4"></div>
          <div className="h-8 w-48 rounded animate-pulse"></div>
        </div>
       
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-1">
            <div className="h-96 rounded-lg animate-pulse"></div>
          </div>
          <div className="lg:col-span-2">
            <div className="h-12 rounded-lg animate-pulse mb-6"></div>
            <div className="h-96 rounded-lg animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }


  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <div className="flex items-center mb-6">
        <Link
          href="/userDashboard"
          className="mr-4 text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h1 className="text-2xl font-semibold text-gray-800">
          Your Profile
        </h1>
      </div>


      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1">
          <Card className="shadow-sm border-slate-200 overflow-hidden">
            <div className="h-32 bg-gradient-to-r from-purple-500 to-indigo-600"></div>
            <CardContent className="p-6">
              <div className="flex flex-col items-center -mt-16">
                <div
                  className="relative w-32 h-32 rounded-full overflow-hidden border-4 border-white dark:border-slate-700 cursor-pointer bg-white dark:bg-slate-700 shadow-md"
                  onClick={handleImageClick}
                >
                  {profileImage ? (
                    <Image
                      src={profileImage}
                      alt="Profile"
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-slate-100 dark:bg-slate-600">
                      <User className="h-16 w-16 text-slate-400 dark:text-slate-300" />
                    </div>
                  )}
                  <div className="absolute bottom-0 right-0 bg-gray-800 p-1.5 rounded-full shadow-sm">
                    <Camera className="h-4 w-4 text-white" />
                  </div>
                </div>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleImageChange}
                  accept="image/*"
                  className="hidden"
                />
                <h2 className="mt-4 text-xl font-semibold text-slate-800 dark:text-white">{userData.name}</h2>
                <p className="text-slate-500 dark:text-slate-300">{userData.email}</p>
                {userData.emailVerified && (
                  <span className="mt-1 px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 text-xs rounded-full">
                    Verified
                  </span>
                )}
              </div>


              <Separator className="my-6 dark:bg-slate-600" />


              <div className="space-y-4">
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-slate-400 dark:text-slate-300 mr-3" />
                  <div>
                    <p className="text-sm text-slate-500 dark:text-slate-400">Email</p>
                    <p className="text-slate-700 dark:text-white">{userData.email}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Phone className="h-5 w-5 text-slate-400 dark:text-slate-300 mr-3" />
                  <div>
                    <p className="text-sm text-slate-500 dark:text-slate-400">Phone</p>
                    <p className="text-slate-700 dark:text-white">{userData.phone || "Not set"}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-slate-400 dark:text-slate-300 mr-3" />
                  <div>
                    <p className="text-sm text-slate-500 dark:text-slate-400">Location</p>
                    <p className="text-slate-700 dark:text-white">{userData.location || "Not set"}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          {/* Health Summary Card */}
          <Card className="shadow-sm border-slate-200 mt-6">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Heart className="h-5 w-5 text-slate-400 mr-2" />
                Health Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {userData.healthMetrics?.weight && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Weight className="h-5 w-5 text-slate-400 mr-2" />
                    <span className="text-sm text-red-500">Current Weight</span>
                  </div>
                  <span className="font-medium text-slate-700">{userData.healthMetrics.weight} kg</span>
                </div>
              )}
             
              {userData.targetWeight && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Activity className="h-5 w-5 text-slate-400 mr-2" />
                    <span className="text-sm text-slate-500">Target Weight</span>
                  </div>
                  <span className="font-medium text-slate-700">{userData.targetWeight} kg</span>
                </div>
              )}
             
              {userData.bmi && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Ruler className="h-5 w-5 text-slate-400 mr-2" />
                    <span className="text-sm text-slate-500">BMI</span>
                  </div>
                  <span className="font-medium text-slate-700">{userData.bmi}</span>
                </div>
              )}
             
              {userData.dailyCalorieTarget && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Activity className="h-5 w-5 text-slate-400 mr-2" />
                    <span className="text-sm text-slate-500">Daily Calorie Target</span>
                  </div>
                  <span className="font-medium text-slate-700">{(userData.dailyCalorieTarget).toFixed(2)} kcal</span>
                </div>
              )}
             
              {(!userData.healthMetrics?.weight && !userData.bmi) && (
                <p className="text-sm text-slate-500 italic">Complete your health profile to see your health summary</p>
              )}
            </CardContent>
          </Card>


          <Card className="shadow-sm border-slate-200 mt-6">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Shield className="h-5 w-5 text-slate-400 mr-2" />
                Account Security
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Button 
                variant="outline" 
                className="w-full"
                onClick={handlePasswordChange}
              >
                Change Password
              </Button>
            </CardContent>
          </Card>
        </div>


        <div className="lg:col-span-2">
          <Tabs defaultValue="profile" className="w-full">
            <TabsList className="grid grid-cols-2 mb-6">
              <TabsTrigger value="profile">Profile Information</TabsTrigger>
              <TabsTrigger value="health">Health Metrics</TabsTrigger>
            </TabsList>


            <TabsContent value="profile">
              <Card className="shadow-sm border-slate-200">
                <CardHeader>
                  <CardTitle>Edit Profile</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label
                          htmlFor="name"
                          className="text-sm font-medium text-gray-700"
                        >
                          Full Name
                        </label>
                        <Input
                          type="text"
                          id="name"
                          name="name"
                          value={userData.name}
                          onChange={handleInputChange}
                          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div className="space-y-2">
                        <label
                          htmlFor="email"
                          className="text-sm font-medium text-gray-700"
                        >
                          Email Address
                        </label>
                        <Input
                          type="email"
                          id="email"
                          name="email"
                          value={userData.email}
                          disabled
                          className="w-full p-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                        />
                        <p className="text-xs text-gray-500">Email cannot be changed</p>
                      </div>
                      <div className="space-y-2">
                        <label
                          htmlFor="phone"
                          className="text-sm font-medium text-gray-700"
                        >
                          Phone Number
                        </label>
                        <Input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={userData.phone}
                          onChange={handleInputChange}
                          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div className="space-y-2">
                        <label
                          htmlFor="location"
                          className="text-sm font-medium text-gray-700"
                        >
                          Location
                        </label>
                        <Select
                          value={userData.location}
                          onValueChange={(value) =>
                            setUserData({ ...userData, location: value })
                          }
                        >
                          <SelectTrigger id="location" className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <SelectValue placeholder="Select Location" />
                          </SelectTrigger>
                          <SelectContent className="max-h-[300px]">
                            {locationOptions.map((location) => (
                              <SelectItem key={location} value={location}>
                                {location}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>


                    <div className="flex justify-end">
                      <Button
                        type="submit"
                        className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
                        disabled={isLoading}
                      >
                        {isLoading ? "Saving..." : "Save Changes"}
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>


            <TabsContent value="health">
              <Card className="shadow-sm border-slate-200">
                <CardHeader>
                  <CardTitle>Health Metrics</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Update your health information to get personalized recommendations
                  </p>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleHealthMetricsSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Age */}
                      <div className="space-y-2">
                        <label htmlFor="age" className="text-sm font-medium text-gray-700">
                          Age
                        </label>
                        <Input
                          type="number"
                          id="age"
                          name="age"
                          value={healthMetricsForm.age}
                          onChange={(e) => setHealthMetricsForm({...healthMetricsForm, age: e.target.value})}
                          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          min="18"
                          max="120"
                        />
                      </div>
                     
                      {/* Gender */}
                      <div className="space-y-2">
                        <label htmlFor="gender" className="text-sm font-medium text-gray-700">
                          Gender
                        </label>
                        <Select
                          value={healthMetricsForm.gender}
                          onValueChange={(value) => setHealthMetricsForm({...healthMetricsForm, gender: value})}
                        >
                          <SelectTrigger id="gender" className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <SelectValue placeholder="Select Gender" />
                          </SelectTrigger>
                          <SelectContent>
                            {genderOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                     
                      {/* Height */}
                      <div className="space-y-2">
                        <label htmlFor="height" className="text-sm font-medium text-gray-700">
                          Height (cm)
                        </label>
                        <Input
                          type="number"
                          id="height"
                          name="height"
                          value={healthMetricsForm.height}
                          onChange={(e) => setHealthMetricsForm({...healthMetricsForm, height: e.target.value})}
                          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          min="50"
                          max="250"
                          step="0.1"
                        />
                      </div>
                     
                      {/* Weight */}
                      <div className="space-y-2">
                        <label htmlFor="weight" className="text-sm font-medium text-gray-700">
                          Weight (kg)
                        </label>
                        <Input
                          type="number"
                          id="weight"
                          name="weight"
                          value={healthMetricsForm.weight}
                          onChange={(e) => setHealthMetricsForm({...healthMetricsForm, weight: e.target.value})}
                          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          min="20"
                          max="300"
                          step="0.1"
                        />
                      </div>
                     
                      {/* Activity Level */}
                      <div className="space-y-2">
                        <label htmlFor="activityLevel" className="text-sm font-medium text-gray-700">
                          Activity Level
                        </label>
                        <Select
                          value={healthMetricsForm.activityLevel}
                          onValueChange={(value) => setHealthMetricsForm({...healthMetricsForm, activityLevel: value})}
                        >
                          <SelectTrigger id="activityLevel" className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <SelectValue placeholder="Select Activity Level" />
                          </SelectTrigger>
                          <SelectContent>
                            {activityLevelOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                     
                      {/* Dietary Preference */}
                      <div className="space-y-2">
                        <label htmlFor="dietaryPreference" className="text-sm font-medium text-gray-700">
                          Dietary Preference
                        </label>
                        <Select
                          value={healthMetricsForm.dietaryPreference}
                          onValueChange={(value) => setHealthMetricsForm({...healthMetricsForm, dietaryPreference: value})}
                        >
                          <SelectTrigger id="dietaryPreference" className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <SelectValue placeholder="Select Dietary Preference" />
                          </SelectTrigger>
                          <SelectContent>
                            {dietaryPreferenceOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                     
                      {/* Weight Goal */}
                      <div className="space-y-2">
                        <label htmlFor="weightGoal" className="text-sm font-medium text-gray-700">
                          Weight Goal
                        </label>
                        <Select
                          value={healthMetricsForm.weightGoal}
                          onValueChange={(value) => setHealthMetricsForm({...healthMetricsForm, weightGoal: value})}
                        >
                          <SelectTrigger id="weightGoal" className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <SelectValue placeholder="Select Weight Goal" />
                          </SelectTrigger>
                          <SelectContent>
                            {weightGoalOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                     
                      {/* Fasting */}
                      <div className="space-y-2">
                        <label htmlFor="fasting" className="text-sm font-medium text-gray-700">
                          Are you fasting?
                        </label>
                        <Select
                          value={healthMetricsForm.fasting}
                          onValueChange={(value) => setHealthMetricsForm({...healthMetricsForm, fasting: value})}
                        >
                          <SelectTrigger id="fasting" className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <SelectValue placeholder="Select Fasting Status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Yes">Yes</SelectItem>
                            <SelectItem value="No">No</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                     
                      {/* Location */}
                      <div className="space-y-2 col-span-full">
                        <label htmlFor="location" className="text-sm font-medium text-gray-700">
                          Location
                        </label>
                        <Select
                          value={healthMetricsForm.location}
                          onValueChange={(value) => setHealthMetricsForm({...healthMetricsForm, location: value})}
                        >
                          <SelectTrigger id="location" className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <SelectValue placeholder="Select Location" />
                          </SelectTrigger>
                          <SelectContent className="max-h-[300px]">
                            {locationOptions.map((location) => (
                              <SelectItem key={location} value={location}>
                                {location}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                   
                    {/* Health Issues */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">
                        Health Issues (Select all that apply)
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                        {healthIssueOptions.map((issue) => (
                          <div key={issue} className="flex items-center space-x-2">
                            <Checkbox
                              id={`health-issue-${issue}`}
                              checked={healthMetricsForm.healthIssues.includes(issue)}
                              onCheckedChange={(checked) =>
                                handleHealthIssueChange(issue, checked === true)
                              }
                            />
                            <label htmlFor={`health-issue-${issue}`} className="text-sm text-gray-700">
                              {issue}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                   
                    {/* Health Metrics Summary */}
                    {userData.bmi && (
                      <div className="bg-slate-50 p-4 rounded-lg space-y-2">
                        <h3 className="font-medium text-gray-700">Your Health Summary</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="flex items-center space-x-2">
                            <Activity className="h-5 w-5 text-blue-500" />
                            <div>
                              <p className="text-sm text-gray-500">BMI</p>
                              <p className="font-medium text-gray-700">{typeof userData.bmi === "number"
                                ? userData.bmi.toFixed(1)
                                : userData.bmi || "N/A"}</p>
                            </div>
                          </div>
                          {userData.targetWeight && (
                            <div className="flex items-center space-x-2">
                              <Weight className="h-5 w-5 text-blue-500" />
                              <div>
                                <p className="text-sm text-gray-500">Target Weight</p>
                                <p className="font-medium text-gray-700">{userData.targetWeight} kg</p>
                              </div>
                            </div>
                          )}
                          {userData.dailyCalorieTarget && (
                            <div className="flex items-center space-x-2">
                              <Ruler className="h-5 w-5 text-blue-500" />
                              <div>
                                <p className="text-sm text-gray-500">Daily Calorie Target</p>
                                <p className="font-medium text-gray-700">{userData.dailyCalorieTarget} kcal</p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                   
                    <Button 
                      type="submit" 
                      className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
                      disabled={isHealthMetricsLoading}
                    >
                      {isHealthMetricsLoading ? "Updating..." : "Update Health Metrics"}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Password Change Dialog */}
      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-gray-800">Change Password</DialogTitle>
          </DialogHeader>
         
          <form onSubmit={handleChangePasswordSubmit} className="space-y-4 py-4">
            {passwordError && (
              <div className="bg-red-50 text-red-600 p-3 rounded-md text-sm">
                {passwordError}
              </div>
            )}
           
            <div className="space-y-2">
              <label htmlFor="currentPassword" className="text-sm font-medium text-gray-700">
                Current Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                <Input
                  id="currentPassword"
                  name="currentPassword"
                  type={showCurrentPassword ? "text" : "password"}
                  value={passwordForm.currentPassword}
                  onChange={handlePasswordInputChange}
                  className="w-full pl-10 pr-10 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="••••••••"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="lg"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  {showCurrentPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                  <span className="sr-only">
                    {showCurrentPassword ? "Hide password" : "Show password"}
                  </span>
                </Button>
              </div>
            </div>
           
            <div className="space-y-2">
              <label htmlFor="newPassword" className="text-sm font-medium text-gray-700">
                New Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                <Input
                  id="newPassword"
                  name="newPassword"
                  type={showNewPassword ? "text" : "password"}
                  value={passwordForm.newPassword}
                  onChange={handlePasswordInputChange}
                  className="w-full pl-10 pr-10 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="••••••••"
                  required
                  minLength={8}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="lg"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                  <span className="sr-only">
                    {showNewPassword ? "Hide password" : "Show password"}
                  </span>
                </Button>
              </div>
             
              {/* Password strength indicator */}
              {passwordForm.newPassword && (
                <div className="mt-2">
                  <div className="flex items-center gap-1 mb-1">
                    {Array.from({ length: 4 }).map((_, i) => (
                      <div
                        key={i}
                        className={`h-1.5 flex-1 rounded-full ${
                          i < passwordStrength
                            ? passwordStrength === 1
                              ? "bg-red-500"
                              : passwordStrength === 2
                              ? "bg-yellow-500"
                              : passwordStrength === 3
                              ? "bg-green-400"
                              : "bg-green-600"
                            : "bg-gray-200"
                        }`}
                      />
                    ))}
                  </div>
                  <p className="text-xs text-gray-500">
                    {passwordStrength === 0 && "Very weak"}
                    {passwordStrength === 1 && "Weak"}
                    {passwordStrength === 2 && "Medium"}
                    {passwordStrength === 3 && "Strong"}
                    {passwordStrength === 4 && "Very strong"}
                  </p>
                </div>
              )}
             
              <p className="text-xs text-gray-500 mt-1">
                Password must be at least 8 characters and include uppercase, number, and special character
              </p>
            </div>
           
            <div className="space-y-2">
              <label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                Confirm New Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={passwordForm.confirmPassword}
                  onChange={handlePasswordInputChange}
                  className="w-full pl-10 pr-10 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="••••••••"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="lg"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                  <span className="sr-only">
                    {showConfirmPassword ? "Hide password" : "Show password"}
                  </span>
                </Button>
              </div>
              {passwordForm.newPassword &&
               passwordForm.confirmPassword &&
               passwordForm.newPassword !== passwordForm.confirmPassword && (
                <p className="text-xs text-red-500 mt-1">Passwords do not match</p>
              )}
            </div>
           
            <DialogFooter className="mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsPasswordDialogOpen(false)}
                disabled={isChangingPassword}
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white"
                disabled={
                  isChangingPassword ||
                  !passwordForm.currentPassword ||
                  !passwordForm.newPassword ||
                  !passwordForm.confirmPassword ||
                  passwordForm.newPassword !== passwordForm.confirmPassword ||
                  passwordStrength < 3
                }
              >
                {isChangingPassword ? (
                  <>
                    <span className="animate-spin mr-2">⟳</span>
                    Changing...
                  </>
                ) : (
                  "Change Password"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}




