"use client";
import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { ArrowLeft, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { PencilLine, Save, Trash2 } from "lucide-react";
import { toast } from "react-hot-toast";
import { blogService, BlogPost } from "@/services/blog.service";
import { format } from "date-fns";
import Image from "next/image";
import { unknown } from "zod";

interface NutritionalInfo {
  calories: string;
  protein: string;
  carbs: string;
  fat: string;
  fiber: string;
}
interface ExtendedBlogPost extends Omit<BlogPost, 'nutritionalInfo'> {
  nutritionalInfo?: NutritionalInfo;
}

export default function BlogDetail() {
  const params = useParams();
  const router = useRouter();
  const [blog, setBlog] = useState<ExtendedBlogPost | null>(null);
  const [relatedBlogs, setRelatedBlogs] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [notes, setNotes] = useState<string>("");
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [savedNotes, setSavedNotes] = useState<string>("");

  // Fetch blog from API
  useEffect(() => {
    const fetchBlog = async () => {
      try {
        setLoading(true);
        const blogId = params.id as string;

        const blogData = await blogService.getBlogById(blogId);
        // Transform nutritionalInfo to match ExtendedBlogPost
        const transformedBlog: ExtendedBlogPost = {
          ...blogData,
          nutritionalInfo: blogData.nutritionalInfo
            ? {
                calories: String(blogData.nutritionalInfo.calories),
                protein: String(blogData.nutritionalInfo.protein),
                carbs: String(blogData.nutritionalInfo.carbs),
                fat: String(blogData.nutritionalInfo.fat),
                fiber: String(blogData.nutritionalInfo.fiber),
              }
            : undefined,
        };
        setBlog(transformedBlog);
        console.log("blogData", blogData);
        setError(null);

        // Fetch related blogs based on category
        if (blogData.category) {
          const relatedResponse = await blogService.getRelatedBlogs({
            category: blogData.category,
            excludeId: blogId,
          });
          console.log("relatedResponse", relatedResponse);
         setRelatedBlogs(relatedResponse as unknown as any);

        }
      } catch (err) {
        console.error("Error fetching blog:", err);
        setError("Failed to load the nutrition guide. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchBlog();
    }
  }, [params.id]);

  // Load saved notes from localStorage
  useEffect(() => {
    if (typeof window !== "undefined" && params.id) {
      const savedNotes = localStorage.getItem(`nutrition-notes-${params.id}`);
      if (savedNotes) {
        setSavedNotes(savedNotes);
      }
    }
  }, [params.id]);

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMMM d, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  const handleSaveNotes = () => {
    if (typeof window !== "undefined" && params.id) {
      if (!notes.trim()) {
        toast("Please add some content to your notes.");
        return;
      }

      localStorage.setItem(`nutrition-notes-${params.id}`, notes);
      setSavedNotes(notes);
      setIsEditingNotes(false);
      toast("Your notes have been saved successfully.");
    }
  };

  const handleDeleteNotes = () => {
    if (typeof window !== "undefined" && params.id) {
      localStorage.removeItem(`nutrition-notes-${params.id}`);
      setNotes("");
      setSavedNotes("");
      setIsEditingNotes(false);
      toast("Your notes have been deleted.");
    }
  };

  const startEditingNotes = () => {
    setNotes(savedNotes);
    setIsEditingNotes(true);
  };

  // Improved content rendering with list support
  const renderContent = (content: string) => {
    return content.split('\n').map((paragraph, index) => {
      if (!paragraph.trim()) return null;

      // Check for unordered list items
      if (paragraph.trim().startsWith('- ') || paragraph.trim().startsWith('* ')) {
        return (
          <ul key={index} className="list-disc pl-6 mb-4 space-y-1 text-slate-700 dark:text-slate-100">
            <li>{paragraph.substring(2)}</li>
          </ul>
        );
      }

      // Check for ordered list items
      if (/^\d+\.\s/.test(paragraph.trim())) {
        return (
          <ol key={index} className="list-decimal pl-6 mb-4 space-y-1 text-slate-700">
            <li>{paragraph.substring(paragraph.indexOf('.') + 2)}</li>
          </ol>
        );
      }

      // Check for headings
      if (paragraph.startsWith('## ')) {
        return <h3 key={index} className="text-xl font-semibold mt-6 mb-3 text-slate-800">{paragraph.substring(3)}</h3>;
      }

      if (paragraph.startsWith('### ')) {
        return <h4 key={index} className="text-lg font-medium mt-5 mb-2 text-slate-800">{paragraph.substring(4)}</h4>;
      }

      // Regular paragraph with improved visibility in dark mode
      return <p key={index} className="mb-4 leading-relaxed text-slate-700">{paragraph}</p>;
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto py-16 px-4 md:px-6 text-center dark:bg-slate-900">
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm p-12 border border-slate-100 dark:border-slate-700">
          <p className="text-slate-600 dark:text-slate-300">Loading nutrition guide...</p>
        </div>
      </div>
    );
  }

  if (error || !blog) {
    return (
      <div className="container mx-auto py-16 px-4 md:px-6 text-center dark:bg-slate-900">
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm p-12 border border-slate-100 dark:border-slate-700">
          <h2 className="text-2xl font-bold text-slate-800 dark:text-white mb-4">
            Nutrition Guide Not Found
          </h2>
          <p className="text-slate-600 dark:text-slate-300 mb-8">
            {error || "The guide you're looking for doesn't exist or has been removed."}
          </p>
          <Button
            onClick={() => router.push("/userDashboard/learn-dite")}
            className="dark:bg-blue-600 dark:text-white dark:hover:bg-blue-700"
          >
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Nutrition Guides
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <Button
        variant="ghost"
        className="mb-6 pl-0 text-slate-600 hover:text-slate-900"
        onClick={() => router.push("/userDashboard/learn-dite")}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Nutrition Guides
      </Button>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="mb-6">
            <Badge variant="outline" className="mb-3">
              {blog.category}
            </Badge>
            <h1 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4">
              {blog.title}
            </h1>

            <div className="flex flex-wrap items-center gap-4 text-sm text-slate-600 mb-6">
              <div className="flex items-center">
                <Calendar className="mr-2 h-4 w-4" />
                {formatDate(blog.createdAt)}
              </div>
            </div>
          </div>

          <div className="rounded-lg overflow-hidden mb-8">
            <Image
              src={blog.featuredImage || "/blog-placeholder.png"}
              alt={blog.title}
              width={800}
              height={450}
              className="w-full h-auto object-cover"
              priority
            />
          </div>

          <div className="prose prose-slate max-w-none">
            {renderContent(blog.content)}
          </div>

          {blog.nutritionalInfo && (
            <div className="mt-8">
              <h2 className="text-2xl font-bold text-slate-800 mb-4">Nutritional Information</h2>
              <Card>
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-slate-600">Calories</span>
                        <span className="font-medium">{blog.nutritionalInfo.calories}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between">
                        <span className="text-slate-600">Protein</span>
                        <span className="font-medium">{blog.nutritionalInfo.protein}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between">
                        <span className="text-slate-600">Carbohydrates</span>
                        <span className="font-medium">{blog.nutritionalInfo.carbs}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between">
                        <span className="text-slate-600">Fat</span>
                        <span className="font-medium">{blog.nutritionalInfo.fat}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between">
                        <span className="text-slate-600">Fiber</span>
                        <span className="font-medium">{blog.nutritionalInfo.fiber}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-4xl font-bold text-primary mb-2">
                          {blog.nutritionalInfo.calories}
                        </div>
                        <div className="text-sm text-slate-500">calories per serving</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        <div className="lg:col-span-1">
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold dark:bg-slate-800">
                  My Notes
                </h3>
                {!isEditingNotes && savedNotes && (
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={startEditingNotes}
                    >
                      <PencilLine className="h-4 w-4 mr-1.5" /> Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={handleDeleteNotes}
                      className="text-red-500 hover:text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4 mr-1.5" /> Delete
                    </Button>
                  </div>
                )}
              </div>

              {isEditingNotes ? (
                <div className="space-y-4">
                  <Textarea
                    placeholder="Add your personal notes about this nutrition guide..."
                    className="min-h-[180px] text-sm"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                  />
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => setIsEditingNotes(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="lg"
                      onClick={handleSaveNotes}
                      className="gap-1.5"
                    >
                      <Save className="h-4 w-4" /> Save Notes
                    </Button>
                  </div>
                </div>
              ) : (
                <div>
                  {savedNotes ? (
                    <div className="text-slate-700 whitespace-pre-wrap bg-slate-50 p-4 rounded-md border border-slate-100 text-sm">
                      {savedNotes}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 px-4 bg-slate-50 rounded-md border border-dashed border-slate-200">
                      <p className="text-slate-500 text-center mb-3">
                        No notes yet for this nutrition guide
                      </p>
                      <Button
                        variant="outline"
                        size="lg"
                        onClick={startEditingNotes}
                      >
                        <PencilLine className="h-4 w-4 mr-1.5" /> Add Notes
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {relatedBlogs.length > 0 && (
            <Card className="dark:bg-slate-800 dark:border-slate-700">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-slate-800 dark:text-white mb-4">
                  Related Guides
                </h3>
                <div className="space-y-4">
                  {relatedBlogs.map((relatedBlog) => (
                    <div
                      key={relatedBlog.blogId}
                      className="flex gap-3 pb-4 border-b border-slate-100 dark:border-slate-700 last:border-0 last:pb-0"
                    >
                      <div className="flex-shrink-0 w-16 h-16 rounded-md overflow-hidden">
                        <Image
                          src={relatedBlog.featuredImage || "/blog-placeholder.png"}
                          alt={relatedBlog.title}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-slate-800 dark:text-white line-clamp-2 text-sm">
                          {relatedBlog.title}
                        </h4>
                        <p className="text-xs text-slate-500 dark:text-slate-300 mt-1">
                          {formatDate(relatedBlog.createdAt)}
                        </p>
                        <Button
                          variant="link"
                          className="p-0 h-auto text-xs mt-1 text-primary dark:text-blue-300 hover:dark:text-blue-200"
                          onClick={() =>
                            router.push(
                              `/userDashboard/learn-dite/${relatedBlog.blogId}`
                            )
                          }
                        >
                          Read More
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
