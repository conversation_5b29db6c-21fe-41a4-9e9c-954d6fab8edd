import type React from "react";
import type { Metadata } from "next";
import Sidebar from "@/components/user/sidebar";
import Header from "@/components/user/header";
import Footer from "@/components/user/footer1";

export const metadata: Metadata = {
  title: "NutriFocus",
  description: "AI Assisted Ethiopian Nutritional Guide Assistant",
};

export default function UserDashboardLayout({
  children,
}: Readonly<{ 
  children: React.ReactNode;
}>) {
  return (
    <div className={`flex h-screen overflow-hidden bg-[#CCC9DA]`}>
      <div className="z-50">
        <Sidebar />
      </div>

      <div className="flex-1 flex flex-col h-screen">
        <div className="fixed top-0 left-0 md:left-60 right-0 h-16 z-40 bg-white shadow-md">
          <Header />
        </div>
        <main className="flex-1 overflow-y-auto p-4 pt-40 md:pt-24 bg-gradient-to-b from-background to-muted/70">
          {children}
        </main>
          <div className="bg-white shadow-inner">
            <Footer />
          </div>
      </div>
    </div>
  );
}
