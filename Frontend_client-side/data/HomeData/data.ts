// This file consolidates all the data used across components
import img1 from "@/app/assets/images/image_fx_ (4) 1.svg";
import img2 from "@/app/assets/images/image_fx_ (5) 1.svg";
import light_logo from "@/app/assets/images/light-logo.svg"
import dark_logo from "@/app/assets/images/dark-logo.svg"
import feature_img from "@/app/assets/images/Battle.net.svg"
// import feat2 from "@/app/assets/images/Battle.net.svg";

export const heroData = [
    {
      id: 1,
      title:
        "AI-Assisted Ethiopian Nutritional Guide",
      sub_title: "Smart Eating and Health Monitoring System.",
      hero_title: "Achieve Your Health Goals with Precision and Ease.",
      hero_desc:
        "Personalized nutrition and fitness plans tailored just for you. Track progress, stay motivated, and thrive every step of the way.",
      about_desc:
        "The AI-Assisted Ethiopian Nutritional Guide: Smart Eating and Health Monitoring combines advanced AI with traditional Ethiopian nutrition to offer personalized, data-driven meal plans. Our platform helps users make healthier food choices, manage health, and track nutrition in a culturally relevant way. With expert guidance and smart monitoring, we aim to empower Ethiopians to live healthier lives, one meal at a time.",
      hero: img1,
      about: img2,
      logo1: light_logo,
      logo2: dark_logo,
    },
  ];
  
  export const features = [
    {
      title: "Smart Analytics",
      desc: "Gain valuable insights with our advanced analytics tools that help you make data-driven decisions.",
      img: feature_img,
    },
    {
      title: "Seamless Integration",
      desc: "Connect with your favorite tools and services without any hassle or technical complexity.",
      img: feature_img,
    },
    {
      title: "Automated Workflows",
      desc: "Save time and reduce errors with our powerful automation capabilities.",
      img: feature_img,
    },
    {
      title: "Secure Platform",
      desc: "Rest easy knowing your data is protected with enterprise-grade security measures.",
      img: feature_img,
    },
    {
      title: "Responsive Design",
      desc: "Access your dashboard and tools from any device with our fully responsive interface.",
      img: feature_img,
    },
    {
      title: "24/7 Support",
      desc: "Get help whenever you need it with our dedicated support team available around the clock.",
      img: feature_img,
    },
  ]
  
  export const faqItems = [
    {
      question: "What is the AI-Assisted Ethiopian Nutritional Guide?",
      answer:
        "The AI-Assisted Ethiopian Nutritional Guide is a web-based platform that provides personalized dietary recommendations tailored to Ethiopian cuisine. It helps users track their caloric and nutrient intake, set health goals, and monitor progress using a comprehensive database of Ethiopian foods. The system leverages machine learning to offer culturally relevant meal plans and nutritional guidance.",
      value: "item-1",
    },
    {
      question: "How does the system provide personalized recommendations?",
      answer:
        "Users input health metrics such as age, weight, height, activity level, dietary preferences, health issues, and weight goals. The systems machine learning model (Random Forest Regressor) analyzes these inputs to predict daily caloric and macronutrient needs (e.g., proteins, fats, carbohydrates). It then recommends meals from a database of 355 Ethiopian foods, considering factors like regional preferences, fasting status, and health conditions.",
      value: "item-2",
    },
    {
      question: "What types of Ethiopian foods are included in the database?",
      answer:
        "The database contains 355 Ethiopian dishes, including staples like injera, stews, legumes, and vegetables, with detailed nutritional profiles (calories, proteins, fats, carbohydrates). It accounts for regional variations in preparation methods and is validated by Ethiopian nutritionists and culinary experts to ensure cultural accuracy..",
      value: "item-3",
    },
    {
      question: "How do I access the platform?",
      answer:
        "The platform is web-based and accessible on desktops, tablets, and smartphones. Future plans include a responsive, user-friendly interface with support for Amharic, Oromifaa, and Tigrigna to ensure cultural and linguistic accessibility. ",
      value: "item-4",
    },
    {
      question: "How can I contribute to the food database?",
      answer:
        "Users can suggest recipes or nutritional data through the platforms crowdsourcing feature. Contributions are validated by nutrition experts to ensure accuracy and cultural relevance before inclusion in the database.",
      value: "item-5",
    },
    {
      question: "Do you offer custom solutions for enterprises?",
      answer:
        "We provide tailored enterprise solutions with dedicated account managers, custom integrations, and specialized training for your team.",
      value: "item-6",
    },
  ]
  