import img1 from "@/app/assets/images/image_fx_ (4) 1.svg";
import img2 from "@/app/assets/images/image_fx_ (5) 1.svg";
import logo from "@/app/assets/images/light-logo.svg"
import feat2 from "@/app/assets/images/Battle.net.svg";
export const Hero = [
  {
    id: 1,
    title:
      "AI-Assisted Ethiopian Nutritional Guide: Smart Eating and Health Monitoring System.",
    hero_title: "Achieve Your Health Goals with Precision and Ease.",
    hero_desc:
      "Personalized nutrition and fitness plans tailored just for you. Track progress, stay motivated, and thrive every step of the way.",
    about_desc:
      "The AI-Assisted Ethiopian Nutritional Guide: Smart Eating and Health Monitoring combines advanced AI with traditional Ethiopian nutrition to offer personalized, data-driven meal plans. Our platform helps users make healthier food choices, manage health, and track nutrition in a culturally relevant way. With expert guidance and smart monitoring, we aim to empower Ethiopians to live healthier lives, one meal at a time.",
    hero: img1,
    about: img2,
    logo1: logo,
  },
];
export default Hero
export const features = [
  {
    id: 1,
    title: "AI-Assisted Nutritional Guidance",
    desc: "Get personalized meal plans and nutritional advice powered by AI to help you make smarter, healthier food choices.",
    img: feat2,
  },
  {
    id: 2,
    title: "Nutritional Details of Ethiopian Food",
    desc: "Access detailed nutritional information for traditional Ethiopian dishes, including ingredients, calories, and health benefits.",
    img: feat2,
  },
  {
    id: 3,
    title: "Expert Nutrition Blog",
    desc: "Learn more about nutrition with expert insights and articles, helping you stay informed and make better dietary decisions.",
    img: feat2,
  },
  {
    id: 4,
    title: "User-Friendly UI",
    desc: "Enjoy an intuitive, easy-to-navigate platform designed for a seamless user experience.",
    img: feat2,
  },
  {
    id: 5,
    title: "Nutritional Details of Ethiopian Food",
    desc: "Access detailed nutritional information for traditional Ethiopian dishes, including ingredients, calories, and health benefits.",
    img: feat2,
  },
  {
    id: 5,
    title: "Nutritional Details of Ethiopian Food",
    desc: "Access detailed nutritional information for traditional Ethiopian dishes, including ingredients, calories, and health benefits.",
    img: feat2,
  },
];
export const faqItems = [
  {
    value: "item-1",
    question: "What is the AI-Assisted Ethiopian Nutritional Guide?",
    answer:
      "This system is an AI-powered Ethiopian Nutritional Guide designed to promote smart eating and health monitoring. It provides users with personalized dietary recommendations, tracks health metrics, and offers educational content about Ethiopian food and nutrition.",
  },
  {
    value: "item-2",
    question: "Who can use this system?",
    answer:
      "The AI-Assisted Ethiopian Nutritional Guide is designed for anyone interested in improving their nutrition and health.",
  },
  {
    value: "item-3",
    question: "Can I update my health metrics after registration?",
    answer:
      "Yes! You can update your health metrics (weight, BMI, etc.) anytime after registration to receive updated AI-driven meal recommendations and health insights.",
  },
  {
    value: "item-4",
    question: "How do I log my food intake?",
    answer:
      "You can log your food intake by selecting meals from the database or manually entering foods along with portion sizes. The system will track your calories and nutrients automatically.",
  },
  {
    value: "item-5",
    question: "How are recommendations generated?",
    answer:
      "Recommendations are generated using AI based on your health metrics, dietary preferences, and goals. The system analyzes your age, weight, activity level, and nutritional needs to provide personalized meal plans and insights.",
  },
  {
    value: "item-6",
    question: "What devices can I use to access the system?",
    answer:
      "You can access the system on any device with an internet connection, including smartphones, tablets, laptops, and desktop computers. It is optimized for both mobile and desktop use for a seamless experience.",
  },
  {
    value: "item-7",
    question: "How do I reset my password?",
    answer:
      "You can reset your password by clicking the 'Forgot Password' link on the login page and following the instructions sent to your registered email.",
  },
  {
    value: "item-8",
    question: "Is there a mobile app available?",
    answer:
      "Currently, our service is web-based, but we are actively working on a mobile app that will be available in both Android and iOS platforms soon.",
  },
  {
    value: "item-7",
    question: "How do I reset my password?",
    answer:
      "You can reset your password by clicking the 'Forgot Password' link on the login page and following the instructions sent to your registered email.",
  },
  {
    value: "item-8",
    question: "Is there a mobile app available?",
    answer:
      "Currently, our service is web-based, but we are actively working on a mobile app that will be available in both Android and iOS platforms soon.",
  },
];

