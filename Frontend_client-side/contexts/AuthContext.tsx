'use client';

import { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { authService } from '@/services/auth.service';
import { useRouter, usePathname } from 'next/navigation';
import { any } from 'zod';

interface AuthContextType {
  user: any | null;
  loading: boolean;
  error: string | null;
  login: (credentials: { email: string; password: string }) => Promise<any>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const PUBLIC_PATHS = [
  '/login', 
  '/signup', 
  '/adminLogin', 
  '/adminSignup',
  '/superAdminLogin',
  '/superAdminSignup',
  '/forgot-password', 
  '/reset-password',
  '/'
];

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const pathname = usePathname();

  const checkAuth = useCallback(async () => {
    // Skip auth check for public pages
    if (PUBLIC_PATHS.includes(pathname)) {
      setLoading(false);
      return;
    }

    try {
      const response = await authService.getCurrentUser();
      setUser(response);
      setError(null);
      
      // Only redirect if user is authenticated and on login/signup pages
      if (response && (pathname === '/login' || pathname === '/signup' || 
          pathname === '/adminLogin' || pathname === '/adminSignup' ||
          pathname === '/superAdminLogin' || pathname === '/superAdminSignup')) {
        router.push('/userDashboard');
      }
    } catch (err) {
      setUser(null);
      setError('Not authenticated');
      
      // Only redirect to login if not on a public path and not already on login/signup
      if (!PUBLIC_PATHS.includes(pathname) && !pathname.includes('/login') && !pathname.includes('/signup')) {
        router.push('/login');
      }
    } finally {
      setLoading(false);
    }
  }, [pathname, router]);

  const login = async (credentials: { email: string; password: string }) => {
    setLoading(true);

    try {
      const response = await authService.userLogin(credentials);
      console.log('Login response:', response);

      // If the response does not contain an accessToken, treat as error
      if (!response.accessToken) {
        throw new Error(response.message || 'Login failed');
      }

      await checkAuth();
      return response;
    } catch (err: any) {
      setError(err.message || 'Login failed');
      setLoading(false);
      throw err;
    }
  };

  const logout = async () => {
    setLoading(true);
    try {
      await authService.logout();
      setUser(null);
      setError(null);
      router.push('/login');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Logout failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const value = {
    user,
    loading,
    error,
    login,
    logout,
    checkAuth,
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
}
