# Stage 1: Build the application
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm install

# Copy app source code and .env file
COPY . .
COPY .env ./

# Build the app (Next.js will load .env automatically)
RUN npm run build

# Stage 2: Run the app
FROM node:20-alpine AS runner

WORKDIR /app

# Copy necessary files from builder
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/.env ./.env    

# Expose the port Next.js runs on
EXPOSE 3000

# Start the Next.js app (Next.js will load .env automatically)
CMD ["npm", "start"]
