import { z } from "zod";

export const HealthMetricsSchema = z.object({
  age: z.number().int().min(18).max(79),
  weight: z.number().min(48).max(119),
  height: z.number().min(150).max(200),
  gender: z.enum(["Male", "Female"]),
  activity_level: z.enum(["Sedentary", "Lightly Active", "Moderately Active", "Very Active"]),
  dietary_preference: z.enum(["Omnivore", "Vegetarian", "Vegan"]),
  weight_goal: z.enum(["Lose Weight", "Maintain Weight", "Gain Weight"]),
  health_issues: z.array(z.enum(["None", "Diabetes", "Hypertension", "Heart Disease", "Kidney Disease", "Acne"])),
  fasting: z.enum(["Yes", "No"]),
  location: z.string().min(1),
});


export type HealthMetrics = z.infer<typeof HealthMetricsSchema>;