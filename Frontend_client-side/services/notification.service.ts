import { api } from './api';

export interface Notification {
  id: string;
  from: string;
  message: string;
  status: "read" | "unread";
  createdAt: string;
  type: "info" | "success" | "warning" | "error";
  category: string;
}

// Map backend notification to frontend format
const mapNotification = (notification: any): Notification => {
  return {
    id: notification.notificationId,
    from: notification.from || 'System',
    message: notification.message,
    status: notification.isRead ? "read" : "unread",
    createdAt: notification.timestamp || notification.createdAt,
    type: notification.type || "info", // Use type directly from backend
    category: notification.category || 'System',
  };
};

// No need for mapNotificationType function anymore since we're using the types directly

const notificationService = {
  // Get all notifications
  getNotifications: async (page = 1, limit = 20, isRead?: boolean, category?: string): Promise<{
    data: Notification[];
    meta: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }> => {
    try {
      let url = `/notifications?page=${page}&limit=${limit}`;

      if (isRead !== undefined) {
        url += `&isRead=${isRead}`;
      }
      
      if (category && category !== 'all') {
        url += `&category=${category}`;
      }
      
      const response = await api.get(url);
      
      // If the response doesn't have the expected structure, handle it gracefully
      if (!response.data || !response.data.data) {
        console.error('Unexpected response format:', response.data);
        return {
          data: [],
          meta: {
            total: 0,
            page: page,
            limit: limit,
            totalPages: 1,
          },
        };
      }
      
      return {
        data: response.data.data.map(mapNotification),
        meta: response.data.meta || {
          total: response.data.data.length,
          page: page,
          limit: limit,
          totalPages: 1,
        },
      };
    } catch (error: any) {
      // Handle rate limiting (429) gracefully
      if (error.response && error.response.status === 429) {
        console.error('Rate limit exceeded. Please try again later.');
        return {
          data: [],
          meta: {
            total: 0,
            page: page,
            limit: limit,
            totalPages: 1,
          },
        };
      }
      console.error('Failed to fetch notifications:', error);
      // Return empty data instead of throwing
      return {
        data: [],
        meta: {
          total: 0,
          page: page,
          limit: limit,
          totalPages: 1,
        },
      };
    }
  },

  getNotificationCounts: async (): Promise<{
    all: number;
    unread: number;
    read: number;
  }> => {
    try {
      const response = await api.get('/notifications/counts');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch notification counts:', error);
      return {
        all: 0,
        unread: 0,
        read: 0,
      };
    }
  },
  
  // Mark a notification as read
  markAsRead: async (id: string): Promise<void> => {
    try {
      await api.post(`/notifications/${id}/mark-read`);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      throw error;
    }
  },
  
  // Get unread count
  getUnreadCount: async (): Promise<number> => {
    try {
      const response = await api.get('/notifications/unread-count');
      return response.data.unreadCount;
    } catch (error) {
      console.error('Failed to get unread count:', error);
      return 0;
    }
  },
  
  // Mark all notifications as read
  markAllAsRead: async (): Promise<void> => {
    try {
      await api.post('/notifications/mark-all-read');
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      throw error;
    }
  },
  
  // Delete a notification
  deleteNotification: async (id: string): Promise<void> => {
    try {
      await api.delete(`/notifications/${id}`);
    } catch (error) {
      console.error('Failed to delete notification:', error);
      throw error;
    }
  },
};

export default notificationService;
