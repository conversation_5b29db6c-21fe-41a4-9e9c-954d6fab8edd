import { AxiosError } from "axios";
import { api } from "./api";
import { authService } from "./auth.service";
interface PaginatedResponse<T> {
  admins: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface Admin {
  adminId: string;
  name: string;
  email: string;
  phone?: string;
  status: string;
  profilePicture?: string;
  createdAt: string;
  updatedAt: string;
}

interface ErrorResponse {
  message: string;
  statusCode?: number;
}

interface GetAdminsParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
}

export enum AdminStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  DELETED = 'deleted',
  PENDING = 'pending',
}

export const adminService = {

  async createAdmin(adminData: {
    name: string;
    email: string;
    password: string;
    status: string;
  }): Promise<Admin> {
    try {
      const response = await api.post('/admins/create', adminData);
      return response.data;
    }
    catch(error)
    {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  },

    async getAllAdmins(params: GetAdminsParams = {}): Promise<PaginatedResponse<Admin>> {
        try {
          const { page = 1, limit = 10, search = "", status = "" } = params;
          
          // Build query parameters
          const queryParams = new URLSearchParams();
          queryParams.append('page', page.toString());
          queryParams.append('limit', limit.toString());
          
          if (search) {
            queryParams.append('search', search);
          }
          
          if (status) {
            queryParams.append('status', status);
          }
                
          const response = await api.get(`/admins?${queryParams.toString()}`)
          
          if (response.data && response.data.admins && Array.isArray(response.data.admins)) {
            return response.data;
          } else {
            console.error("Unexpected API response format:", response.data);
    
            return {
              admins: [],
              meta: {
                total: 0,
                page: 1,
                limit: 10,
                totalPages: 1
              }
            };
          }
        } catch (error) {
          const axiosError = error as AxiosError<ErrorResponse>;
          console.error("Error in getAllUsers:", axiosError);
          
          return {
            admins: [],
            meta: {
              total: 0,
              page: 1,
              limit: 10,
              totalPages: 1
            }
          };
        }
      },
    

      async getAdminProfile () {
        try {
          const response = await api.get('/admins/profile');
          return response.data;
        } catch (error) {
          const axiosError = error as AxiosError;
          throw axiosError;
        }
      },

      async deleteAdmin(adminId: string) {
        try {
          const response = await api.delete(`/admins/${adminId}`);
          return response.data;
        } catch (error) {
          const axiosError = error as AxiosError;
          throw axiosError;
        }
      },

      async updateAdminStatus(adminId: string, status: string) {
        try {
          const response = await api.patch(`/admins/status/${adminId}`, { status });
          return response.data;
        } catch (error) {
          const axiosError = error as AxiosError;
          throw axiosError;
        }
      },

      async updateAdmin(adminId: string, data: any) {
        try {
          const response = await api.put(`/admins/${adminId}`, data);
          return response.data;
        } catch (error) {
          const axiosError = error as AxiosError;
          throw axiosError;
        }
      },
}