import { <PERSON><PERSON><PERSON> } from "lucide-react";
import { api } from "./api";
import { authService } from "./auth.service";

interface AdminData {
  name: string;
  email: string;
  password: string;
  role?: string;
}

export interface SuperAdmin {
  superAdminId: string;
  name: string;
  email: string;
  phone?: string;
  status: string;
  profilePicture?: string;
  createdAt: string;
  updatedAt: string;
}

interface AdminResponse {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

interface UserByIdResponse {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

interface UpdateUserData {
  name?: string;
  email?: string;
  password?: string;
  role?: string;
}

interface UpdateUserResponse {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

interface AdminByIdResponse {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

interface UpdateAdminData {
  name?: string;
  email?: string;
  password?: string;
  role?: string;
}

interface UpdateAdminResponse {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

interface DeleteAdminResponse {
  message: string;
}

interface DeleteUserResponse {
  message: string;
}

// User Management
interface UserData {
  name: string;
  email: string;
  password: string;
  role?: string;
}

interface UserResponse {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

// Admin Management
export const createAdmin = async (
  adminData: AdminData
): Promise<AdminResponse> => {
  console.log(adminData);
  const response = await api.post<AdminResponse>(`/admins/create`, adminData);
  return response.data;
};

export interface SuperAdminProfile {
  id: string;
  name: string;
  email: string;
  profilePicture?: string | null;
  location: string;
  createdAt: string;
  updatedAt: string;
  phone?: string | null;
  status: string;
  emailVerified: boolean;
}

export const superAdminService = {
  async getSuperAdminProfile(): Promise<SuperAdminProfile> {
    const response = await api.get('/super-admins/profile');
    return response.data;
  },

  async updateProfile(data: Partial<SuperAdminProfile>): Promise<SuperAdminProfile> {
    const admin = await authService.checkUser();

    console.log('Updating profile with data:', admin);
    const response = await api.put(`/super-admins/${admin.id}`, data);
    return response.data;
  },

  async uploadProfileImage(file: File): Promise<{ profilePicture: string }> {
    const formData = new FormData();
    formData.append('profileImage', file);
    
    const response = await api.post('/super-admins/profile-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  }
};

export const getAllAdmins = async () => {
  const response = await api.get(`/admins`);
  return response.data;
};

export const getAdminById = async (
  adminId: string
): Promise<AdminByIdResponse> => {
  const response = await api.get<AdminByIdResponse>(`/admins/${adminId}`);
  return response.data;
};

export const updateAdmin = async (
  adminId: string,
  updateData: UpdateAdminData
): Promise<UpdateAdminResponse> => {
  const response = await api.put<UpdateAdminResponse>(
    `/admins/${adminId}`,
    updateData
  );
  return response.data;
};

export const deleteAdmin = async (
  adminId: string
): Promise<DeleteAdminResponse> => {
  const response = await api.delete<DeleteAdminResponse>(`/admins/${adminId}`);
  return response.data;
};

export const getAllUsers = async () => {
  const response = await api.get(`/users`);
  return response.data;
};

export const getUserById = async (
  userId: string
): Promise<UserByIdResponse> => {
  const response = await api.get<UserByIdResponse>(`/users/${userId}`);
  console.log(response.data);
  return response.data;
};

export const updateUser = async (
  userId: string,
  updateData: UpdateUserData
): Promise<UpdateUserResponse> => {
  const response = await api.put<UpdateUserResponse>(
    `/users/${userId}`,
    updateData
  );
  return response.data;
};

export const deleteUser = async (
  userId: string
): Promise<DeleteUserResponse> => {
  const response = await api.delete<DeleteUserResponse>(`/users/${userId}`);
  return response.data;
};
