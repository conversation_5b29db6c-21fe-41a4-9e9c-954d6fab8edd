import type { CredentialResponse } from "@react-oauth/google";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";

function useGoogleAuthentication() {
  const router = useRouter();
  const API_URL = process.env.NEXT_PUBLIC_API_URL;

  const handleSuccess = (response: CredentialResponse, isSignup: boolean) => {
    if (response.credential) {
      const credential = response.credential;
      const endpoint = isSignup ? "signup" : "signin";
      fetch(`${API_URL}/google/${endpoint}`, {
        method: "POST",
        body: JSON.stringify({ token: credential}),
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // This ensures cookies are sent with the request
      })
        .then((res) => res.json()) // Handle the response
        .then((data) => {
          console.log(data);
          if (data.redirectUrl) {
            if (isSignup) {
              toast.success("Sign-up successful!");
            }
            else {
              toast.success("Sign-in successful!");
            }
            router.push(data.redirectUrl);
          } else if (data.message) {
            toast.error(data.message);
          } else {
            toast.error("Sign-in successful, but no redirect URL found. Please try again.");
          }
        })
        .catch((error) => {
          toast.error("An error occurred. Please try again.");
          console.log(error.message);
        });
    }
  };

  return { handleSuccess };
}

export default useGoogleAuthentication;