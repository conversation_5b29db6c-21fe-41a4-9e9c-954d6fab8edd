import { api } from "./api";

export interface Feedback {
  id: string;
  message: string;
  feedbackType: string;
  rating: number;
  adminResponse: string | null;
  createdAt: string;
}

export async function getFeedback(page = 1, limit = 10) {
  try {
    const response = await api.get(`/feedback?page=${page}&limit=${limit}`);
    return response;

  } catch (error) {
    console.error("Failed to fetch feedback:", error);
    return [];
  }
}

export async function getUserFeedback(page = 1, limit = 6) {
  try {
    const response = await api.get(`/feedback/user?page=${page}&limit=${limit}`);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch user feedback:", error);
    return [];
  }
}

export async function createFeedback(feedbackData: {
  message: string;
  feedbackType: string;
  rating: number;
}) {
  try {
    const response = await api.post("/feedback", feedbackData);
    return response;
  } catch (error) {
    console.error("Failed to create feedback:", error);
    return null;
  }
}
 

export async function deleteFeedback(feedbackId: string) {
  try {
    const response = await api.delete(`/feedback/${feedbackId}`);
    return response.data;
  } catch (error) {
    console.error("Failed to delete feedback:", error);
    return null;
  }
}
