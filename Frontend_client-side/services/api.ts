import axios, { AxiosRequestConfig } from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

console.log(`API_BASE_URL: ${API_BASE_URL}`);
let isRefreshing = false;
let failedQueue: Array<(token: string | null) => void> = [];

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  withCredentials: true,
  timeout: 30000,
  validateStatus: function (status) {
    return status >= 200 && status < 500; // Accept all status codes less than 500
  }
});

const refreshAccessToken = async (): Promise<string | null> => {
  try {
    const response = await axios.get<{ accessToken: string }>(
      `${API_BASE_URL}/auth/refresh`,
      { withCredentials: true }
    );
    return response.data.accessToken;
  } catch {
    return null;
  }
};

api.interceptors.request.use(
  (config) => {
    if (config.method === 'get' && config.url?.includes('/auth/')) {
      config.params = { ...config.params, _t: Date.now() };
    }
    return config;
  },
  (error) => Promise.reject(error)
);

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !originalRequest.url?.includes('/auth/')
    ) {
      if (isRefreshing) {
        return new Promise((resolve) => {
          failedQueue.push((token) => {
            resolve(api(originalRequest));
          });
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const newAccessToken = await refreshAccessToken();
        if (newAccessToken) {
          failedQueue.forEach((cb) => cb(newAccessToken));
          failedQueue = [];
          return api(originalRequest);
        }
        
        if (typeof window !== 'undefined' && 
            !window.location.pathname.includes('/login') && 
            !window.location.pathname.includes('/signup')) {
          const Cookies = require('js-cookie');
          const cookieOptions = {
            domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN,
            path: '/',
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'Lax' as const,
          };

          Cookies.remove('access_token', cookieOptions);
          Cookies.remove('refresh_token', cookieOptions);
          Cookies.remove('user_role', cookieOptions);
          window.location.href = '/login';
        }
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

export { api };