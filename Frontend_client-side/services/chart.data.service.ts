import { api } from "./api";
import { userService } from "./user.service";
import type { HealthMetricsDto, PredictionResponse } from "./user.service";
import type { HealthMetrics } from "@/store/recommendationStore";

export interface CalorieData {
  date: string;
  consumed: number;
  target: number;
}

export interface MacroData {
  name: string;
  value: number;
  color: string;
}

export interface WeeklyProgressData {
  date: string;
  calories: number;
  protein: number;
  carbs: number;
  fats: number;
  weight: number;
}

export interface ActivityData {
  date: string;
  duration: number;
  caloriesBurned: number;
  activityType: string;
}

export interface MLRecommendation {
  category: string;
  recommendation: string;
  confidence: number;
}

export interface RecommendationHistoryItem {
  id: string;
  date: string;
  category: string;
  recommendation: string;
  confidence: number;
  healthMetrics?: {
    weight?: number;
    height?: number;
    age?: number;
    gender?: string;
    activityLevel?: string;
  };
  macroTargets?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    fiber?: number;
  };
}
export interface DailyPlanData {
  [meal: string]: Array<{
    local_name: string;
    serving_nutrition: {
      calories: number;
    };
  }>;
}
// Chart data service that uses the existing API and user service
const chartDataService = {
  getDailyPlanData: async (): Promise<any> => {
  const mockDailyPlanData: DailyPlanData = {
  breakfast: [{ local_name: "Injera", serving_nutrition: { calories: 200 } }],
  lunch: [{ local_name: "Doro Wat", serving_nutrition: { calories: 500 } }],
  dinner: [{ local_name: "Tibs", serving_nutrition: { calories: 400 } }],
  snack: [{ local_name: "Roasted Kolo", serving_nutrition: { calories: 150 } }],
};
    try {
      const response = await api.get(`/recommendations/todays-plan`);
      console.log(response);
      return response.data;
      // return mockDailyPlanData
      return [];
    } catch (error) {
      console.error("Failed to fetch daily plan data:", error);
      // Return placeholder data if API fails
      return [];
    }
  },

  getTodaysTargetCalorie: async () : Promise<any> => {
    try {
      const response = await api.get(`/recommendations/todays-target-calorie`);
      return response.data;
    } catch (error) {
      console.error("Failed to fetch today's target:", error);
      // Return placeholder data if API fails
      return {};
    }
  },

  getWeeklyTargetCalorieData: async (userId: string): Promise<any[]> => {
    try {
      const response = await api.get(`/users/nutrition/weekly-target-calorie-data`);
      return response.data;
    } catch (error) {
      console.error("Failed to fetch weekly target calorie data:", error);
      // Return placeholder data if API fails
      return [
        { day: "Mon", target: 2200 },
        { day: "Tue", target: 2200 },
        { day: "Wed", target: 2200 },
        { day: "Thu", target: 2200 },
        { day: "Fri", target: 2200 },
        { day: "Sat", target: 2200 },
        { day: "Sun", target: 2200 },
      ];
    }
  },

  // Weekly progress data
  getWeeklyProgressData: async (): Promise<WeeklyProgressData[]> => {
    try {
      // Use the existing API to fetch weekly progress data
      const response = await api.get("/user/nutrition/weekly");
      const data = response.data;

      // Transform the API response to match the expected format
      return data.map((day: any) => ({
        date: day.date,
        calories: day.totalCalories || 0,
        protein: day.macros?.protein || 0,
        carbs: day.macros?.carbohydrates || 0,
        fats: day.macros?.fat || 0,
        weight: day.weight || 0,
      }));
    } catch (error) {
      console.error("Failed to fetch weekly progress data:", error);
      // Return placeholder data if API fails
      const today = new Date();
      return Array.from({ length: 7 }, (_, i) => {
        const date = new Date(today);
        date.setDate(date.getDate() - (6 - i));
        return {
          date: date.toISOString().split("T")[0],
          calories: 1800 + Math.floor(Math.random() * 600),
          protein: 80 + Math.floor(Math.random() * 40),
          carbs: 200 + Math.floor(Math.random() * 100),
          fats: 60 + Math.floor(Math.random() * 30),
          weight: 70 - i * 0.1,
        };
      });
    }
  },

  getNutrientData: async (): Promise<any[]> => {
    try {
      // Use the existing API to fetch micronutrient data
      const response = await api.get("/users/nutrition/nutrients");
      const data = response.data;
      console.log(data);
      // Transform the API response to match the expected format
      return [
        { nutrient: "Fiber", amount: data.fiber, color: "#82ca9d", fullMark: 100, unit: "g" },
        { nutrient: "Sodium", amount: data.sodium, color: "#8884d8", fullMark: 100, unit: "mg" },
        { nutrient: "Sugar", amount: data.sugar, color: "#ffc658", fullMark: 100, unit: "g" },
        { nutrient: "Protein", amount: data.protein, color: "#ff8042", fullMark: 100, unit: "g" },
        { nutrient: "Carbohydrates", amount: data.carbohydrate, color: "#0088fe", fullMark: 100, unit: "g" },
        { nutrient: "Fat", amount: data.fat, color: "#00c49f", fullMark: 100, unit: "g" },
      ];
    } catch (error) {
      console.error("Failed to fetch micronutrient data:", error);
      // Return placeholder data if API fails
      return [
        { nutrient: "Sodium", amount: 100, color: "#8884d8" },
        { nutrient: "Fiber", amount: 150, color: "#82ca9d" },
        { nutrient: "Sugar", amount: 200, color: "#ffc658" },
      ];
    }
  },

  // Macronutrient breakdown
  getMacronutrientData: async (): Promise<MacroData[]> => {
    try {
      // Use the existing API to fetch macronutrient data
      const response = await api.get("users/nutrition/macro-nutrients");
      const data = response.data;

      // Calculate percentages based on the macronutrient values
      const protein = data.protein;
      const carbs = data.carbohydrate;
      const fats = data.fat;

      const total = protein + carbs + fats;

      // If we have no data, return placeholder percentages
      if (total === 0) {
        return [
          { name: "Protein", value: 30, color: "#8884d8" },
          { name: "Carbs", value: 45, color: "#82ca9d" },
          { name: "Fats", value: 25, color: "#ffc658" },
        ];
      }

      // Calculate actual percentages
      return [
        {
          name: "Protein",
          value: protein,
          color: "#8884d8",
        },
        {
          name: "Carbs",
          value: carbs,
          color: "#82ca9d",
        },
        {
          name: "Fats",
          value: fats,
          color: "#ffc658",
        },
      ];
    } catch (error) {
      console.error("Failed to fetch macronutrient data:", error);
      // Return placeholder data if API fails
      return [
        { name: "Protein", value: 30, color: "#8884d8" },
        { name: "Carbs", value: 45, color: "#82ca9d" },
        { name: "Fats", value: 25, color: "#ffc658" },
      ];
    }
  },

  // Activity level data
  getActivityData: async (): Promise<ActivityData[]> => {
    try {
      // Use the existing API to fetch activity data
      const response = await api.get("/user/activity/weekly");
      const data = response.data;

      // Transform the API response to match the expected format
      return data.map((day: any) => ({
        date: day.date,
        duration: day.duration || 0,
        caloriesBurned: day.caloriesBurned || 0,
        activityType: day.activityType || "Not specified",
      }));
    } catch (error) {
      console.error("Failed to fetch activity data:", error);
      // Return placeholder data if API fails
      const today = new Date();
      return Array.from({ length: 7 }, (_, i) => {
        const date = new Date(today);
        date.setDate(date.getDate() - (6 - i));
        return {
          date: date.toISOString().split("T")[0],
          duration: Math.floor(Math.random() * 60) + 15,
          caloriesBurned: Math.floor(Math.random() * 400) + 100,
          activityType: ["Walking", "Running", "Cycling", "Swimming", "Gym"][
            Math.floor(Math.random() * 5)
          ],
        };
      });
    }
  },

  // ML recommendations using your existing prediction structure
  getMLRecommendations: async (): Promise<MLRecommendation[]> => {
    try {
      // Get user profile using your existing service
      const userProfile = await userService.getUserProfile();

      // Prepare health metrics from user profile
      const healthMetrics: HealthMetricsDto = {
        age: userProfile.healthMetrics?.age,
        weight: userProfile.healthMetrics?.weight,
        height: userProfile.healthMetrics?.height,
        healthIssues: userProfile.healthMetrics?.healthIssues,
        goals: userProfile.healthMetrics?.goals
      };

      // Call ML API using the environment variable 
      //Todo: this is completely wrong
      const mlApiUrl =
        process.env.NEXT_PUBLIC_ML_API_URL || "https://ml-backend.duckdns.org";
      const response = await api.post(`${mlApiUrl}/predict`, { healthMetrics });
      const prediction: PredictionResponse = response.data;

      // Save the recommendation using your existing service
      await userService.saveRecommendation({
        userData: healthMetrics,
        prediction: prediction,
      });

      // Transform the prediction into recommendations
      const recommendations: MLRecommendation[] = [];

      // Diet recommendation
      const mealTypes = Object.keys(prediction.recommendations);
      if (mealTypes.length > 0) {
        const randomMealType = mealTypes[Math.floor(Math.random() * mealTypes.length)];
        const foods = prediction.recommendations[randomMealType as keyof typeof prediction.recommendations];
        if (foods && foods.length > 0) {
          const topFood = foods[0];
          recommendations.push({
            category: "Diet",
            recommendation: `For ${randomMealType}, consider adding ${topFood.local_name} to your meal plan for optimal nutrition.`,
            confidence: 0.9,
          });
        }
      }

      // Activity recommendation based on nutritional detail
      const calorieTarget = prediction.nutritional_detail.daily_calorie_target;
      recommendations.push({
        category: "Activity",
        recommendation: `Based on your profile, aim for ${calorieTarget} calories daily. Adjust your activity level to meet this target.`,
        confidence: 0.85,
      });

      // Nutrition recommendation based on nutritional detail
      const protein = prediction.nutritional_detail.protein;
      const fiber = prediction.nutritional_detail.fiber;
      recommendations.push({
        category: "Nutrition",
        recommendation: `Your optimal daily intake should include ${protein}g of protein and ${fiber}g of fiber for balanced nutrition.`,
        confidence: 0.9,
      });

      return recommendations;
    } catch (error) {
      console.error("Failed to fetch ML recommendations:", error);
      // Return placeholder data if ML API fails
      return [
        {
          category: "Diet",
          recommendation:
            "Consider incorporating more traditional Ethiopian dishes like Misir Wot (lentil stew) for increased protein and fiber intake.",
          confidence: 0.85,
        },
        {
          category: "Activity",
          recommendation:
            "Based on your progress, increasing your daily steps by 1,000 could help accelerate your weight loss goals.",
          confidence: 0.92,
        },
        {
          category: "Nutrition",
          recommendation:
            "Your calcium intake appears to be below recommended levels. Consider adding more dairy or calcium-rich vegetables to your diet.",
          confidence: 0.78,
        },
      ];
    }
  },

  getRecommendationHistory: async (): Promise<any[]> => {
    try {
      const response = await api.get("/recommendations/history");
      console.log(response.data);
      return response.data.recommendations.map((item: any) => ({
        id: item.recommendationId,
        recommendationId: item.recommendationId,
        date: item.createdAt,
        category: "Diet",
        healthMetricId: item.healthMetricId,
        predictions: item.predictions,
        nutritionTargets: item.nutritionTargets,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        userId: item.userId
      }));
    } catch (error) {
      console.error("Failed to fetch recommendation history:", error);
      // Return empty array if API fails
      return [];
    }
  },

  // Weight history data
  getWeightHistoryData: async (): Promise<any[]> => {
    try {
      // Use the existing API to fetch weight history data
      const response = await api.get("/health-metrics/weight-history-chart");
      const data = response.data;
      // Process the data to add target weight and ensure unique dates
      const dateMap = new Map();
      
      // Get user profile to get target weight
      const userProfile = await userService.getUserProfile();
      const targetWeight = userProfile.targetWeight || 65; // Default target weight
      
      // Group by date and use the latest weight for each date
      data.forEach((entry: any) => {
        const date = entry.date;
        // If this date already exists, update it only if this entry is newer
        if (!dateMap.has(date) || new Date(entry.recordedAt) > dateMap.get(date).recordedAt) {
          dateMap.set(date, {
            name: date,
            weight: entry.weight,
            target: targetWeight,
            recordedAt: new Date(entry.recordedAt)
          });
        }
      });
      
      // Convert map to array and sort by date
      const uniqueEntries = Array.from(dateMap.values());
      uniqueEntries.sort((a, b) => a.recordedAt - b.recordedAt);
      
      // Remove the recordedAt property which was only used for sorting
      return uniqueEntries.map(({ name, weight, target }) => ({ name, weight, target }));
    } catch (error) {
      console.error("Failed to fetch weight history data:", error);
      // Return placeholder data if API fails
      return [
        { name: "Jan", weight: 70, target: 65 },
        { name: "Feb", weight: 69, target: 65 },
        { name: "Mar", weight: 68, target: 65 },
        { name: "Apr", weight: 67.5, target: 65 },
        { name: "May", weight: 67, target: 65 },
        { name: "Jun", weight: 66, target: 65 },
      ];
    }
  },
};

export default chartDataService;
