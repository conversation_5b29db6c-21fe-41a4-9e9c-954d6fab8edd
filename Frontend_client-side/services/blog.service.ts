import { title } from "process";
import { api } from "./api";
import { AxiosError } from "axios";
import { authService } from "./auth.service";

export interface BlogPost {
  blogId: string;
  title: string;
  content: string;
  featuredImage?: string;
  category: string;
  authorId: string;
  createdAt: string;
  updatedAt: string;
  nutritionalInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
}

export interface BlogListResponse {
  data: BlogPost[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

interface BlogData {
  title: string;
  content: string;
  category: string;
  featuredImage?: File;
}
export interface BlogQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  excludeId?: string; // Added for related blogs
  authorId?: string;
}

export interface BlogResponse {
  data: BlogPost[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export const blogService = {
  async getAllBlogs(
    params: BlogQueryParams = {}
  ): Promise<any> {
    try {
      const response = await api.get("/blog", { params });
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      throw axiosError.response?.data || axiosError;
    }
  },

  async getRelatedBlogs(
    params: BlogQueryParams = {}
  ): Promise<BlogListResponse> {
    try {
      const response = await api.get("/blog/related", { params });
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      throw axiosError.response?.data || axiosError;
    }
  },

  async getBlogById(id: string): Promise<BlogPost> {
    try {
      const response = await api.get(`/blog/${id}`);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      throw axiosError.response?.data || axiosError;
    }
  },

  async getCategories(): Promise<string[]> {
    try {
      const response = await api.get("/blog/categories");
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      throw axiosError.response?.data || axiosError;
    }
  },

  async getMyBlogs(params: BlogQueryParams = {}): Promise<any> {
    try {
      const response = await api.get('/blog/my-blogs', { params });

      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      throw axiosError.response?.data || axiosError;
    }
  },
    
  async createBlog(blogData: BlogData): Promise<BlogPost> {
    try {
      const formData = new FormData();
      formData.append('title', blogData.title);
      formData.append('content', blogData.content);
      formData.append('category', blogData.category);
      if (blogData.featuredImage) {
        formData.append('file', blogData.featuredImage);
      }

      const response = await api.post('/blog', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError;
      throw axiosError.response?.data || axiosError;
    }
  },

  async updateBlogWithSuperAdmin(id: string, blogData: FormData): Promise<BlogPost> {
    console.log("Updating blog with super admin permissions");
    try {
      const featuredImage = blogData.get('featuredImage');
      const updateData: Record<string, any> = {
        title: blogData.get('title'),
        content: blogData.get('content'),
        category: blogData.get('category'),
      };

      if (featuredImage instanceof File) {
        const formData = new FormData();
        formData.append('title', updateData.title as string);
        formData.append('content', updateData.content as string);
        formData.append('category', updateData.category as string);
        formData.append('file', featuredImage);

        const response = await api.put(`/blog/${id}`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        });

        return response.data;
      } else {
        // featuredImage is likely a URL or not a File
        updateData.featuredImage = featuredImage;

        const response = await api.put(`/blog/${id}`, updateData);
        return response.data;
      }
    } catch (error) {
      const axiosError = error as AxiosError;
      throw axiosError.response?.data || axiosError;
    }
  },
  
  async updateBlog(id: string, blogData: FormData): Promise<BlogPost> {
    try {
      const featuredImage = blogData.get('featuredImage');
      const updateData: Record<string, any> = {
        title: blogData.get('title'),
        content: blogData.get('content'),
        category: blogData.get('category'),
      };
  
      if (featuredImage instanceof File) {
        const formData = new FormData();
        formData.append('title', updateData.title as string);
        formData.append('content', updateData.content as string);
        formData.append('category', updateData.category as string);
        formData.append('file', featuredImage);
  
        const response = await api.patch(`/blog/${id}`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        });
  
        return response.data;
      } else {
        // featuredImage is likely a URL or not a File
        updateData.featuredImage = featuredImage;
  
        const response = await api.patch(`/blog/${id}`, updateData);
        return response.data;
      }
    } catch (error) {
      const axiosError = error as AxiosError;
      throw axiosError.response?.data || axiosError;
    }
  },
   
  async deleteBlog(id: string): Promise<void> {
    try {
      await api.delete(`/blog/${id}`);
    } catch (error) {
      const axiosError = error as AxiosError;
      throw axiosError.response?.data || axiosError;
    }
  },

  async deleteBlogWithSuperAdmin(id: string): Promise<void> {
    try {
      await api.delete(`/blog/super-admin/${id}`);
    } catch (error) {
      const axiosError = error as AxiosError;
      throw axiosError.response?.data || axiosError;
    }
  }
};
