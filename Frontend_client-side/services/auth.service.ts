import { AxiosError } from "axios";
import { api } from "./api";
import Cookies from 'js-cookie';

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  // gender: string;
  // location: string;
}

interface RegisterResponse {
  message: string;
  token: string;
}

interface ErrorResponse {
  message: string;
  statusCode?: number;
}

interface HealthMetricsDto {
  age?: number;
  weight?: number;
  height?: number;
  healthIssues?: string;
  goals?: string;
}

interface UpdateUserDto {
  name?: string;
  email?: string;
  password?: string;
  phone?: string;
  status?: string;
  healthMetrics?: HealthMetricsDto;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  status?: string;
  healthMetrics?: HealthMetricsDto;
}

interface ChangePasswordRequest {
  email: string;
  currentPassword: string;
  newPassword: string;
}

interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}

interface CookieData {
  name: string;
  value: string;
  options: {
    expires: string;
    secure: boolean;
    sameSite: 'strict' | 'lax' | 'none';
    path: string;
    domain?: string;
  };
}

interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  userRole: string;
  message: string;
}

interface GoogleAuthResponse {
  accessToken: string;
  refreshToken: string;
  userRole: string;
}

class AuthService {

  private setCookie(cookieData: CookieData) {
    try {
      const domain = process.env.NEXT_PUBLIC_COOKIE_DOMAIN;
      console.log('Setting cookie with domain:', domain);
      
      Cookies.set(cookieData.name, cookieData.value, {
        expires: new Date(cookieData.options.expires),
        secure: true,
        sameSite: 'lax',
        path: '/',
        ...(domain ? { domain } : {})
      });
    } catch (error) {
      console.error('Error setting cookie:', error);
    }
  }

  private clearCookies() {
    try {
      const domain = process.env.NEXT_PUBLIC_COOKIE_DOMAIN;
      console.log('Clearing cookies with domain:', domain);
      
      Cookies.remove('access_token', { 
        domain: domain,
        path: '/',
        secure: true,
        sameSite: 'lax'
      });
      Cookies.remove('refresh_token', { 
        domain: domain,
        path: '/',
        secure: true,
        sameSite: 'lax'
      });
      Cookies.remove('user_role', { 
        domain: domain,
        path: '/',
        secure: true,
        sameSite: 'lax'
      });
    } catch (error) {
      console.error('Error clearing cookies:', error);
    }
  }

  private setAuthCookies(accessToken: string, refreshToken: string, userRole: string) {
    const domain = process.env.NEXT_PUBLIC_COOKIE_DOMAIN;
    
    // Set access token cookie
    this.setCookie({
      name: 'access_token',
      value: accessToken,
      options: {
        expires: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours
        secure: true,
        sameSite: 'lax',
        path: '/',
        ...(domain ? { domain } : {})
      }
    });

    // Set refresh token cookie
    this.setCookie({
      name: 'refresh_token',
      value: refreshToken,
      options: {
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 1 day
        secure: true,
        sameSite: 'lax',
        path: '/',
        ...(domain ? { domain } : {})
      }
    });

    // Set user role cookie
    this.setCookie({
      name: 'user_role',
      value: userRole,
      options: {
        expires: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours
        secure: true,
        sameSite: 'lax',
        path: '/',
        ...(domain ? { domain } : {})
      }
    });
  }

  async userLogin(credentials: LoginCredentials) {
    try {
      const response = await api.post<LoginResponse>("/auth/userLogin", credentials);
      
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<any>;
      if (axiosError.code === 'ECONNABORTED' || axiosError.message?.includes('timeout')) {
        throw new Error('Connection timed out. Please check your internet connection and try again.');
      }
      throw axiosError;
    }
  }

  async register(data: RegisterData): Promise<RegisterResponse> {
    try {
      const response = await api.post<RegisterResponse>("/auth/register", data);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      console.error('Registration error:', axiosError.response?.data?.message || axiosError.message);
      throw axiosError;
    }
  }

  async superAdminLogin(credentials: LoginCredentials) {
    try {
      const response = await api.post<LoginResponse>("/auth/superAdminLogin", credentials);
      
      // if (response.data) {
      //   const { accessToken, refreshToken, userRole } = response.data;
      //   this.setAuthCookies(accessToken, refreshToken, userRole);
      // }
  
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }
  
  async adminLogin(credentials: LoginCredentials) {
    try {
      const response = await api.post<LoginResponse>("/auth/AdminLogin", credentials);
  
      // if (response.data) {
      //   const { accessToken, refreshToken, userRole } = response.data;
      //   this.setAuthCookies(accessToken, refreshToken, userRole);
      // }
  
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }

  async logout() {
    try {
      // console.log(Cookies.get())
      const response = await api.post("/auth/logout");
      console.log('Logout response:', response.data);
      // Clear cookies and client-side state
      // this.clearCookies();
      localStorage.removeItem('userRole');
      
      console.log('Cookies after logout:', {
        allCookies: Cookies.get()
      });

      return response;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }

  async checkAuth() {
    try {
      const response = await api.get("/auth/check-cookies");
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      // Don't throw here, just return false to indicate not authenticated
      return false;
    }
  }

  async getCurrentUser() {
    try {
      const response = await api.get('/auth/check-user');
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      // Don't throw here, just return null to indicate no user
      return null;
    }
  }

  async refreshToken() {
    try {
      const response = await api.get("/auth/refresh");
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      
      // If refresh fails, clear user role and redirect to login
      if (axiosError.response?.status === 401) {
        localStorage.removeItem('userRole');
        
        // Only redirect if not already on login or signup page
        if (window.location.pathname !== '/adminLogin' && 
            window.location.pathname !== '/login' &&
            window.location.pathname !== '/signup') {
          window.location.href = '/login';
        }
      }
      
      throw axiosError;
    }
  }

  async checkUser() {
    try {
      const response = await api.get<UserProfile>("/auth/check-user");
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }

  async changePassword(data: ChangePasswordRequest) {
    try {
      const response = await api.post('/auth/change-password', data);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }

  async forgotPassword(email: string) {
    try {
      const response = await api.post('/auth/forgot-password', { email });
      return response.data;
    }
    catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }

  async resetPassword(data: ResetPasswordRequest) {
    try {
      const response = await api.post('/auth/reset-password', data);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }

  async handleGoogleAuth(response: GoogleAuthResponse) {
    if (response) {
      const { accessToken, refreshToken, userRole } = response;
      // this.setAuthCookies(accessToken, refreshToken, userRole);
      return response;
    }
    throw new Error('Invalid Google authentication response');
  }
}

export const authService = new AuthService();
