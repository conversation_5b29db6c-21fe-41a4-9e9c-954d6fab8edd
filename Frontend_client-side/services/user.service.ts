import { AxiosError } from "axios";
import { api } from "./api";
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  DELETED = 'deleted',
  PENDING = 'pending',
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  gender?: string;
  phone?: string;
  profilePicture?: string;
  activityLevel?: string;
  dietaryPreference?: string;
  targetWeight?: number;
  startingWeight?: number;
  bmi?: number;
  dailyCalorieTarget?: number;
  healthMetrics?: {
    age?: number;
    weight?: number;
    height?: number;
    gender?: string;
    activityLevel?: string;
    dietaryPreference?: string;
    location?: string;
    healthIssues?: string;
    fasting?: string;
    goals?: string;
  };
  createdAt: string;
  updatedAt: string;
  emailVerified: boolean;
}

export interface User {
  userId: string;
  name: string;
  email: string;
  phone?: string;
  status: UserStatus | string;
  createdAt: string;
  profilePicture?: string;
  healthMetrics?: HealthMetricsDto;
}

export interface HealthMetricsDto {
  age?: number;
  weight?: number;
  height?: number;
  healthIssues?: string;
  goals?: string;
}

interface RecommendationData {
  userData: any;
  prediction: PredictionResponse;
}

export interface PredictionResponse {
  nutritional_detail: {
    daily_calorie_target: number;
    protein: number;
    sugar: number;
    sodium: number;
    calories: number;
    carbohydrates: number;
    fiber: number;
    fat: number;
  };
  recommendations: {
    breakfast: any[];
    lunch: any[];
    snack: any[];
    dinner: any[];
  };
}

interface ErrorResponse {
  message: string;
  statusCode?: number;
}

interface GetUsersParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  includeDeleted?: boolean;
}

interface PaginatedResponse<T> {
  users: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

class UserService {
  async getAllUsers(params: GetUsersParams = {}): Promise<PaginatedResponse<User>> {
    try {
      const { page = 1, limit = 10, search = "", status = "" } = params;
      
      // Build query parameters
      const queryParams = new URLSearchParams();
      queryParams.append('page', page.toString());
      queryParams.append('limit', limit.toString());
      
      if (search) {
        queryParams.append('search', search);
      }
      
      if (status) {
        queryParams.append('status', status);
      }
            
      const response = await api.get(`/users?${queryParams.toString()}`)
      
      if (response.data && response.data.users && Array.isArray(response.data.users)) {
        return response.data;
      } else {
        console.error("Unexpected API response format:", response.data);

        return {
          users: [],
          meta: {
            total: 0,
            page: 1,
            limit: 10,
            totalPages: 1
          }
        };
      }
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      console.error("Error in getAllUsers:", axiosError);
      
      return {
        users: [],
        meta: {
          total: 0,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      };
    }
  }

  async getUserById(userId: string) {
    try {
      const response = await api.get<User>(`/users/${userId}`);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }

  async updateHealthMetrics(data: any) {
    try {
      console.log(data);
      const response = await api.patch('/health-metrics', data); // Send data to the backend
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      console.error("Error updating health metrics:", axiosError.response?.data);
      throw axiosError;
    }
  }

  async updateHealthMetricsOnBoarding(data: any) {
    try {
      const response = await api.patch('/health-metrics/onboarding', data);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      console.error("Error updating health metrics:", axiosError.response?.data);
      throw axiosError;
    }
  }
  
  async createUser(userData: {
    name: string;
    email: string;
    password: string;
    status: string;
  }): Promise<User> {
    try {
      const response = await api.post('/users/create', userData);
      return response.data;
    }
    catch(error)
    {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }

  async updateUser(userId: string, data: any) {
    try {
      console.log("response");
      const response = await api.put(`/users/${userId}`, data);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }

  async deleteUser(userId: string) {
    try {
      const response = await api.delete(`/users/${userId}`);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }

  async updateUserStatus(userId: string, status: string) {
    try {
      const response = await api.patch(`/users/status/${userId}`, { status });
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }

  async getRecentUsers(limit: number = 5) {
    try {
      const response = await api.get<User[]>(`/users/recent?limit=${limit}`);
      return response.data;
    } catch (error) {
      const axiosError = error as AxiosError<ErrorResponse>;
      throw axiosError;
    }
  }

  async getUserProfile(): Promise<UserProfile> {
    try {
      const response = await api.get(`/users/profile`);
      
      if (!response?.data?.userProfile) {
        console.log("No userProfile found in response:", response.data);
      }
      
      return response?.data?.userProfile;
    } catch (error) {
      const axiosError = error as AxiosError;
      throw axiosError.response?.data || axiosError;
    }
  }

  async saveRecommendation(data: RecommendationData): Promise<void> {
    try {
      await api.post('/recommendations/save', data);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to save recommendation');
    }
  }
}

export const userService = new UserService();
