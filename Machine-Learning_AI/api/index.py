import logging
import os
import re
from enum import Enum
from typing import Dict, List, Optional
import numpy as np
import pandas as pd
import pickle
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.docs import get_swagger_ui_html
from pydantic import BaseModel, Field, field_validator

# ==================== LOGGING SETUP ====================

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(), logging.FileHandler('api.log')]
)
logger = logging.getLogger(__name__)

# ==================== CONSTANTS ====================

BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
MODEL_PATH = os.path.join(BASE_DIR, 'model', 'rf_regressor_optimized.pkl')
FOOD_DATA_PATH = os.path.join(BASE_DIR, 'datasets', 'Ethiopian_Foods.csv')

MAJOR_URBAN_CENTERS = [
    "addis ababa", "dire dawa", "adama", "gondar", "mekelle",
    "hawassa", "bahir dar", "jimma", "debre markos"
]

SUPPORTED_LOCATIONS = [
    "afar", "asossa/berta", "asossa/komo", "asossa/mao", "bale", "w.wellega", "n.omo/konta",
    "bench and maji zone/bench", "bench and maji zone/menit", "bench and maji zone/sheko",
    "borena", "dorze", "e. wellega", "e.hararge/jarso", "e.hararge/kersa",
    "gambella/agnuak", "gambella/nuer", "gamo", "gedeo", "harma area",
    "gonder/negede weyto", "keficho", "kembata", "konsso",
    "guji", "gurage/cheha", "harari", "keficho zone/keficho", "kemache zone/gumuz",
    "metekel/gumuz", "metekel/shinasha", "n. gonder/dembia", "n. omo/konta",
    "sidama", "tigray/central zone", "tigray/eastern zone", "tigray/kunama",
    "tigray/mekele", "tigray/shire", "tigray/southern zone",
    "urban/common", "w. wellega", "welayita"
] + MAJOR_URBAN_CENTERS

# Modified REGION_GROUPS to handle Dire Dawa as both Oromia and Harari
REGION_GROUPS = {
    loc: region for loc, region in [
        ("addis ababa", "urban"), ("dire dawa", "oromia/harari"), ("adama", "oromia"), ("w.wellega", "oromia"),
        ("gondar", "amhara"), ("mekelle", "tigray"), ("hawassa", "snnpr"), ("n.omo/konta", "snnpr"),
        ("bahir dar", "amhara"), ("jimma", "oromia"), ("debre markos", "amhara"),
        ("afar", "afar"), ("asossa/berta", "benishangul gumuz"),
        ("asossa/komo", "benishangul gumuz"), ("asossa/mao", "benishangul gumuz"),
        ("bale", "oromia"), ("bench and maji zone/bench", "snnpr"),
        ("bench and maji zone/menit", "snnpr"), ("bench and maji zone/sheko", "snnpr"),
        ("borena", "oromia"), ("dorze", "snnpr"), ("e. wellega", "oromia"),
        ("e.hararge/jarso", "oromia"), ("e.hararge/kersa", "oromia"),
        ("gambella/agnuak", "gambella"), ("gambella/nuer", "gambella"),
        ("gamo", "snnpr"), ("gedeo", "snnpr"), ("gonder/negede weyto", "amhara"),
        ("guji", "oromia"), ("gurage/cheha", "snnpr"), ("harari", "harari"),
        ("keficho zone/keficho", "snnpr"), ("kemache zone/gumuz", "benishangul gumuz"),
        ("kembata", "snnpr"), ("konsso", "snnpr"), ("metekel/gumuz", "benishangul gumuz"),
        ("metekel/shinasha", "benishangul gumuz"), ("n. gonder/dembia", "amhara"),
        ("n. omo/konta", "snnpr"), ("sidama", "snnpr"), ("tigray/central zone", "tigray"),
        ("tigray/eastern zone", "tigray"), ("tigray/kunama", "tigray"),
        ("tigray/mekele", "tigray"), ("tigray/shire", "tigray"),
        ("tigray/southern zone", "tigray"), ("urban/common", "urban"),
        ("w. wellega", "oromia"), ("welayita", "snnpr")
    ]
}

DIETARY_FILTERS = {
    'omnivore': lambda df: df,
    'vegetarian': lambda df: df[~df['Category'].str.contains('meat|fish|poultry', case=False, na=False)],
    'vegan': lambda df: df[df['Category'].str.contains('cereal|starchy|legumes|vegetables|fruits', case=False, na=False)]
}

HEALTH_ISSUE_RESTRICTIONS = {
    'Acne': {'fat': {'max': 10.0, 'weight': 0.4}, 'sugar': {'max': 5.0, 'weight': 0.3}},
    'Diabetes': {'cho (inc.fiber)': {'max': 30.0, 'weight': 0.3}, 'fiber': {'min': 5.0, 'weight': 0.4}},
    'Heart Disease': {'fat': {'max': 7.0, 'weight': 0.4}, 'sodium': {'max': 150.0, 'weight': 0.3}},
    'Hypertension': {'sodium': {'max': 100.0, 'weight': 0.5}, 'fat': {'max': 30.0, 'weight': 0.2}},
    'Kidney Disease': {
        'protein': {'max': 10.0, 'weight': 0.3},
        'sodium': {'max': 100.0, 'weight': 0.3},
        'potassium': {'max': 200.0, 'weight': 0.2}
    },
    'None': {}
}

WEIGHT_GOAL_ADJUSTMENTS = {
    'gain weight': {'calories': 1.2, 'protein': 1.2},
    'lose weight': {'calories': 0.8, 'fat': 0.8},
    'maintain weight': {'calories': 1.0, 'protein': 1.0, 'fat': 1.0}
}

FASTING_RESTRICTED_INGREDIENTS = [
    'milk', 'asa', 'fish', 'wetet', 'meat', 'siga', 'ergo', 'yogurt', 'cheese', 'ayib', 'butter', 'qibe', 'kibe', 'Beef'
]

SNACK_CATEGORIES = [
    'legumes and legume based products',
    'fish and fish based products',
    'fruits and berries',
    'vegetables and vegetables based products',
    'dairy and dairy based products'
]

NUTRIENT_MAPPING = {
    'fat': 'Fat',
    'sugar': 'Sugar',
    'cho (inc.fiber)': 'CHO (inc.fiber)',
    'fiber': 'Fiber',
    'sodium': 'Sodium',
    'protein': 'Protein',
    'potassium': 'Potassium'
}

MEAL_CALORIE_DISTRIBUTION = {
    'breakfast': 0.25, 'lunch': 0.35, 'snack': 0.15, 'dinner': 0.25
}

SWAGGER_CUSTOM_CSS = """
    .swagger-ui .topbar { background-color: #2c3e50; }
    .swagger-ui .info .title { color: #27ae60; font-family: 'Arial', sans-serif; font-size: 28px; }
    .swagger-ui .info .description { font-size: 18px; line-height: 1.6; color: #2d3436; padding: 25px; background-color: #f8f9fa; border-radius: 8px; }
    .swagger-ui .info { background-color: #ffffff; border: 2px solid #27ae60; border-radius: 12px; padding: 30px; margin-bottom: 20px; }
    .swagger-ui .opblock-tag { background-color: #34495e; color: #ecf0f1; }
    .swagger-ui .opblock { border-radius: 8px; margin-bottom: 10px; }
    .swagger-ui .opblock-summary { background-color: #ecf0f1; }
    .swagger-ui .opblock-summary-method { background-color: #27ae60; color: white; }
    .swagger-ui .opblock-get { border-left: 5px solid #3498db; }
    .swagger-ui .opblock-post { border-left: 5px solid #27ae60; }
    .swagger-ui .try-out__btn { background-color: #27ae60; color: white; }
    .swagger-ui .btn.execute { background-color: #2980b9; border-color: #2980b9; }
    .swagger-ui .btn.authorize { background-color: #e74c3c; border-color: #e74c3c; }
    .swagger-ui input, .swagger-ui select { border-radius: 4px; border: 1px solid #bdc3c7; }
    .swagger-ui .scheme-container { background-color: #f7f9fb; }
    .swagger-ui .filter .operation-filter-input { border: 2px solid #3498db; }
    .swagger-ui .markdown p, .swagger-ui .markdown li { font-size: 18px; color: #2d3436; }
    .swagger-ui .opblock-description-wrapper { background-color: #f8f9fa; padding: 10px; border-radius: 4px; }
"""

# ==================== ENUMS ====================

class Gender(str, Enum):
    male = "Male"
    female = "Female"

class ActivityLevel(str, Enum):
    sedentary = "Sedentary"
    lightly_active = "Lightly Active"
    moderately_active = "Moderately Active"
    very_active = "Very Active"

class DietaryPreference(str, Enum):
    omnivore = "Omnivore"
    vegetarian = "Vegetarian"
    vegan = "Vegan"

class WeightGoal(str, Enum):
    lose = "Lose Weight"
    maintain = "Maintain Weight"
    gain = "Gain Weight"

class HealthIssue(str, Enum):
    none = "None"
    diabetes = "Diabetes"
    hypertension = "Hypertension"
    heart_disease = "Heart Disease"
    kidney_disease = "Kidney Disease"
    acne = "Acne"

class FastingStatus(str, Enum):
    yes = "Yes"
    no = "No"

# ==================== MODELS ====================

class NutritionalDetail(BaseModel):
    daily_calorie_target: float = Field(..., json_schema_extra={"example": 2000})
    protein: float = Field(..., json_schema_extra={"example": 60})
    sugar: float = Field(..., json_schema_extra={"example": 30})
    sodium: float = Field(..., json_schema_extra={"example": 1500})
    calories: float = Field(..., json_schema_extra={"example": 2000})
    carbohydrates: float = Field(..., json_schema_extra={"example": 250})
    fiber: float = Field(..., json_schema_extra={"example": 30})
    fat: float = Field(..., json_schema_extra={"example": 65})

class BaseInput(BaseModel):
    dietary_preference: DietaryPreference = Field(..., json_schema_extra={"example": "Omnivore"})
    weight_goal: WeightGoal = Field(..., json_schema_extra={"example": "Maintain Weight"})
    health_issues: List[HealthIssue] = Field(default=[HealthIssue.none], json_schema_extra={"example": ["None"]})

    @field_validator('health_issues')
    @classmethod
    def validate_health_issues(cls, v):
        if not v:
            raise ValueError("At least one health issue must be specified")
        if HealthIssue.none in v and len(v) > 1:
            raise ValueError("Cannot combine 'None' with other health issues")
        return v

class UserInput(BaseInput):
    age: int = Field(..., ge=18, le=100, json_schema_extra={"example": 30})
    gender: Gender = Field(..., json_schema_extra={"example": "Male"})
    height: float = Field(..., ge=100, le=250, json_schema_extra={"example": 170})
    weight: float = Field(..., ge=30, le=200, json_schema_extra={"example": 70})
    activity_level: ActivityLevel = Field(..., json_schema_extra={"example": "Moderately Active"})

class RecommendationRequest(BaseInput):
    nutritional_detail: NutritionalDetail
    fasting: FastingStatus = Field(..., json_schema_extra={"example": "No"})
    location: Optional[str] = Field(None, json_schema_extra={"example": "Addis Ababa"})

    @field_validator('location')
    @classmethod
    def validate_location(cls, v):
        if v is not None:
            v_lower = v.strip().lower()
            if v_lower not in SUPPORTED_LOCATIONS:
                raise ValueError(f"Unsupported location. Supported locations: {', '.join(SUPPORTED_LOCATIONS)}")
        return v

class CombinedInput(UserInput):
    fasting: FastingStatus = Field(..., json_schema_extra={"example": "No"})
    location: Optional[str] = Field(None, json_schema_extra={"example": "Addis Ababa"})

    @field_validator('location')
    @classmethod
    def validate_location(cls, v):
        if v is not None:
            v_lower = v.strip().lower()
            if v_lower not in SUPPORTED_LOCATIONS:
                raise ValueError(f"Unsupported location. Supported locations: {', '.join(SUPPORTED_LOCATIONS)}")
        return v

class FoodItem(BaseModel):
    local_name: str
    ingredient: str
    location: str
    region: str
    category: str
    nutrition: Dict[str, float]
    serving_size_grams: float = Field(..., json_schema_extra={"example": 100})
    serving_nutrition: Dict[str, float]

class RecommendationResponse(BaseModel):
    breakfast: List[FoodItem]
    lunch: List[FoodItem]
    snack: List[FoodItem]
    dinner: List[FoodItem]

class CombinedResponse(BaseModel):
    nutritional_detail: NutritionalDetail
    recommendations: RecommendationResponse

class ValidInputResponse(BaseModel):
    genders: List[str]
    activity_levels: List[str]
    dietary_preferences: List[str]
    weight_goals: List[str]
    health_issues: List[str]
    fasting_statuses: List[str]
    supported_locations: List[str]

# ==================== INITIALIZATION ====================

def load_nutrition_model() -> tuple[Optional[object], Optional[object]]:
    """Load the nutrition prediction model and multi-label binarizer."""
    try:
        with open(MODEL_PATH, 'rb') as f:
            model_dict = pickle.load(f)
        logger.info("Nutrition model loaded successfully")
        return model_dict['model'], model_dict['mlb']
    except Exception as e:
        logger.error(f"Error loading nutrition model: {e}")
        return None, None

def load_food_data() -> tuple[pd.DataFrame, pd.DataFrame]:
    """Load and preprocess the food dataset."""
    try:
        if not os.path.exists(FOOD_DATA_PATH):
            raise FileNotFoundError(f"Food data file not found at: {FOOD_DATA_PATH}")
        
        food_df = pd.read_csv(FOOD_DATA_PATH, encoding='utf-8')
        
        # Clean and standardize data
        food_df['Location'] = food_df['Location'].str.strip().str.lower()
        food_df['Category'] = food_df['Category'].str.strip().str.lower()
        
        # Convert numeric columns
        numeric_cols = ['Food Energy', 'Protein', 'Fat', 'CHO (inc.fiber)', 'Fiber', 'Sodium']
        for col in numeric_cols:
            if col in food_df.columns:
                food_df[col] = pd.to_numeric(food_df[col], errors='coerce').fillna(0)
        
        # Add region column
        food_df['Region'] = food_df['Location'].map(REGION_GROUPS).fillna('other')
        
        # Create urban foods DataFrame
        urban_foods_df = food_df[food_df['Location'].isin(['urban', 'common', 'urban/common'])]
        
        logger.info(f"Loaded {len(food_df)} food records (including {len(urban_foods_df)} urban/common foods)")
        return food_df, urban_foods_df
    except Exception as e:
        logger.error(f"Error loading food data: {e}", exc_info=True)
        return pd.DataFrame(), pd.DataFrame()

model, mlb = load_nutrition_model()
food_df, urban_foods_df = load_food_data()

# ==================== FASTAPI SETUP ====================

app = FastAPI(
    title="🌾 EthioNutri: Personalized Ethiopian Meal Planner",
    description="""EthioNutri crafts personalized, culturally rich Ethiopian meal plans tailored to your dietary preferences, health goals, and regional traditions. Powered by advanced nutrition prediction, it offers portioned breakfast, lunch, dinner, and snacks that nourish and delight.

**Why EthioNutri?**
- **Personalized**: Matches your nutritional needs with precise serving sizes.
- **Culturally Rich**: Features Ethiopia's diverse flavors across urban and regional cuisines.
- **Health-Focused**: Supports weight goals and conditions like diabetes or hypertension.
- **User-Friendly**: Interactive Swagger UI for easy exploration.

Start your culinary journey through Ethiopia today!""",
    version="2.3",
    contact={"name": "EthioNutri Support Team", "email": "<EMAIL>"},
    docs_url=None,
    redoc_url=None
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

@app.middleware("http")
async def add_forwarded_proto(request, call_next):
    request.scope["scheme"] = request.headers.get("X-Forwarded-Proto", "http")
    response = await call_next(request)
    return response

@app.get("/API-DOCS", include_in_schema=False)
async def custom_swagger_ui_html():
    """Serve custom Swagger UI for API documentation."""
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=f"{app.title} - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        swagger_favicon_url="https://favicon.io/favicon-converter/favicon.ico",
        swagger_ui_parameters={
            "defaultModelsExpandDepth": 1,
            "docExpansion": "list",
            "filter": True,
            "tryItOutEnabled": True,
            "displayOperationId": True,
            "persistAuthorization": True,
            "syntaxHighlight.theme": "obsidian",
            "layout": "BaseLayout",
            "deepLinking": True,
            "customCss": SWAGGER_CUSTOM_CSS
        }
    )

# ==================== CORE FUNCTIONS ====================

def predict_nutrition(user_input: UserInput) -> NutritionalDetail:
    """Predict nutritional needs based on user input."""
    if model is None or mlb is None:
        raise HTTPException(status_code=500, detail="Nutrition prediction service unavailable")
    
    try:
        input_data = user_input.model_dump()
        health_issues = [hi.value for hi in input_data['health_issues']]
        
        features = pd.DataFrame([{
            'Ages': input_data['age'],
            'Gender': input_data['gender'].value,
            'Height': input_data['height'],
            'Weight': input_data['weight'],
            'Activity Level': input_data['activity_level'].value,
            'Dietary Preference': input_data['dietary_preference'].value,
            'Weight Goal': input_data['weight_goal'].value,
            'BMI': input_data['weight'] / ((input_data['height']/100) ** 2)
        }])
        
        health_encoded = pd.DataFrame(
            mlb.transform([health_issues]),
            columns=[f"Health Issues_{c}" for c in mlb.classes_]
        )
        features = pd.concat([features, health_encoded], axis=1)
        
        pred = model.predict(features)[0]
        
        nutrients = {
            'daily_calorie_target': float(np.expm1(pred[0])),
            'protein': float(np.expm1(pred[1])),
            'sugar': min(float(np.expm1(pred[2])), 50),
            'sodium': float(np.expm1(pred[3])) * 1000,
            'calories': float(np.expm1(pred[4])),
            'carbohydrates': float(np.expm1(pred[5])),
            'fiber': float(np.expm1(pred[6])),
            'fat': float(np.expm1(pred[7]))
        }
        
        return NutritionalDetail(**nutrients)
    except Exception as e:
        logger.error(f"Prediction error: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Prediction failed: {str(e)}")

def filter_by_health_issues(df: pd.DataFrame, issues: List[str]) -> pd.DataFrame:
    """Filter foods based on health issue restrictions."""
    if not issues or any(issue.lower() == 'none' for issue in issues):
        logger.info("No health issue restrictions applied for 'None'")
        return df.copy()
    
    try:
        df = df.copy()
        for issue in issues:
            # Match HEALTH_ISSUE_RESTRICTIONS keys (e.g., 'None', 'Hypertension')
            issue_key = issue if issue == 'None' else issue.capitalize()
            restrictions = HEALTH_ISSUE_RESTRICTIONS.get(issue_key, {})
            if not restrictions:
                logger.warning(f"No restrictions defined for health issue: {issue_key}")
                continue
            for nutrient, constraint in restrictions.items():
                nutrient_column = NUTRIENT_MAPPING.get(nutrient)
                if nutrient_column not in df.columns:
                    logger.warning(f"Nutrient column {nutrient_column} not found in DataFrame")
                    continue
                if 'max' in constraint:
                    df = df[df[nutrient_column] <= constraint['max']]
                if 'min' in constraint:
                    df = df[df[nutrient_column] >= constraint['min']]
        
        logger.info(f"After health issue filters for {issues}: {len(df)} foods remain")
        if len(df) == 0:
            logger.warning(f"No foods match health issue restrictions for {issues}")
            raise ValueError(f"No foods meet health requirements for {issues}")
        
        return df
    except ValueError as e:
        logger.error(f"Health issue filtering error: {e}", exc_info=True)
        raise
    except Exception as e:
        logger.error(f"Unexpected error in health issue filtering: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Health issue filtering failed: {str(e)}")

def recommend_meals(request: RecommendationRequest) -> RecommendationResponse:
    """Generate meal recommendations based on user preferences and constraints."""
    if food_df.empty:
        raise HTTPException(status_code=503, detail="Food database not available")
    
    try:
        df = food_df.copy()
        logger.info(f"Starting with {len(df)} foods")
        
        # Apply dietary filter
        diet_filter = DIETARY_FILTERS.get(request.dietary_preference.value.lower(), DIETARY_FILTERS['omnivore'])
        df = diet_filter(df)
        logger.info(f"After dietary filter: {len(df)}")
        
        # Apply health-based filtering
        health_issues = [hi.value for hi in request.health_issues]
        df = filter_by_health_issues(df, health_issues)
        logger.info(f"After health filter: {len(df)}")
        
        # Apply fasting filter
        if request.fasting == FastingStatus.yes:
            pattern = '|'.join(FASTING_RESTRICTED_INGREDIENTS)
            df = df[~df['Ingredient'].str.contains(pattern, case=False, na=False)]
            logger.info(f"After fasting filter (restricted ingredients): {len(df)}")
            if len(df) == 0:
                logger.warning("No foods available after fasting filter")
                return RecommendationResponse(breakfast=[], lunch=[], snack=[], dinner=[])
        
        # Apply location filter with special handling for urban centers and Dire Dawa
        if request.location:
            location = request.location.lower().strip()
            if location in MAJOR_URBAN_CENTERS:
                # For urban centers, prioritize urban/common foods first
                urban_df = urban_foods_df.copy()
                urban_df = diet_filter(urban_df)
                urban_df = filter_by_health_issues(urban_df, health_issues)
                if request.fasting == FastingStatus.yes:
                    urban_df = urban_df[~urban_df['Ingredient'].str.contains(pattern, case=False, na=False)]
                
                if len(urban_df) == 0:
                    logger.warning(f"No urban/common foods match filters for {location}")
                
                # Special handling for Dire Dawa (both Oromia and Harari)
                if location == "dire dawa":
                    region_df1 = df[df['Region'] == "oromia"]
                    region_df2 = df[df['Region'] == "harari"]
                    region_df = pd.concat([region_df1, region_df2]).drop_duplicates()
                else:
                    region = REGION_GROUPS.get(location, 'other').split('/')[0]  # Take first region if multiple
                    region_df = df[df['Region'] == region]
                
                combined_df = pd.concat([urban_df, region_df]).drop_duplicates()
                
                if len(combined_df) > 0:
                    df = combined_df
                    logger.info(f"Using {len(df)} foods (urban/common + regional) for major city {location}")
                else:
                    logger.warning(f"No urban/common or regional foods match filters for {location}")
                    return RecommendationResponse(breakfast=[], lunch=[], snack=[], dinner=[])
            else:
                # For non-urban locations, first try exact location match
                location_df = df[df['Location'] == location]
                if len(location_df) > 0:
                    df = location_df
                    logger.info(f"Found {len(df)} exact location matches for {location}")
                else:
                    # Fall back to regional foods
                    region = REGION_GROUPS.get(location, 'other')
                    if region == 'oromia/harari':  # Special case for Dire Dawa
                        region_df1 = df[df['Region'] == "oromia"]
                        region_df2 = df[df['Region'] == "harari"]
                        region_df = pd.concat([region_df1, region_df2]).drop_duplicates()
                    else:
                        region_df = df[df['Region'] == region]
                    
                    if len(region_df) > 0:
                        df = region_df
                        logger.info(f"Falling back to {len(df)} regional foods for {location}")
                    else:
                        logger.warning(f"No regional foods match for {location}")
                        return RecommendationResponse(breakfast=[], lunch=[], snack=[], dinner=[])
        
        if len(df) == 0:
            logger.warning("No foods match all filters")
            return RecommendationResponse(breakfast=[], lunch=[], snack=[], dinner=[])
        
        targets = adjust_targets(request.nutritional_detail, request.weight_goal)
        used_foods = set()
        
        return RecommendationResponse(
            breakfast=get_meal_options(df, targets, 'breakfast', health_issues, used_foods),
            lunch=get_meal_options(df, targets, 'lunch', health_issues, used_foods),
            snack=get_snack_options(df, targets, health_issues, used_foods),
            dinner=get_meal_options(df, targets, 'dinner', health_issues, used_foods)
        )
    except ValueError as e:
        logger.warning(f"Recommendation skipped due to no matching foods: {e}")
        return RecommendationResponse(breakfast=[], lunch=[], snack=[], dinner=[])
    except Exception as e:
        logger.error(f"Recommendation error: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Recommendation failed: {str(e)}")

def adjust_targets(nutrition: NutritionalDetail, goal: WeightGoal) -> Dict:
    """Adjust nutritional targets based on weight goal."""
    adjustments = WEIGHT_GOAL_ADJUSTMENTS.get(goal.value.lower(), {})
    adjusted = nutrition.model_dump()
    for nutrient, factor in adjustments.items():
        if nutrient in adjusted:
            adjusted[nutrient] *= factor
    return adjusted

def get_meal_options(df: pd.DataFrame, targets: Dict, meal_type: str, health_issues: List[str], used_foods: set) -> List[FoodItem]:
    """Select food items for a specific meal type."""
    if df.empty:
        return []
    
    try:
        df = df.copy()
        df = df[~df['Local Name'].isin(used_foods)]
        
        if len(df) < 3:
            logger.warning(f"Not enough unique foods for {meal_type}, relaxing filters")
            df = food_df.copy()
            df = DIETARY_FILTERS.get('omnivore')(df)
            df = filter_by_health_issues(df, health_issues)
            df = df[~df['Local Name'].isin(used_foods)]
        
        df['score'] = df.apply(lambda x: calculate_food_score(x, targets, meal_type, health_issues), axis=1)
        n_candidates = min(15, max(3, len(df)))
        candidates = df.nsmallest(n_candidates, 'score')
        selected = candidates.sample(n=3, replace=False) if len(candidates) >= 3 else candidates
        
        used_foods.update(selected['Local Name'].values)
        meal_calories = targets['daily_calorie_target'] * MEAL_CALORIE_DISTRIBUTION.get(meal_type, 0.25)
        
        total_calories = selected['Food Energy'].sum()
        if total_calories == 0:
            serving_grams = pd.Series([meal_calories / 3 / 100] * len(selected), index=selected.index)
        else:
            serving_sizes = (meal_calories / 3) / selected['Food Energy'] * 100
            serving_grams = serving_sizes.clip(lower=10)
        
        return [
            FoodItem(
                local_name=row['Local Name'],
                ingredient=row['Ingredient'],
                location=row['Location'],
                region=REGION_GROUPS.get(row['Location'].lower(), 'other'),
                category=row['Category'],
                nutrition={
                    'calories': row.get('Food Energy', 0),
                    'protein': row.get('Protein', 0),
                    'fat': row.get('Fat', 0),
                    'carbohydrates': row.get('CHO (inc.fiber)', 0),
                    'fiber': row.get('Fiber', 0),
                    'sodium': row.get('Sodium', 0)
                },
                serving_size_grams=serving_grams.iloc[i],
                serving_nutrition={
                    'calories': (row.get('Food Energy', 0) * serving_grams.iloc[i]) / 100,
                    'protein': (row.get('Protein', 0) * serving_grams.iloc[i]) / 100,
                    'fat': (row.get('Fat', 0) * serving_grams.iloc[i]) / 100,
                    'carbohydrates': (row.get('CHO (inc.fiber)', 0) * serving_grams.iloc[i]) / 100,
                    'fiber': (row.get('Fiber', 0) * serving_grams.iloc[i]) / 100,
                    'sodium': (row.get('Sodium', 0) * serving_grams.iloc[i]) / 100
                }
            ) for i, (_, row) in enumerate(selected.iterrows())
        ]
    except Exception as e:
        logger.error(f"Meal selection error for {meal_type}: {e}")
        return []

def get_snack_options(df: pd.DataFrame, targets: Dict, health_issues: List[str], used_foods: set) -> List[FoodItem]:
    """Select food items for snacks."""
    if df.empty:
        return []
    
    try:
        snack_df = df[df['Category'].str.lower().isin(SNACK_CATEGORIES)]
        df = snack_df.copy() if len(snack_df) > 0 else df.copy()
        df = df[~df['Local Name'].isin(used_foods)]
        
        if len(df) < 3:
            logger.warning("Not enough unique foods for snack, relaxing filters")
            df = food_df.copy()
            df = df[df['Category'].str.lower().isin(SNACK_CATEGORIES)]
            df = filter_by_health_issues(df, health_issues)
            df = df[~df['Local Name'].isin(used_foods)]
        
        df['score'] = df.apply(lambda x: calculate_food_score(x, targets, 'snack', health_issues), axis=1)
        n_candidates = min(15, max(3, len(df)))
        candidates = df.nsmallest(n_candidates, 'score')
        selected = candidates.sample(n=3, replace=False) if len(candidates) >= 3 else candidates
        
        used_foods.update(selected['Local Name'].values)
        snack_calories = targets['daily_calorie_target'] * MEAL_CALORIE_DISTRIBUTION['snack']
        
        total_calories = selected['Food Energy'].sum()
        if total_calories == 0:
            serving_grams = pd.Series([snack_calories / 3 / 100] * len(selected), index=selected.index)
        else:
            serving_sizes = (snack_calories / 3) / selected['Food Energy'] * 100
            serving_grams = serving_sizes.clip(lower=10)
        
        return [
            FoodItem(
                local_name=row['Local Name'],
                ingredient=row['Ingredient'],
                location=row['Location'],
                region=REGION_GROUPS.get(row['Location'].lower(), 'other'),
                category=row['Category'],
                nutrition={
                    'calories': row.get('Food Energy', 0),
                    'protein': row.get('Protein', 0),
                    'fat': row.get('Fat', 0),
                    'carbohydrates': row.get('CHO (inc.fiber)', 0),
                    'fiber': row.get('Fiber', 0),
                    'sodium': row.get('Sodium', 0)
                },
                serving_size_grams=serving_grams.iloc[i],
                serving_nutrition={
                    'calories': (row.get('Food Energy', 0) * serving_grams.iloc[i]) / 100,
                    'protein': (row.get('Protein', 0) * serving_grams.iloc[i]) / 100,
                    'fat': (row.get('Fat', 0) * serving_grams.iloc[i]) / 100,
                    'carbohydrates': (row.get('CHO (inc.fiber)', 0) * serving_grams.iloc[i]) / 100,
                    'fiber': (row.get('Fiber', 0) * serving_grams.iloc[i]) / 100,
                    'sodium': (row.get('Sodium', 0) * serving_grams.iloc[i]) / 100
                }
            ) for i, (_, row) in enumerate(selected.iterrows())
        ]
    except Exception as e:
        logger.error(f"Snack selection error: {e}")
        return []

def calculate_food_score(food: pd.Series, targets: Dict, meal_type: str, health_issues: List[str]) -> float:
    """Calculate a score for a food item based on nutritional targets and health issues."""
    meal_calories = targets['daily_calorie_target'] * MEAL_CALORIE_DISTRIBUTION.get(meal_type, 0.25)
    
    calorie_diff = abs(food.get('Food Energy', 0) - meal_calories / 3)
    protein_diff = abs(food.get('Protein', 0) - targets['protein'] / 12)
    carb_diff = abs(food.get('CHO (inc.fiber)', 0) - targets['carbohydrates'] / 12)
    fat_diff = abs(food.get('Fat', 0) - targets['fat'] / 12)
    
    score = 0.4 * calorie_diff + 0.3 * protein_diff + 0.2 * carb_diff + 0.1 * fat_diff
    
    for issue in health_issues:
        issue_key = issue if issue == 'None' else issue.capitalize()
        restrictions = HEALTH_ISSUE_RESTRICTIONS.get(issue_key, {})
        for nutrient, constraint in restrictions.items():
            nutrient_column = NUTRIENT_MAPPING.get(nutrient)
            if nutrient_column in food and 'weight' in constraint:
                value = food.get(nutrient_column, 0)
                if 'max' in constraint and value > constraint['max']:
                    score += constraint['weight'] * (value - constraint['max']) * 10
                elif 'min' in constraint and value < constraint['min']:
                    score += constraint['weight'] * (constraint['min'] - value) * 10
                elif 'min' in constraint and value >= constraint['min']:
                    score -= constraint['weight'] * value * 0.5
    
    return score

# ==================== API ENDPOINTS ====================

@app.get("/valid-inputs", response_model=ValidInputResponse, tags=["Utilities"])
async def get_valid_inputs():
    """Return valid input options for API requests."""
    return ValidInputResponse(
        genders=[g.value for g in Gender],
        activity_levels=[a.value for a in ActivityLevel],
        dietary_preferences=[d.value for d in DietaryPreference],
        weight_goals=[w.value for w in WeightGoal],
        health_issues=[h.value for h in HealthIssue],
        fasting_statuses=[f.value for f in FastingStatus],
        supported_locations=SUPPORTED_LOCATIONS
    )

@app.post("/predict", response_model=NutritionalDetail, tags=["Nutrition"])
async def predict_nutritional_needs(user_input: UserInput):
    """Predict nutritional needs based on user profile."""
    return predict_nutrition(user_input)

@app.post("/recommend", response_model=RecommendationResponse, tags=["Meals"])
async def recommend_foods(request: RecommendationRequest):
    """Generate meal recommendations based on nutritional needs and preferences."""
    return recommend_meals(request)

@app.post("/predict-and-recommend", response_model=CombinedResponse, tags=["Nutrition and Meals"])
async def predict_and_recommend(request: CombinedInput):
    """Predict nutritional needs and generate meal recommendations."""
    try:
        user_input = UserInput(
            age=request.age,
            gender=request.gender,
            height=request.height,
            weight=request.weight,
            activity_level=request.activity_level,
            dietary_preference=request.dietary_preference,
            weight_goal=request.weight_goal,
            health_issues=request.health_issues
        )
        
        nutritional_detail = predict_nutrition(user_input)
        recommendation_request = RecommendationRequest(
            nutritional_detail=nutritional_detail,
            dietary_preference=request.dietary_preference,
            weight_goal=request.weight_goal,
            health_issues=request.health_issues,
            fasting=request.fasting,
            location=request.location
        )
        
        recommendations = recommend_meals(recommendation_request)
        return CombinedResponse(
            nutritional_detail=nutritional_detail,
            recommendations=recommendations
        )
    except Exception as e:
        logger.error(f"Predict and recommend error: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Processing failed: {str(e)}")

@app.get("/health", tags=["Utilities"])
async def health_check():
    """Check the health status of the API."""
    return {
        "status": "ok",
        "nutrition_model_ready": model is not None,
        "food_data_ready": not food_df.empty,
        "food_records": len(food_df)
    }

@app.get("/", include_in_schema=False)
async def root():
    """Root endpoint for the API."""
    return {"message": "Ethiopian Nutrition & Meal Recommendation API"}

# ==================== MAIN ====================

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)