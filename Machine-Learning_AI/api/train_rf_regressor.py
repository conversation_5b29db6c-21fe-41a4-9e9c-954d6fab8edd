import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MultiLabelBinarizer, OneHotEncoder
from sklearn.ensemble import RandomForestRegressor
from sklearn.multioutput import MultiOutputRegressor
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import PolynomialFeatures
import pickle
import logging
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Get the base directory (project root) relative to this script
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
DATA_PATH = os.path.join(BASE_DIR, 'datasets', 'user_nutritional_profiles.csv')
MODEL_PATH = os.path.join(BASE_DIR, 'model', 'rf_regressor_optimized.pkl')

# Load dataset
try:
    data = pd.read_csv(DATA_PATH)
    logging.info(f"Dataset loaded successfully. Initial records: {len(data)}")
except Exception as e:
    logging.error(f"Error loading dataset: {e}")
    raise

# Define variables
independent_vars = ['Ages', 'Gender', 'Height', 'Weight', 'Activity Level', 'Dietary Preference', 'Weight Goal', 'Health Issues']
dependent_vars = ['Daily Calorie Target', 'Protein', 'Sugar', 'Sodium', 'Calories', 'Carbohydrates', 'Fiber', 'Fat']

# Verify columns
missing_cols = [col for col in independent_vars + dependent_vars if col not in data.columns]
if missing_cols:
    logging.error(f"Missing columns: {missing_cols}")
    raise ValueError(f"Dataset missing columns: {missing_cols}")

# Data quality fixes
data['Sodium'] = data['Sodium'] * 1000  # Convert from grams to mg
data['Sugar'] = data['Sugar'].clip(upper=50)  # Cap Sugar at 50 g
logging.info("Applied data quality fixes: Sodium (g to mg), Sugar capped at 50 g")

# Data quality checks
logging.info("Data quality summary:")
logging.info(data[dependent_vars].describe().to_string())
correlation = data[['Daily Calorie Target', 'Calories']].corr().iloc[0, 1]
logging.info(f"Correlation between Daily Calorie Target and Calories: {correlation:.3f}")
if correlation > 0.95:
    logging.warning("High correlation detected. Removing 'Calories'.")
    dependent_vars.remove('Calories')

# Feature engineering
data['BMI'] = data['Weight'] / ((data['Height'] / 100) ** 2)
independent_vars.append('BMI')

# Preprocess Health Issues as multi-label
mlb = MultiLabelBinarizer()
data['Health Issues'] = data['Health Issues'].apply(lambda x: x.split(',') if isinstance(x, str) else ['None'])
health_issues_encoded = pd.DataFrame(mlb.fit_transform(data['Health Issues']), columns=[f"Health Issues_{c}" for c in mlb.classes_], index=data.index)
data = pd.concat([data, health_issues_encoded], axis=1)
independent_vars.remove('Health Issues')
independent_vars.extend(health_issues_encoded.columns)

# Vectorized outlier removal
def remove_outliers(df, columns):
    mask = np.ones(len(df), dtype=bool)
    for col in columns:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 2.0 * IQR
        upper_bound = Q3 + 2.0 * IQR
        mask &= (df[col] >= lower_bound) & (df[col] <= upper_bound)
    return df[mask]

data = remove_outliers(data, dependent_vars)
logging.info(f"Outliers removed. Remaining records: {len(data)}")

# Log-transform all targets
for col in dependent_vars:
    data[col] = np.log1p(data[col])
    logging.info(f"Log-transformed {col}")

# Extract features and targets
X = data[independent_vars]
y = data[dependent_vars]

# Handle missing values
for col in ['Ages', 'Height', 'Weight', 'BMI'] + [c for c in independent_vars if c.startswith('Health Issues_')]:
    if X[col].isna().any():
        X[col].fillna(0, inplace=True)
        logging.info(f"Filled missing values in {col} with 0")

for col in ['Gender', 'Activity Level', 'Dietary Preference', 'Weight Goal']:
    if X[col].isna().any():
        X[col].fillna(X[col].mode()[0], inplace=True)
        logging.info(f"Filled missing values in {col} with mode")

for col in dependent_vars:
    if y[col].isna().any():
        y[col].fillna(y[col].mean(), inplace=True)
        logging.info(f"Filled missing values in {col} with mean")

# Preprocessing pipeline
numerical_cols = ['Ages', 'Height', 'Weight', 'BMI']
categorical_cols = ['Gender', 'Activity Level', 'Dietary Preference', 'Weight Goal'] + [c for c in independent_vars if c.startswith('Health Issues_')]

preprocessor = ColumnTransformer(
    transformers=[
        ('num', Pipeline([
            ('scaler', StandardScaler()),
            ('poly', PolynomialFeatures(degree=2, interaction_only=True, include_bias=False))
        ]), numerical_cols),
        ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_cols)
    ])

# Define model pipeline
model = Pipeline([
    ('preprocessor', preprocessor),
    ('regressor', MultiOutputRegressor(RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        min_samples_split=2,
        random_state=42
    )))
])

# Split data
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
logging.info("Data split into training (80%) and testing (20%) sets")

# Train model
try:
    model.fit(X_train, y_train)
    logging.info("Model trained successfully")
except Exception as e:
    logging.error(f"Error training model: {e}")
    raise

# Evaluate model on test set
y_pred = model.predict(X_test)
for i, target in enumerate(dependent_vars):
    mse = mean_squared_error(y_test[target], y_pred[:, i])
    r2 = r2_score(y_test[target], y_pred[:, i])
    logging.info(f"{target} - MSE: {mse:.2f}, R²: {r2:.2f}")

# Feature importance
feature_names = (
    preprocessor.named_transformers_['num'].named_steps['poly'].get_feature_names_out(numerical_cols).tolist() +
    preprocessor.named_transformers_['cat'].get_feature_names_out().tolist()
)
importances = np.mean([est.feature_importances_ for est in model.named_steps['regressor'].estimators_], axis=0)
for name, importance in sorted(zip(feature_names, importances), key=lambda x: x[1], reverse=True)[:10]:
    logging.info(f"Feature: {name}, Importance: {importance:.3f}")

# Save model and MultiLabelBinarizer
try:
    with open(MODEL_PATH, 'wb') as f:
        pickle.dump({'model': model, 'mlb': mlb}, f)
    logging.info(f"Model and MultiLabelBinarizer saved as '{MODEL_PATH}'")
except Exception as e:
    logging.error(f"Error saving model: {e}")
    raise

# Example prediction function
def predict_nutrition(user_input, model_dict, dependent_vars):
    try:
        model = model_dict['model']
        mlb = model_dict['mlb']
        # Convert Health Issues to multi-label format
        health_issues = user_input['Health Issues'].split(',')
        health_issues_encoded = pd.DataFrame(mlb.transform([health_issues]), columns=[f"Health Issues_{c}" for c in mlb.classes_])
        # Prepare input
        input_data = {
            'Ages': user_input['Ages'],
            'Gender': user_input['Gender'],
            'Height': user_input['Height'],
            'Weight': user_input['Weight'],
            'Activity Level': user_input['Activity Level'],
            'Dietary Preference': user_input['Dietary Preference'],
            'Weight Goal': user_input['Weight Goal']
        }
        input_df = pd.DataFrame([input_data])
        input_df['BMI'] = input_df['Weight'] / ((input_df['Height'] / 100) ** 2)
        # Add health issues encoded columns
        input_df = pd.concat([input_df, health_issues_encoded], axis=1)
        predictions = model.predict(input_df)[0]
        for i, col in enumerate(dependent_vars):
            predictions[i] = np.expm1(predictions[i])  # Reverse log-transformation
        return dict(zip(dependent_vars, predictions))
    except Exception as e:
        logging.error(f"Prediction error: {e}")
        raise

# Example usage
example_user = {
    'Ages': 30,
    'Gender': 'Male',
    'Height': 170,
    'Weight': 70,
    'Activity Level': 'Moderately Active',
    'Dietary Preference': 'Omnivore',
    'Weight Goal': 'Maintain Weight',
    'Health Issues': 'Acne,Hypertension'
}

try:
    predictions = predict_nutrition(example_user, {'model': model, 'mlb': mlb}, dependent_vars)
    logging.info("Example prediction:")
    for target, value in predictions.items():
        logging.info(f"{target}: {value:.2f}")
except Exception as e:
    logging.error(f"Example prediction failed: {e}")