import pandas as pd
import logging
import os

# Set up logging (aligned with index.py)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('api.log')  # Use same log file as index.py
    ]
)

# Get the base directory (project root) relative to this script
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
FOOD_DATA_PATH = os.path.join(BASE_DIR, 'datasets', 'Ethiopian_Foods.csv')

# Load the dataset globally with error handling
try:
    food_df = pd.read_csv(FOOD_DATA_PATH)
    logging.info(f"Successfully loaded dataset from {FOOD_DATA_PATH}")
except FileNotFoundError:
    logging.error(f"Dataset file '{FOOD_DATA_PATH}' not found")
    food_df = pd.DataFrame()  # Empty DataFrame to prevent crashes
except Exception as e:
    logging.error(f"Error loading dataset: {e}", exc_info=True)
    food_df = pd.DataFrame()

def get_available_locations() -> list:
    """
    Retrieve unique locations from the Ethiopian_Foods.csv dataset.

    Returns:
        list: Sorted list of unique locations, or empty list if an error occurs or no locations are found.
    """
    try:
        # Check if DataFrame is empty
        if food_df.empty:
            logging.error("Dataset is empty or not loaded")
            return []

        # Check if 'Location' column exists
        if 'Location' not in food_df.columns:
            logging.error("Dataset does not contain a 'Location' column")
            return []

        # Clean and standardize 'Location' column (as in index.py)
        locations = food_df['Location'].str.strip().str.lower()

        # Get unique locations, excluding NaN
        unique_locations = sorted(locations.dropna().unique())

        if not unique_locations:
            logging.warning("No valid locations found in the dataset")
        else:
            logging.info(f"Retrieved {len(unique_locations)} unique locations")

        return unique_locations

    except Exception as e:
        logging.error(f"Error processing locations: {e}", exc_info=True)
        return []

def main():
    """Main function to retrieve and print available locations."""
    locations = get_available_locations()

    if locations:
        print("Available locations in the dataset:")
        for loc in locations:
            print(f"- {loc}")
        print(f"\nTotal unique locations: {len(locations)}")
    else:
        print("No locations found. Check the dataset and 'api.log' for errors.")

if __name__ == "__main__":
    main()