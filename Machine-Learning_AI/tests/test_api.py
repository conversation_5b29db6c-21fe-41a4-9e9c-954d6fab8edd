import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from api.index import app
from fastapi.testclient import TestClient

client = TestClient(app)

def test_health_endpoint():
    response = client.get("/health")
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["status"] == "ok"
    assert "nutrition_model_ready" in response_json
    assert "food_data_ready" in response_json
    assert "food_records" in response_json
    assert isinstance(response_json["nutrition_model_ready"], bool)
    assert isinstance(response_json["food_data_ready"], bool)
    assert isinstance(response_json["food_records"], int)
    assert response_json["food_records"] >= 0