import pytest
import pandas as pd
import os
from unittest.mock import MagicMock
from fastapi.testclient import TestClient
from api.index import (
    app, NutritionalDetail, DietaryPreference, WeightGoal, HealthIssue,
    FastingStatus, RecommendationRequest, REGION_GROUPS
)

@pytest.fixture
def sample_food_df():
    csv_path = os.path.join(os.path.dirname(__file__), "fixtures/sample_food_data.csv")
    if not os.path.exists(csv_path):
        raise FileNotFoundError(f"Sample food data file not found at: {csv_path}")
    df = pd.read_csv(csv_path, encoding='utf-8')
    df['Location'] = df['Location'].str.strip().str.lower()
    df['Category'] = df['Category'].str.strip().str.lower()
    numeric_cols = ['Food Energy', 'Protein', 'Fat', 'CHO (inc.fiber)', 'Fiber', 'Sodium']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
    if isinstance(REGION_GROUPS, dict):
        region_map = {k.lower(): v for k, v in REGION_GROUPS.items()}
    else:
        raise ValueError(f"Unsupported REGION_GROUPS type: {type(REGION_GROUPS)}")
    df['Region'] = df['Location'].map(region_map).fillna('other')
    return df

@pytest.fixture
def sample_model():
    model_mock = MagicMock()
    model_mock.predict.return_value = [[
        7.6, 4.1, 3.4, 0.7, 7.6, 5.5, 3.4, 4.2
    ]]
    mlb_mock = MagicMock()
    mlb_mock.classes_ = ['None', 'Diabetes', 'Hypertension', 'Heart Disease', 'Kidney Disease', 'Acne']
    mlb_mock.transform.return_value = [[1, 0, 0, 0, 0, 0]]
    return {'model': model_mock, 'mlb': mlb_mock}

@pytest.fixture
def nutritional_detail():
    return NutritionalDetail(
        daily_calorie_target=2000.0,
        protein=60.0,
        sugar=30.0,
        sodium=1500.0,
        calories=2000.0,
        carbohydrates=250.0,
        fiber=30.0,
        fat=65.0
    )

@pytest.fixture
def recommendation_request(nutritional_detail):
    return RecommendationRequest(
        nutritional_detail=nutritional_detail,
        dietary_preference=DietaryPreference.vegan,
        weight_goal=WeightGoal.maintain,
        health_issues=[HealthIssue.none],
        fasting=FastingStatus.no,
        location="addis ababa"
    )

@pytest.fixture
def test_client(mocker, sample_food_df, sample_model):
    mocker.patch('api.index.food_df', sample_food_df)
    mocker.patch('api.index.urban_foods_df', sample_food_df[sample_food_df['Location'].isin(['urban', 'common', 'urban/common'])])
    mocker.patch('api.index.model', sample_model['model'])
    mocker.patch('api.index.mlb', sample_model['mlb'])
    return TestClient(app)