# NutriGeo: Ethiopian Meal Recommendation System

![Ethiopian Cuisine](https://via.placeholder.com/800x400?text=Ethiopian+Food+Recommendation+System)

A machine learning-powered API that recommends personalized Ethiopian meals based on user profiles and nutritional needs.

## 🚀 Local Development Setup

### Prerequisites
- Python 3.9
- Git

### Installation
```bash
# Clone repository
git clone https://github.com/yourusername/nutrigeo.git
cd nutrigeo

# Create virtual environment
python3.9 -m venv venv

# Activate environment
source venv/bin/activate  # Linux/Mac
.\venv\Scripts\activate   # Windows

# Install dependencies
pip install -r requirements.txt
```

### 🏃 Running the Application
```bash
# Using start.sh script (recommended)
chmod +x start.sh  # Make executable
./start.sh         # Default port: 3000

# Or manually specify port
PORT=4000 ./start.sh
```

### 🌐 Access Endpoints
- API Docs: `http://localhost:3000/docs`
- Health Check: `http://localhost:3000/`
- Prediction Endpoint: `POST http://localhost:3000/predict`

## 📦 Project Structure
```
nutrigeo/
├── api/
│   ├── index.py           # Main FastAPI application
│   ├── *.pkl              # ML model files
├── datasets/
│   └── NutriGeo_Ethiopian_Meal_Recommendations.csv
├── requirements.txt       # Python dependencies
├── start.sh              # Launch script
└── README.md
```

## 🛠️ Deployment (Non-Docker)

### Render.com Setup
1. Create new **Web Service**
2. Select **Python** environment
3. Configure:
   - Build Command: `pip install -r requirements.txt`
   - Start Command: `uvicorn api.index:app --host 0.0.0.0 --port $PORT`
   - Environment Variables:
     - `PORT`: 3000
     - `PYTHONHASHSEED`: 42

## 🔍 Technical Details

### Machine Learning Models
| Model Type | Target Variables |
|------------|------------------|
| Regression | Calories, Protein, Carbs, Fat |
| Classification | Breakfast, Lunch, Dinner |

### Dependencies
Core packages pinned for stability:
```txt
scikit-learn==1.5.0
joblib==1.2.0
pandas==1.5.3
fastapi==0.95.2
```

## 📜 License
MIT License