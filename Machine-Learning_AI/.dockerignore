# Ignore Python cache files
__pycache__/
*.pyc
*.pyo
*.pyd

# Ignore logs
*.log
api.log

# Ignore IDE and editor files
.vscode/
.idea/
*.swp
*.swo

# Ignore environment files
.env
.venv/
venv/
env/

# Ignore Docker-specific files
docker-compose.yml
*.dockerignore

# Ignore Git files
.git/
.gitignore

# Ignore temporary files
*.bak
*.tmp
*.temp

# Ignore testing and build artifacts
*.egg-info/
build/
dist/
*.coverage
coverage.xml
.pytest_cache/