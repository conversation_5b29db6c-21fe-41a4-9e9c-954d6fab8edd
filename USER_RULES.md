# User Rules - Nutrition Recommendation System

## Quick Development Guidelines

### 🚀 **Project Structure**
- **Backend**: NestJS v11 + TypeScript + PostgreSQL + Drizzle ORM
- **Frontend**: Next.js 15 + React 18 + TypeScript + Tailwind CSS  
- **ML Service**: FastAPI + Python + scikit-learn

### 📝 **Code Standards**

#### TypeScript/JavaScript
- Use **strict TypeScript** with proper types
- Prefer **const** over let, **destructuring** for cleaner code
- Use **async/await** instead of Promises
- Implement **proper error handling** with try-catch

#### React/Next.js
- Use **functional components** with hooks
- **Zustand** for global state management
- **TypeScript interfaces** for props/state
- **Radix UI** components for accessibility
- **Server Components** where appropriate

#### NestJS Backend
- Follow **NestJS decorators** and dependency injection
- Use **DTOs** for request/response validation
- Implement **JWT authentication** with Guards
- Use **class-validator** for input validation
- **Swagger** for API documentation

#### Python/FastAPI
- Use **type hints** throughout
- Follow **PEP 8** style guidelines
- Use **Pydantic models** for validation
- Implement **proper async/await** patterns

### 🔒 **Security Rules**
- **Never commit secrets** to version control
- Use **environment variables** for sensitive data
- **Validate all inputs** on client and server
- Implement **rate limiting** for API endpoints
- Use **HTTPS** in production

### 🧪 **Testing Requirements**
- **Unit tests** for all business logic
- **Integration tests** for API endpoints
- **E2E tests** for critical user flows
- Maintain **>80% test coverage**

### 🚀 **Performance Guidelines**
- **Optimize database queries** with proper indexes
- **Implement caching** for frequently accessed data
- **Code splitting** for frontend bundles
- **Lazy loading** for components and routes
- **Image optimization** with Next.js Image

### 📋 **Git Workflow**
- Use **conventional commits**: `feat:`, `fix:`, `docs:`, `style:`, `refactor:`, `test:`, `chore:`
- **Branch naming**: `feature/feature-name`, `bugfix/issue-description`
- **Pull request reviews** for all changes
- **Squash commits** before merging

### 🛠 **Development Commands**

```bash
# Backend
pnpm start:dev          # Start development server
pnpm test              # Run tests
pnpm lint              # Lint code
pnpm biome:check       # Format code

# Frontend  
npm run dev            # Start development server
npm run build          # Build for production
npm run lint           # Lint code

# ML Service
python -m pytest       # Run tests
black .                # Format code
flake8 .               # Lint code

# Database
pnpm drizzle-kit generate  # Generate migration
pnpm drizzle-kit push      # Apply migrations
```

### 🔍 **Code Review Checklist**
- [ ] **Security**: Input validation, authentication, no sensitive data
- [ ] **Performance**: Optimized queries, proper caching, bundle size
- [ ] **Quality**: Tests passing, follows style guidelines, error handling
- [ ] **Accessibility**: ARIA labels, keyboard navigation, color contrast

### 🚨 **Emergency Procedures**
- **Rollback procedures** documented
- **Emergency contacts** established
- **Monitoring alerts** configured
- **Backup procedures** tested

### 📚 **Documentation**
- **JSDoc comments** for functions and classes
- **README files** for each module
- **API documentation** with Swagger
- **Database schema** documentation

---

**Remember**: These rules ensure code quality, security, and maintainability. Follow them consistently for a robust, scalable application. 