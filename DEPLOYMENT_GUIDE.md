# NutriFocus Deployment Guide

This guide covers deploying the NutriFocus application both locally (for testing) and on AWS EC2 (for production).

## Table of Contents

1. [Local Production Simulation](#local-production-simulation)
2. [AWS EC2 Production Deployment](#aws-ec2-production-deployment)
3. [Environment Configuration](#environment-configuration)
4. [CORS and Cookie Configuration](#cors-and-cookie-configuration)
5. [SSL Certificate Setup](#ssl-certificate-setup)
6. [Monitoring and Maintenance](#monitoring-and-maintenance)
7. [Troubleshooting](#troubleshooting)

## Local Production Simulation

### Prerequisites

- Docker and Docker Compose installed
- OpenSSL for SSL certificate generation
- sudo access for hosts file modification

### Quick Start

1. **Navigate to the local production setup directory:**
   ```bash
   cd local-production-setup
   ```

2. **Run the setup script:**
   ```bash
   chmod +x setup-local-production.sh
   ./setup-local-production.sh
   ```

3. **Update environment variables:**
   - Edit `../Frontend_client-side/.env.local`
   - Edit `../Backend_server-side/.env.local`
   - Replace placeholder values with your actual configuration

4. **Start the local production environment:**
   ```bash
   ./start-local-production.sh
   ```

5. **Access your application:**
   - Frontend: https://nutrifocus.duckdns.org
   - Backend API: https://nutrifocus.duckdns.org/api
   - Swagger Docs: https://nutrifocus.duckdns.org/docs

### Local Environment Structure

```
local-production-setup/
├── nginx.conf                 # Nginx reverse proxy configuration
├── docker-compose.local.yml   # Docker Compose for local testing
├── setup-local-production.sh  # Setup script
├── start-local-production.sh  # Start script
├── stop-local-production.sh   # Stop script
├── cleanup-local-production.sh # Cleanup script
└── ssl/                       # SSL certificates directory
    ├── certificate.crt
    └── private.key
```

### Important Notes for Local Testing

- **SSL Certificate**: You'll need to accept the self-signed certificate in your browser
- **Google OAuth**: Update your Google OAuth redirect URIs to include `https://nutrifocus.duckdns.org`
- **CORS**: The configuration allows requests from the local domain
- **Cookies**: Set to work with the local domain

## AWS EC2 Production Deployment

### Prerequisites

- AWS EC2 instance (Ubuntu 20.04+ recommended)
- Domain name pointing to your EC2 instance
- SSH access to the EC2 instance
- Root or sudo access

### Deployment Steps

1. **Connect to your EC2 instance:**
   ```bash
   ssh -i your-key.pem ubuntu@your-ec2-ip
   ```

2. **Clone your repository:**
   ```bash
   git clone https://github.com/your-username/nutrifocus.git
   cd nutrifocus
   ```

3. **Run the deployment script:**
   ```bash
   cd production-deployment
   chmod +x deploy.sh
   sudo ./deploy.sh
   ```

4. **Update environment variables:**
   ```bash
   sudo nano /opt/nutrifocus/frontend.env
   sudo nano /opt/nutrifocus/backend.env
   ```

5. **Restart services:**
   ```bash
   sudo systemctl restart nutrifocus.service
   ```

### Production Environment Structure

```
/opt/nutrifocus/
├── docker-compose.prod.yml    # Production Docker Compose
├── frontend.env              # Frontend environment variables
├── backend.env               # Backend environment variables
├── monitor.sh                # Monitoring script
├── backup.sh                 # Backup script
└── data/                     # Application data
    └── postgres/             # PostgreSQL data
```

## Environment Configuration

### Frontend Environment Variables

```bash
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://nutrifocus.duckdns.org
NEXT_PUBLIC_COOKIE_DOMAIN=nutrifocus.duckdns.org
```

### Backend Environment Variables

```bash
NODE_ENV=production
PORT=4000
POSTGRES_HOST=nutrifocus-postgres
POSTGRES_PORT=5432
POSTGRES_USER=nutrifocus_user
POSTGRES_PASSWORD=your-secure-postgres-password
POSTGRES_DB=nutrifocus_prod
FRONTEND_URL=https://nutrifocus.duckdns.org
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_EXPIRES_IN=2h
JWT_ACCESS_TOKEN_SECRET=your-access-token-secret
JWT_ACCESS_TOKEN_EXPIRATION_TIME=2h
JWT_REFRESH_TOKEN_SECRET=your-refresh-token-secret
JWT_REFRESH_TOKEN_EXPIRATION_TIME=24h
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_VERIFICATION_SECRET=your-email-verification-secret
ML_API_URL=http://nutrifocus-ml:8000
THROTTLE_TTL=60
THROTTLE_LIMIT=100
PASSWORD_RESET_SECRET=your-password-reset-secret
GOOGLE_AUTH_CLIENT_ID=your-google-client-id
GOOGLE_AUTH_CLIENT_SECRET=your-google-client-secret
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret
```

## CORS and Cookie Configuration

### Backend CORS Configuration

The backend is configured to allow requests from:
- `https://nutrifocus.duckdns.org`
- `https://nutrifocusmealplan.duckdns.org`
- `https://localhost:3000` (for local development)
- `http://localhost:3000` (for local development)

### Cookie Configuration

Cookies are configured with:
- **Domain**: `nutrifocus.duckdns.org`
- **Secure**: `true` (HTTPS only)
- **SameSite**: `lax`
- **HttpOnly**: `true` (for sensitive cookies)

### Frontend Cookie Handling

The frontend uses the `js-cookie` library with:
- **Domain**: Set via `NEXT_PUBLIC_COOKIE_DOMAIN`
- **Secure**: `true`
- **SameSite**: `lax`

## SSL Certificate Setup

### Local Development

Self-signed certificates are generated automatically by the setup script.

### Production (Let's Encrypt)

1. **Automatic Setup**: The deployment script automatically sets up Let's Encrypt certificates
2. **Manual Setup** (if needed):
   ```bash
   sudo certbot --nginx -d nutrifocus.duckdns.org
   ```

3. **Auto-renewal**: Certificates auto-renew via cron job

## Monitoring and Maintenance

### Service Management

```bash
# Check service status
sudo systemctl status nutrifocus.service

# Restart services
sudo systemctl restart nutrifocus.service

# View logs
sudo docker-compose -f /opt/nutrifocus/docker-compose.prod.yml logs -f

# Monitor specific service
sudo docker logs -f nutrifocus-frontend
sudo docker logs -f nutrifocus-backend
```

### Automated Monitoring

- **Health Checks**: Every 5 minutes
- **Auto-restart**: Failed services are automatically restarted
- **Log Rotation**: Logs are automatically rotated

### Backup Strategy

- **Daily Backups**: Automatic daily backups at 2 AM
- **Retention**: Last 7 backups kept
- **Location**: `/opt/nutrifocus/backups/`

### Performance Monitoring

```bash
# Check resource usage
docker stats

# Monitor disk usage
df -h

# Check memory usage
free -h

# Monitor network connections
netstat -tulpn
```

## Troubleshooting

### Common Issues

#### 1. SSL Certificate Issues

**Problem**: Browser shows SSL warning
**Solution**: 
- Local: Accept the self-signed certificate
- Production: Check Let's Encrypt certificate renewal

#### 2. CORS Errors

**Problem**: Frontend can't connect to backend
**Solution**:
- Check CORS configuration in backend
- Verify domain names match
- Check Nginx configuration

#### 3. Cookie Issues

**Problem**: Authentication not working
**Solution**:
- Verify cookie domain configuration
- Check HTTPS requirement
- Ensure SameSite policy is correct

#### 4. Database Connection Issues

**Problem**: Backend can't connect to PostgreSQL
**Solution**:
- Check PostgreSQL container status
- Verify environment variables
- Check network connectivity

### Debug Commands

```bash
# Check all containers
docker ps -a

# Check container logs
docker logs nutrifocus-backend
docker logs nutrifocus-frontend

# Check Nginx configuration
sudo nginx -t

# Check SSL certificate
sudo certbot certificates

# Test database connection
docker exec nutrifocus-postgres pg_isready

# Check network connectivity
docker network ls
docker network inspect nutrifocus-network
```

### Log Locations

- **Application Logs**: `/var/log/nutrifocus/`
- **Nginx Logs**: `/var/log/nginx/`
- **System Logs**: `/var/log/syslog`
- **Docker Logs**: `docker logs <container-name>`

## Security Best Practices

### 1. Environment Variables

- Never commit secrets to version control
- Use strong, unique passwords
- Rotate secrets regularly

### 2. Network Security

- Configure firewall rules
- Use HTTPS only
- Implement rate limiting

### 3. Application Security

- Keep dependencies updated
- Use security headers
- Implement proper authentication

### 4. Database Security

- Use strong database passwords
- Limit database access
- Regular backups

## Performance Optimization

### 1. Nginx Configuration

- Enable gzip compression
- Configure caching headers
- Use HTTP/2

### 2. Docker Optimization

- Use multi-stage builds
- Optimize image sizes
- Configure resource limits

### 3. Database Optimization

- Configure connection pooling
- Use proper indexes
- Regular maintenance

## Support and Maintenance

### Regular Tasks

- **Weekly**: Check logs for errors
- **Monthly**: Update dependencies
- **Quarterly**: Security audit
- **Annually**: Performance review

### Emergency Procedures

1. **Service Down**: Check logs and restart services
2. **Database Issues**: Restore from backup
3. **SSL Issues**: Renew certificates manually
4. **Security Breach**: Rotate all secrets

### Contact Information

For issues and support:
- Check logs first
- Review this documentation
- Create an issue in the repository

---

## Quick Reference Commands

### Local Development
```bash
cd local-production-setup
./setup-local-production.sh
./start-local-production.sh
```

### Production Deployment
```bash
cd production-deployment
sudo ./deploy.sh
sudo systemctl restart nutrifocus.service
```

### Monitoring
```bash
sudo docker-compose -f /opt/nutrifocus/docker-compose.prod.yml logs -f
sudo systemctl status nutrifocus.service
```

### Backup
```bash
sudo /opt/nutrifocus/backup.sh
```

This deployment guide ensures a smooth transition from development to production while maintaining security, performance, and reliability. 