# Modern Development Rules for Cursor

## Project Overview
This is a full-stack nutrition recommendation system with:
- **Backend**: NestJS with TypeScript, PostgreSQL, Drizzle ORM
- **Frontend**: Next.js 15 with React 18, <PERSON>Script, Tailwind CSS
- **ML Service**: FastAPI with Python, scikit-learn
- **Testing**: Jest, pytest, E2E testing
- **Code Quality**: <PERSON><PERSON>int, Biome, <PERSON><PERSON>er, Black, Flake8

## Code Style & Standards

### TypeScript/JavaScript
- Use **strict TypeScript** with proper type annotations
- Prefer **const assertions** for immutable data
- Use **destructuring** for cleaner code
- Implement **proper error handling** with try-catch blocks
- Use **async/await** over Promises when possible
- Follow **functional programming** principles where appropriate
- Use **discriminated unions** for better type safety
- Implement **proper null checking** with optional chaining

### React/Next.js Frontend
- Use **functional components** with hooks
- Implement **proper state management** (Zustand for global state)
- Use **TypeScript interfaces** for props and state
- Follow **component composition** patterns
- Implement **proper loading states** and error boundaries
- Use **Next.js App Router** patterns and conventions
- Implement **responsive design** with Tailwind CSS
- Use **Radix UI** components for accessibility
- Implement **Server Components** where appropriate
- Use **React Suspense** for loading states
- Implement **proper SEO** with Next.js metadata

### NestJS Backend
- Follow **NestJS decorators** and dependency injection patterns
- Use **DTOs** for request/response validation
- Implement **proper authentication** with JWT
- Use **Guards** for route protection
- Follow **RESTful API** conventions
- Use **class-validator** for input validation
- Implement **proper error handling** with filters
- Use **Swagger** for API documentation
- Implement **OpenAPI 3.0** specifications
- Use **custom decorators** for reusable logic

### Python/FastAPI ML Service
- Use **type hints** throughout the codebase
- Follow **PEP 8** style guidelines
- Use **Pydantic models** for data validation
- Implement **proper async/await** patterns
- Use **dependency injection** for services
- Follow **FastAPI best practices** for API design
- Use **dataclasses** for simple data structures
- Implement **proper logging** with structured format

## Modern Development Practices

### AI-Assisted Development
- Use **GitHub Copilot** for code suggestions
- Leverage **Cursor AI** for code generation and refactoring
- Implement **AI-powered code review** tools
- Use **AI for test generation** and documentation
- **Review AI-generated code** thoroughly before committing

### Microservices Communication
- Use **REST APIs** for synchronous communication
- Implement **message queues** for asynchronous communication
- Use **gRPC** for high-performance inter-service communication
- Implement **circuit breakers** for fault tolerance
- Use **API gateways** for centralized routing

### Containerization & Orchestration
- Use **multi-stage Docker builds** for optimization
- Implement **health checks** in containers
- Use **Docker Compose** for local development
- Consider **Kubernetes** for production orchestration
- Implement **proper resource limits** and requests

### Observability
- Implement **distributed tracing** with OpenTelemetry
- Use **structured logging** with correlation IDs
- Implement **metrics collection** with Prometheus
- Use **alerting** for critical issues
- Implement **dashboard monitoring** with Grafana

### Feature Flags & A/B Testing
- Implement **feature flags** for gradual rollouts
- Use **A/B testing** for user experience optimization
- Implement **canary deployments** for risk mitigation
- Use **blue-green deployments** for zero-downtime updates

## Architecture Patterns

### Backend Architecture
- **Modular design** with separate modules for each feature
- **Service layer** for business logic
- **Repository pattern** for data access
- **DTO pattern** for data transfer
- **Guard pattern** for authentication/authorization
- **Interceptor pattern** for cross-cutting concerns

### Frontend Architecture
- **Component-based architecture** with reusable components
- **Custom hooks** for shared logic
- **Context API** for theme/auth state
- **Service layer** for API calls
- **Store pattern** for global state management
- **Error boundary** for error handling

### Database Design
- Use **Drizzle ORM** for type-safe database operations
- Implement **proper migrations** with versioning
- Use **indexes** for performance optimization
- Follow **normalization** principles
- Implement **soft deletes** where appropriate

## Testing Strategy

### Unit Testing
- **Jest** for JavaScript/TypeScript unit tests
- **pytest** for Python unit tests
- **Mock external dependencies**
- **Test business logic** thoroughly
- **Maintain high test coverage** (>80%)

### Integration Testing
- **Test API endpoints** with supertest
- **Test database operations** with test database
- **Test authentication flows**
- **Test error scenarios**

### E2E Testing
- **Test complete user workflows**
- **Test cross-browser compatibility**
- **Test responsive design**
- **Test accessibility features**

## Security Best Practices

### Authentication & Authorization
- Use **JWT tokens** with proper expiration
- Implement **role-based access control**
- Use **secure password hashing** (bcrypt)
- Implement **rate limiting** for API endpoints
- Use **HTTPS** in production
- Implement **CORS** properly

### Data Validation
- **Validate all inputs** on both client and server
- Use **Zod** for frontend validation
- Use **class-validator** for backend validation
- Use **Pydantic** for Python validation
- **Sanitize user inputs** to prevent XSS

### Environment & Configuration
- Use **environment variables** for sensitive data
- **Never commit secrets** to version control
- Use **different configurations** for different environments
- Implement **proper logging** without sensitive data

## Performance Optimization

### Frontend Performance
- **Code splitting** with dynamic imports
- **Image optimization** with Next.js Image component
- **Lazy loading** for components and routes
- **Memoization** with React.memo and useMemo
- **Bundle optimization** with proper tree shaking

### Backend Performance
- **Database query optimization** with proper indexes
- **Caching** for frequently accessed data
- **Connection pooling** for database connections
- **Async operations** for I/O intensive tasks
- **Rate limiting** to prevent abuse

### API Performance
- **Pagination** for large datasets
- **Compression** for API responses
- **Caching headers** for static content
- **Optimistic updates** for better UX

## Development Workflow

### Git Practices
- Use **conventional commits** (feat:, fix:, docs:, etc.)
- **Branch naming**: feature/feature-name, bugfix/issue-description
- **Pull request reviews** for all changes
- **Squash commits** before merging
- **Keep commits atomic** and focused

### Code Review Guidelines
- **Review for security** vulnerabilities
- **Check for performance** implications
- **Ensure proper error handling**
- **Verify accessibility** compliance
- **Check for code duplication**
- **Review test coverage**

### Documentation
- **JSDoc comments** for functions and classes
- **README files** for each module
- **API documentation** with Swagger
- **Component documentation** with Storybook (if applicable)
- **Database schema documentation**

## Error Handling

### Frontend Error Handling
- **Error boundaries** for React components
- **Toast notifications** for user feedback
- **Fallback UI** for failed components
- **Retry mechanisms** for failed API calls
- **Proper loading states**

### Backend Error Handling
- **Global exception filters** in NestJS
- **Proper HTTP status codes**
- **Structured error responses**
- **Logging** for debugging
- **Graceful degradation**

## Accessibility (A11y)

### Frontend Accessibility
- **Semantic HTML** elements
- **ARIA labels** and roles
- **Keyboard navigation** support
- **Screen reader** compatibility
- **Color contrast** compliance
- **Focus management**

### API Accessibility
- **Consistent error responses**
- **Clear documentation**
- **Rate limiting information**
- **Versioning** for API changes

## Monitoring & Logging

### Application Monitoring
- **Error tracking** with proper context
- **Performance monitoring** for API endpoints
- **User analytics** (privacy-compliant)
- **Health checks** for services

### Logging Strategy
- **Structured logging** with consistent format
- **Log levels** (error, warn, info, debug)
- **Sensitive data masking**
- **Log aggregation** for analysis

## Deployment & DevOps

### Environment Management
- **Docker** containers for consistency
- **Environment-specific** configurations
- **Health checks** for services
- **Graceful shutdown** handling

### CI/CD Pipeline
- **Automated testing** on every commit
- **Code quality checks** (linting, formatting)
- **Security scanning** for dependencies
- **Automated deployment** to staging/production

## Code Quality Tools

### Linting & Formatting
- **ESLint** for JavaScript/TypeScript
- **Biome** for code formatting
- **Prettier** for consistent formatting
- **Black** for Python formatting
- **Flake8** for Python linting

### Type Checking
- **TypeScript** strict mode
- **MyPy** for Python type checking
- **Zod** for runtime type validation

## Database Guidelines

### Schema Design
- **Consistent naming** conventions
- **Proper foreign key** relationships
- **Indexes** for performance
- **Constraints** for data integrity
- **Migrations** for schema changes

### Query Optimization
- **Avoid N+1 queries**
- **Use proper joins**
- **Implement pagination**
- **Cache frequently accessed data**

## API Design Principles

### RESTful Design
- **Resource-based URLs**
- **Proper HTTP methods**
- **Consistent response formats**
- **Versioning strategy**
- **Pagination and filtering**

## State Management

### Frontend State
- **Zustand** for global state
- **React Query** for server state
- **Local state** for component-specific data
- **Optimistic updates** for better UX

### Backend State
- **Stateless design** where possible
- **Session management** with Redis
- **Cache invalidation** strategies

## Security Headers

### Frontend Security
- **Content Security Policy (CSP)**
- **X-Frame-Options**
- **X-Content-Type-Options**
- **Referrer Policy**

### Backend Security
- **Helmet.js** for security headers
- **CORS** configuration
- **Rate limiting**
- **Input validation**

## Performance Budgets

### Frontend Performance
- **Bundle size** limits
- **Lighthouse scores** targets
- **Core Web Vitals** optimization
- **Image optimization** targets

### Backend Performance
- **Response time** targets
- **Database query** performance
- **Memory usage** limits
- **CPU utilization** targets

## Accessibility Standards

### WCAG Compliance
- **WCAG 2.1 AA** compliance
- **Keyboard navigation** support
- **Screen reader** compatibility
- **Color contrast** requirements

### Testing Accessibility
- **Automated testing** with axe-core
- **Manual testing** with screen readers
- **Keyboard-only** navigation testing
- **Color blindness** testing

## Internationalization (i18n)

### Frontend i18n
- **Next.js i18n** support
- **Translation files** organization
- **RTL language** support
- **Date/time** formatting

### Backend i18n
- **Error message** localization
- **API response** localization
- **Database** multilingual support

## Data Privacy & GDPR

### Privacy Compliance
- **Data minimization** principles
- **User consent** management
- **Data retention** policies
- **Right to be forgotten** implementation

### Security Measures
- **Data encryption** at rest and in transit
- **Access controls** and audit logs
- **Regular security** assessments
- **Incident response** procedures

## Code Review Checklist

### Security Review
- [ ] Input validation implemented
- [ ] Authentication/authorization checked
- [ ] SQL injection prevention
- [ ] XSS protection
- [ ] CSRF protection
- [ ] Sensitive data handling

### Performance Review
- [ ] Database queries optimized
- [ ] Frontend bundle size reasonable
- [ ] Caching implemented where appropriate
- [ ] Lazy loading used
- [ ] Image optimization applied

### Quality Review
- [ ] Tests written and passing
- [ ] Code follows style guidelines
- [ ] Documentation updated
- [ ] Error handling implemented
- [ ] Accessibility requirements met

## Emergency Procedures

### Incident Response
- **Rollback procedures** documented
- **Emergency contacts** established
- **Monitoring alerts** configured
- **Communication plan** ready

### Data Recovery
- **Backup procedures** tested
- **Recovery time** objectives defined
- **Data integrity** verification
- **Business continuity** planning

---

## Quick Reference Commands

### Development
```bash
# Backend
pnpm start:dev          # Start development server
pnpm test              # Run tests
pnpm lint              # Lint code
pnpm biome:check       # Format code

# Frontend
npm run dev            # Start development server
npm run build          # Build for production
npm run lint           # Lint code

# ML Service
python -m pytest       # Run tests
black .                # Format code
flake8 .               # Lint code
```

### Database
```bash
# Generate migration
pnpm drizzle-kit generate

# Apply migrations
pnpm drizzle-kit push

# Reset database
pnpm drizzle-kit drop
```

### Docker
```bash
# Build and run
docker-compose up --build

# Run in background
docker-compose up -d

# View logs
docker-compose logs -f
```

Remember: These rules are living guidelines. Update them as the project evolves and new best practices emerge. 