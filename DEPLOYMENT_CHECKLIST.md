# NutriFocus Deployment Checklist

Use this checklist to ensure all configurations are properly set up before and after deployment.

## Pre-Deployment Checklist

### ✅ Environment Variables

#### Frontend (.env.local)
- [ ] `NEXT_PUBLIC_API_URL=https://nutrifocus.duckdns.org`
- [ ] `NEXT_PUBLIC_COOKIE_DOMAIN=nutrifocus.duckdns.org`
- [ ] `NODE_ENV=production`

#### Backend (.env.local)
- [ ] `NODE_ENV=production`
- [ ] `PORT=4000`
- [ ] `POSTGRES_HOST=postgres` (local) or `nutrifocus-postgres` (production)
- [ ] `POSTGRES_PORT=5432`
- [ ] `POSTGRES_USER=postgres` (local) or `nutrifocus_user` (production)
- [ ] `POSTGRES_PASSWORD=secure-password`
- [ ] `POSTGRES_DB=nutrifocus` (local) or `nutrifocus_prod` (production)
- [ ] `FRONTEND_URL=https://nutrifocus.duckdns.org`
- [ ] `JWT_SECRET=your-super-secure-jwt-secret-key`
- [ ] `JWT_EXPIRES_IN=2h`
- [ ] `JWT_ACCESS_TOKEN_SECRET=your-access-token-secret`
- [ ] `JWT_ACCESS_TOKEN_EXPIRATION_TIME=2h`
- [ ] `JWT_REFRESH_TOKEN_SECRET=your-refresh-token-secret`
- [ ] `JWT_REFRESH_TOKEN_EXPIRATION_TIME=24h`
- [ ] `EMAIL_SERVICE=gmail`
- [ ] `EMAIL_USER=<EMAIL>`
- [ ] `EMAIL_PASSWORD=your-app-password`
- [ ] `EMAIL_VERIFICATION_SECRET=your-email-verification-secret`
- [ ] `ML_API_URL=http://ml-service:8000` (local) or `http://nutrifocus-ml:8000` (production)
- [ ] `THROTTLE_TTL=60`
- [ ] `THROTTLE_LIMIT=100`
- [ ] `PASSWORD_RESET_SECRET=your-password-reset-secret`
- [ ] `GOOGLE_AUTH_CLIENT_ID=your-google-client-id`
- [ ] `GOOGLE_AUTH_CLIENT_SECRET=your-google-client-secret`
- [ ] `CLOUDINARY_CLOUD_NAME=your-cloudinary-name`
- [ ] `CLOUDINARY_API_KEY=your-cloudinary-api-key`
- [ ] `CLOUDINARY_API_SECRET=your-cloudinary-api-secret`

### ✅ External Service Configuration

#### Google OAuth
- [ ] Add `https://nutrifocus.duckdns.org/auth/google/callback` to authorized redirect URIs
- [ ] Add `https://nutrifocus.duckdns.org` to authorized JavaScript origins
- [ ] Verify client ID and secret are correct

#### Cloudinary
- [ ] Verify cloud name, API key, and secret are correct
- [ ] Test image upload functionality

#### Email Service (Gmail)
- [ ] Enable 2-factor authentication
- [ ] Generate app password
- [ ] Test email sending functionality

### ✅ SSL Certificate

#### Local Development
- [ ] Self-signed certificate generated
- [ ] Certificate accepted in browser
- [ ] HTTPS working properly

#### Production
- [ ] Domain points to EC2 instance
- [ ] Let's Encrypt certificate obtained
- [ ] Auto-renewal configured

### ✅ Docker Configuration

#### Local Development
- [ ] Docker and Docker Compose installed
- [ ] Images build successfully
- [ ] Containers start without errors
- [ ] Network connectivity between containers

#### Production
- [ ] Docker installed on EC2
- [ ] Images built and tagged correctly
- [ ] Containers running with proper restart policy
- [ ] Volume mounts configured correctly

## Post-Deployment Checklist

### ✅ Service Health Checks

#### Frontend
- [ ] Accessible at `https://nutrifocus.duckdns.org`
- [ ] No console errors in browser
- [ ] All pages load correctly
- [ ] Responsive design working

#### Backend
- [ ] Health check endpoint responding: `https://nutrifocus.duckdns.org/health`
- [ ] API endpoints accessible: `https://nutrifocus.duckdns.org/api`
- [ ] Swagger docs accessible: `https://nutrifocus.duckdns.org/docs`
- [ ] Database connection working

#### Database
- [ ] PostgreSQL container running
- [ ] Database migrations applied
- [ ] Connection pool working
- [ ] Data persistence confirmed

#### ML Service
- [ ] ML service container running
- [ ] API endpoints responding
- [ ] Model predictions working

### ✅ Authentication & Authorization

#### User Registration/Login
- [ ] User registration working
- [ ] Email verification working
- [ ] User login working
- [ ] Password reset working

#### Google OAuth
- [ ] Google login button visible
- [ ] OAuth flow completing successfully
- [ ] User data retrieved correctly
- [ ] Session management working

#### Role-Based Access
- [ ] User dashboard accessible
- [ ] Admin dashboard accessible (for admin users)
- [ ] Super admin dashboard accessible (for super admins)
- [ ] Unauthorized access blocked

### ✅ CORS & Cookie Configuration

#### CORS
- [ ] Frontend can make requests to backend
- [ ] Preflight requests working
- [ ] No CORS errors in browser console
- [ ] All HTTP methods allowed

#### Cookies
- [ ] Authentication cookies set correctly
- [ ] Cookies accessible across subdomains
- [ ] Secure flag set for HTTPS
- [ ] SameSite policy configured correctly

### ✅ Security Configuration

#### HTTPS
- [ ] All traffic redirected to HTTPS
- [ ] SSL certificate valid
- [ ] Security headers present
- [ ] HSTS configured

#### Rate Limiting
- [ ] API rate limiting working
- [ ] Login rate limiting working
- [ ] General rate limiting working

#### Input Validation
- [ ] All forms validated on frontend
- [ ] All API endpoints validated on backend
- [ ] SQL injection prevention working
- [ ] XSS protection working

### ✅ Performance & Monitoring

#### Performance
- [ ] Page load times acceptable
- [ ] API response times acceptable
- [ ] Database query performance good
- [ ] Image optimization working

#### Monitoring
- [ ] Log files being generated
- [ ] Health checks running
- [ ] Auto-restart working
- [ ] Backup system working

#### Error Handling
- [ ] 404 pages working
- [ ] 500 error pages working
- [ ] Error logging configured
- [ ] User-friendly error messages

### ✅ Functionality Testing

#### Core Features
- [ ] User registration and login
- [ ] Profile management
- [ ] Food logging
- [ ] Health metrics tracking
- [ ] Nutritional recommendations
- [ ] Blog system
- [ ] Feedback system
- [ ] Notifications

#### Admin Features
- [ ] User management
- [ ] Blog management
- [ ] Feedback management
- [ ] System monitoring

#### Super Admin Features
- [ ] Admin user management
- [ ] System configuration
- [ ] Advanced monitoring

## Troubleshooting Checklist

### If Frontend Won't Load
- [ ] Check Nginx configuration
- [ ] Verify SSL certificate
- [ ] Check frontend container logs
- [ ] Verify environment variables

### If Backend API Won't Respond
- [ ] Check backend container logs
- [ ] Verify database connection
- [ ] Check CORS configuration
- [ ] Verify API routes

### If Authentication Fails
- [ ] Check JWT configuration
- [ ] Verify cookie settings
- [ ] Check Google OAuth configuration
- [ ] Verify email service

### If Database Issues
- [ ] Check PostgreSQL container status
- [ ] Verify connection credentials
- [ ] Check volume mounts
- [ ] Verify migrations

### If SSL Issues
- [ ] Check certificate validity
- [ ] Verify domain configuration
- [ ] Check Nginx SSL configuration
- [ ] Verify Let's Encrypt setup

## Maintenance Checklist

### Daily
- [ ] Check application logs
- [ ] Monitor system resources
- [ ] Verify all services running

### Weekly
- [ ] Review error logs
- [ ] Check backup status
- [ ] Monitor performance metrics

### Monthly
- [ ] Update dependencies
- [ ] Review security logs
- [ ] Check SSL certificate renewal
- [ ] Performance optimization review

### Quarterly
- [ ] Security audit
- [ ] Backup restoration test
- [ ] Disaster recovery test
- [ ] Performance review

## Emergency Procedures

### Service Down
1. [ ] Check service status: `systemctl status nutrifocus.service`
2. [ ] Restart services: `systemctl restart nutrifocus.service`
3. [ ] Check logs for errors
4. [ ] Verify resource usage

### Database Issues
1. [ ] Check PostgreSQL container: `docker logs nutrifocus-postgres`
2. [ ] Restart database: `docker restart nutrifocus-postgres`
3. [ ] Check volume mounts
4. [ ] Restore from backup if needed

### SSL Certificate Issues
1. [ ] Check certificate status: `certbot certificates`
2. [ ] Renew certificate: `certbot renew`
3. [ ] Restart Nginx: `systemctl restart nginx`
4. [ ] Verify HTTPS access

### Security Breach
1. [ ] Rotate all secrets immediately
2. [ ] Check access logs
3. [ ] Review recent changes
4. [ ] Update security configurations

---

## Quick Commands Reference

```bash
# Check all services
docker ps -a

# View logs
docker-compose -f /opt/nutrifocus/docker-compose.prod.yml logs -f

# Restart services
sudo systemctl restart nutrifocus.service

# Check status
sudo systemctl status nutrifocus.service

# Backup data
sudo /opt/nutrifocus/backup.sh

# Monitor resources
docker stats

# Check SSL certificate
sudo certbot certificates
```

Use this checklist to ensure a successful deployment and maintain system reliability. 