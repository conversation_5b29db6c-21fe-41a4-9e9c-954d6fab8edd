#!/bin/bash

# Local Production Setup Script for NutriFocus
# This script sets up a local production-like environment

set -e

echo "🚀 Setting up local production environment for NutriFocus..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root (needed for hosts file modification)
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

# Create SSL directory
print_status "Creating SSL directory..."
mkdir -p ssl

# Generate self-signed SSL certificate for local testing
print_status "Generating self-signed SSL certificate..."
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout ssl/private.key \
    -out ssl/certificate.crt \
    -subj "/C=US/ST=State/L=City/O=NutriFocus/CN=nutrifocus.duckdns.org" \
    -addext "subjectAltName=DNS:nutrifocus.duckdns.org,DNS:localhost,IP:127.0.0.1"

print_success "SSL certificate generated successfully"

# Check if hosts file entry already exists
if grep -q "nutrifocus.duckdns.org" /etc/hosts; then
    print_warning "Hosts file entry for nutrifocus.duckdns.org already exists"
else
    print_status "Adding nutrifocus.duckdns.org to /etc/hosts..."
    echo "127.0.0.1 nutrifocus.duckdns.org" | sudo tee -a /etc/hosts
    print_success "Hosts file updated successfully"
fi

# Create environment files
print_status "Creating environment files..."

# Frontend environment
cat > ../Frontend_client-side/.env.local << EOF
NEXT_PUBLIC_API_URL=https://nutrifocus.duckdns.org
NEXT_PUBLIC_COOKIE_DOMAIN=nutrifocus.duckdns.org
NODE_ENV=production
EOF

# Backend environment
cat > ../Backend_server-side/.env.local << EOF
NODE_ENV=production
PORT=4000
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=admin
POSTGRES_DB=nutrifocus
FRONTEND_URL=https://nutrifocus.duckdns.org
JWT_SECRET=your-super-secret-jwt-key-for-local-testing
JWT_EXPIRES_IN=2h
JWT_ACCESS_TOKEN_SECRET=your-access-token-secret
JWT_ACCESS_TOKEN_EXPIRATION_TIME=2h
JWT_REFRESH_TOKEN_SECRET=your-refresh-token-secret
JWT_REFRESH_TOKEN_EXPIRATION_TIME=24h
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_VERIFICATION_SECRET=your-email-verification-secret
ML_API_URL=http://ml-service:8000
THROTTLE_TTL=60
THROTTLE_LIMIT=100
PASSWORD_RESET_SECRET=your-password-reset-secret
GOOGLE_AUTH_CLIENT_ID=your-google-client-id
GOOGLE_AUTH_CLIENT_SECRET=your-google-client-secret
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret
EOF

print_success "Environment files created successfully"

# Create startup script
cat > start-local-production.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting NutriFocus local production environment..."

# Stop any existing containers
docker-compose -f docker-compose.local.yml down

# Build and start services
docker-compose -f docker-compose.local.yml up --build -d

echo "✅ Services started successfully!"
echo ""
echo "🌐 Access your application at:"
echo "   Frontend: https://nutrifocus.duckdns.org"
echo "   Backend API: https://nutrifocus.duckdns.org/api"
echo "   Swagger Docs: https://nutrifocus.duckdns.org/docs"
echo ""
echo "📊 View logs with: docker-compose -f docker-compose.local.yml logs -f"
echo "🛑 Stop services with: docker-compose -f docker-compose.local.yml down"
EOF

chmod +x start-local-production.sh

# Create stop script
cat > stop-local-production.sh << 'EOF'
#!/bin/bash

echo "🛑 Stopping NutriFocus local production environment..."

docker-compose -f docker-compose.local.yml down

echo "✅ Services stopped successfully!"
EOF

chmod +x stop-local-production.sh

# Create cleanup script
cat > cleanup-local-production.sh << 'EOF'
#!/bin/bash

echo "🧹 Cleaning up NutriFocus local production environment..."

# Stop and remove containers
docker-compose -f docker-compose.local.yml down -v

# Remove images
docker rmi $(docker images -q nutrifocus-frontend nutrifocus-backend) 2>/dev/null || true

# Remove SSL certificates
rm -rf ssl/

# Remove environment files
rm -f ../Frontend_client-side/.env.local
rm -f ../Backend_server-side/.env.local

echo "✅ Cleanup completed successfully!"
echo ""
echo "⚠️  Note: You may want to manually remove the hosts file entry:"
echo "   sudo nano /etc/hosts"
echo "   Remove the line: 127.0.0.1 nutrifocus.duckdns.org"
EOF

chmod +x cleanup-local-production.sh

print_success "Local production setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Update the environment variables in .env.local files with your actual values"
echo "2. Run: ./start-local-production.sh"
echo "3. Access your application at https://nutrifocus.duckdns.org"
echo ""
echo "⚠️  Important notes:"
echo "- You'll need to accept the self-signed certificate in your browser"
echo "- Update your Google OAuth redirect URIs to include https://nutrifocus.duckdns.org"
echo "- Update your Cloudinary settings if needed"
echo ""
echo "🛠️  Available commands:"
echo "- ./start-local-production.sh  - Start the environment"
echo "- ./stop-local-production.sh   - Stop the environment"
echo "- ./cleanup-local-production.sh - Clean up everything" 