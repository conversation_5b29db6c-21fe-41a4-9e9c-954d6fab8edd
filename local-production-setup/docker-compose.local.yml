version: '3.8'

services:
  # # Nginx Reverse Proxy
  # nginx:
  #   image: nginx:alpine
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf:ro
  #     - ./ssl:/etc/nginx/ssl:ro
  #   depends_on:
  #     - frontend
  #     - backend
  #   restart: unless-stopped
  #   networks:
  #     - nutrifocus-network

  # Frontend Service
  frontend:
    build:
      context: ../Frontend_client-side
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://nutrifocus.duckdns.org
      - NEXT_PUBLIC_COOKIE_DOMAIN=nutrifocus.duckdns.org
    ports:
      - "3000:3000"
    restart: unless-stopped
    networks:
      - nutrifocus-network

  # Backend Service
  backend:
    build:
      context: ../Backend_server-side
      dockerfile: Dockerfile
    env_file:
      - ../Backend_server-side/.env
    ports:
      - "4000:4000"
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - nutrifocus-network

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - PORT=${PORT:-4000}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - nutrifocus-network

volumes:
  postgres_data:

networks:
  nutrifocus-network:
    driver: bridge 